﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

#pragma warning disable CA1814 // Prefer jagged arrays over multidimensional

namespace InternshipManagement.Migrations
{
    /// <inheritdoc />
    public partial class InitialStaticSeed : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "AppUser",
                columns: table => new
                {
                    code = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: false),
                    Role = table.Column<int>(type: "int", nullable: false),
                    passwordhash = table.Column<string>(type: "nvarchar(max)", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AppUser", x => new { x.code, x.Role });
                });

            migrationBuilder.CreateTable(
                name: "Khoa",
                columns: table => new
                {
                    makhoa = table.Column<string>(type: "char(10)", maxLength: 10, nullable: false),
                    tenkhoa = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    dienthoai = table.Column<string>(type: "varchar(20)", maxLength: 20, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Khoa", x => x.makhoa);
                });

            migrationBuilder.CreateTable(
                name: "GiangVien",
                columns: table => new
                {
                    magv = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    hotengv = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    luong = table.Column<decimal>(type: "decimal(5,2)", precision: 5, scale: 2, nullable: true),
                    makhoa = table.Column<string>(type: "char(10)", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_GiangVien", x => x.magv);
                    table.ForeignKey(
                        name: "FK_GiangVien_Khoa_makhoa",
                        column: x => x.makhoa,
                        principalTable: "Khoa",
                        principalColumn: "makhoa",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "SinhVien",
                columns: table => new
                {
                    masv = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    hotensv = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    makhoa = table.Column<string>(type: "char(10)", maxLength: 10, nullable: false),
                    namsinh = table.Column<int>(type: "int", nullable: true),
                    quequan = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SinhVien", x => x.masv);
                    table.ForeignKey(
                        name: "FK_SinhVien_Khoa_makhoa",
                        column: x => x.makhoa,
                        principalTable: "Khoa",
                        principalColumn: "makhoa",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "DeTai",
                columns: table => new
                {
                    madt = table.Column<string>(type: "char(10)", maxLength: 10, nullable: false),
                    tendt = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true),
                    kinhphi = table.Column<int>(type: "int", nullable: true),
                    noithucTap = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true),
                    magv = table.Column<int>(type: "int", nullable: false),
                    hocky = table.Column<byte>(type: "tinyint", nullable: false),
                    namhoc = table.Column<string>(type: "varchar(9)", maxLength: 9, nullable: false),
                    soluongtoida = table.Column<int>(type: "int", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DeTai", x => x.madt);
                    table.ForeignKey(
                        name: "FK_DeTai_GiangVien_magv",
                        column: x => x.magv,
                        principalTable: "GiangVien",
                        principalColumn: "magv",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "HuongDan",
                columns: table => new
                {
                    masv = table.Column<int>(type: "int", nullable: false),
                    madt = table.Column<string>(type: "char(10)", nullable: false),
                    magv = table.Column<int>(type: "int", nullable: false),
                    ketqua = table.Column<decimal>(type: "decimal(5,2)", precision: 5, scale: 2, nullable: true),
                    trangthai = table.Column<byte>(type: "tinyint", nullable: false),
                    ngaydangky = table.Column<DateTime>(type: "datetime2", nullable: false),
                    ngaychapnhan = table.Column<DateTime>(type: "datetime2", nullable: true),
                    ghichu = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_HuongDan", x => new { x.masv, x.madt });
                    table.ForeignKey(
                        name: "FK_HuongDan_DeTai_madt",
                        column: x => x.madt,
                        principalTable: "DeTai",
                        principalColumn: "madt",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_HuongDan_GiangVien_magv",
                        column: x => x.magv,
                        principalTable: "GiangVien",
                        principalColumn: "magv",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_HuongDan_SinhVien_masv",
                        column: x => x.masv,
                        principalTable: "SinhVien",
                        principalColumn: "masv",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.InsertData(
                table: "Khoa",
                columns: new[] { "makhoa", "dienthoai", "tenkhoa" },
                values: new object[,]
                {
                    { "CNCK", "0901111111", "Khoa Công nghệ Cơ khí" },
                    { "CNDIEN", "0901111113", "Khoa Công nghệ Điện" },
                    { "CNDL", "0901111115", "Khoa Công nghệ Động lực" },
                    { "CNDT", "0901111114", "Khoa Công nghệ Điện tử" },
                    { "CNHH", "0901111118", "Khoa Công nghệ Hóa học" },
                    { "CNMT", "0901111117", "Khoa Công nghệ May – Thời trang" },
                    { "CNNL", "0901111116", "Khoa Công nghệ Nhiệt – Lạnh" },
                    { "CNSH_TP", "0901111127", "Viện Công nghệ Sinh học và Thực phẩm" },
                    { "CNTT", "0901111112", "Khoa Công nghệ Thông tin" },
                    { "DTQT", "0901111126", "Viện Đào tạo quốc tế và Sau đại học" },
                    { "KHCB", "0901111119", "Khoa Khoa học Cơ bản" },
                    { "KHCNMT", "0901111128", "Viện Khoa học Công nghệ và Quản lý Môi trường" },
                    { "KTXD", "0901111124", "Khoa Kỹ thuật Xây dựng" },
                    { "LUAT", "0901111120", "Khoa Luật và Khoa học Chính trị" },
                    { "NN", "0901111121", "Khoa Ngoại ngữ" },
                    { "QTKD", "0901111122", "Khoa Quản trị Kinh doanh" },
                    { "TCKT", "0901111125", "Viện Tài chính – Kế toán" },
                    { "TMDL", "0901111123", "Khoa Thương mại – Du lịch" }
                });

            migrationBuilder.InsertData(
                table: "GiangVien",
                columns: new[] { "magv", "hotengv", "luong", "makhoa" },
                values: new object[,]
                {
                    { 1, "Nguyễn Thị Hoàng Khanh", 22.00m, "CNTT" },
                    { 2, "Trần Văn Minh", 25.50m, "CNTT" },
                    { 3, "Lê Thị Mai Phương", 24.20m, "CNTT" },
                    { 4, "Phạm Quốc Dũng", 28.00m, "CNTT" },
                    { 5, "Đỗ Hồng Ngọc", 23.75m, "CNTT" },
                    { 6, "Nguyễn Văn An", 26.00m, "CNCK" },
                    { 7, "Trần Thị Bích Ngọc", 21.50m, "CNCK" },
                    { 8, "Phan Văn Hùng", 27.30m, "CNCK" },
                    { 9, "Vũ Thị Lan", 22.80m, "CNCK" },
                    { 10, "Lê Quang Hiếu", 29.00m, "CNCK" },
                    { 11, "Nguyễn Thị Thu Hằng", 20.50m, "CNDIEN" },
                    { 12, "Trần Văn Hòa", 24.90m, "CNDIEN" },
                    { 13, "Đặng Thị Hương", 23.60m, "CNDIEN" },
                    { 14, "Hoàng Văn Phúc", 30.00m, "CNDIEN" },
                    { 15, "Phạm Thị Thanh Thủy", 22.40m, "CNDIEN" },
                    { 16, "Nguyễn Văn Thắng", 24.50m, "CNDT" },
                    { 17, "Trần Thị Mỹ Linh", 23.80m, "CNDT" },
                    { 18, "Phạm Văn Huy", 26.00m, "CNDT" },
                    { 19, "Đỗ Thị Cẩm Tú", 22.70m, "CNDT" },
                    { 20, "Lê Minh Tuấn", 28.20m, "CNDT" },
                    { 21, "Nguyễn Hoàng Anh", 25.30m, "CNDL" },
                    { 22, "Trần Thị Bảo Yến", 21.90m, "CNDL" },
                    { 23, "Phan Văn Lộc", 27.40m, "CNDL" },
                    { 24, "Đinh Thị Hồng Nhung", 23.50m, "CNDL" },
                    { 25, "Vũ Đức Long", 29.10m, "CNDL" },
                    { 26, "Nguyễn Thị Thu Trang", 22.40m, "CNNL" },
                    { 27, "Trần Văn Lâm", 24.80m, "CNNL" },
                    { 28, "Phạm Thị Bích Thảo", 23.20m, "CNNL" },
                    { 29, "Lê Văn Sơn", 26.70m, "CNNL" },
                    { 30, "Hoàng Thị Ngọc Mai", 28.50m, "CNNL" },
                    { 31, "Nguyễn Thị Thanh Hương", 23.60m, "CNMT" },
                    { 32, "Trần Văn Khánh", 25.10m, "CNMT" },
                    { 33, "Phạm Thị Mỹ Dung", 21.80m, "CNMT" },
                    { 34, "Đỗ Văn Bình", 27.40m, "CNMT" },
                    { 35, "Lê Thị Kim Ngân", 22.90m, "CNMT" },
                    { 36, "Nguyễn Văn Toàn", 26.30m, "CNHH" },
                    { 37, "Trần Thị Minh Châu", 23.20m, "CNHH" },
                    { 38, "Phan Văn Khôi", 28.00m, "CNHH" },
                    { 39, "Đinh Thị Hòa", 22.40m, "CNHH" },
                    { 40, "Hoàng Văn Cường", 24.70m, "CNHH" },
                    { 41, "Nguyễn Thị Mai Lan", 21.50m, "KHCB" },
                    { 42, "Trần Văn Hải", 25.80m, "KHCB" },
                    { 43, "Phạm Thị Như Quỳnh", 23.40m, "KHCB" },
                    { 44, "Vũ Văn Thái", 27.10m, "KHCB" },
                    { 45, "Lê Thị Thanh Trúc", 22.80m, "KHCB" },
                    { 46, "Nguyễn Văn Lợi", 23.90m, "LUAT" },
                    { 47, "Trần Thị Mai Hoa", 22.70m, "LUAT" },
                    { 48, "Phạm Văn Khánh", 25.60m, "LUAT" },
                    { 49, "Đỗ Thị Bích Phượng", 24.20m, "LUAT" },
                    { 50, "Hoàng Văn Dũng", 27.00m, "LUAT" },
                    { 51, "Nguyễn Thị Mỹ Linh", 22.50m, "NN" },
                    { 52, "Trần Văn Hùng", 26.40m, "NN" },
                    { 53, "Phạm Thị Hồng Nhung", 23.80m, "NN" },
                    { 54, "Đinh Văn Nam", 25.20m, "NN" },
                    { 55, "Lê Thị Thu Hà", 27.10m, "NN" },
                    { 56, "Nguyễn Văn Phát", 24.30m, "QTKD" },
                    { 57, "Trần Thị Thanh Tâm", 22.80m, "QTKD" },
                    { 58, "Phan Văn Quang", 26.70m, "QTKD" },
                    { 59, "Đỗ Thị Lan Anh", 23.90m, "QTKD" },
                    { 60, "Vũ Đức Thịnh", 28.20m, "QTKD" },
                    { 61, "Nguyễn Thị Kim Yến", 22.40m, "TMDL" },
                    { 62, "Trần Văn Quý", 25.50m, "TMDL" },
                    { 63, "Phạm Thị Bảo Trân", 23.10m, "TMDL" },
                    { 64, "Lê Văn Hòa", 27.60m, "TMDL" },
                    { 65, "Đặng Thị Minh Ngọc", 24.80m, "TMDL" },
                    { 66, "Nguyễn Văn Thọ", 26.90m, "KTXD" },
                    { 67, "Trần Thị Thảo", 22.60m, "KTXD" },
                    { 68, "Phạm Văn Thành", 28.40m, "KTXD" },
                    { 69, "Đỗ Thị Thanh Loan", 23.50m, "KTXD" },
                    { 70, "Hoàng Văn Lâm", 25.70m, "KTXD" },
                    { 71, "Nguyễn Thị Thanh Vân", 23.40m, "TCKT" },
                    { 72, "Trần Văn Hậu", 25.60m, "TCKT" },
                    { 73, "Phạm Thị Ngọc Bích", 22.80m, "TCKT" },
                    { 74, "Đỗ Văn Khải", 27.10m, "TCKT" },
                    { 75, "Lê Thị Mỹ Hạnh", 24.50m, "TCKT" },
                    { 76, "Nguyễn Văn Hưng", 26.20m, "DTQT" },
                    { 77, "Trần Thị Diễm My", 22.90m, "DTQT" },
                    { 78, "Phan Văn Quý", 25.70m, "DTQT" },
                    { 79, "Đinh Thị Thảo", 23.60m, "DTQT" },
                    { 80, "Hoàng Văn Tiến", 28.30m, "DTQT" },
                    { 81, "Nguyễn Thị Hồng Nhung", 24.20m, "CNSH_TP" },
                    { 82, "Trần Văn Duy", 26.40m, "CNSH_TP" },
                    { 83, "Phạm Thị Mai Anh", 22.50m, "CNSH_TP" },
                    { 84, "Đỗ Văn Hiếu", 27.80m, "CNSH_TP" },
                    { 85, "Lê Thị Bảo Trâm", 23.90m, "CNSH_TP" },
                    { 86, "Nguyễn Văn Long", 25.10m, "KHCNMT" },
                    { 87, "Trần Thị Ngọc Hân", 23.70m, "KHCNMT" },
                    { 88, "Phạm Văn Đức", 26.80m, "KHCNMT" },
                    { 89, "Đinh Thị Thanh Xuân", 22.60m, "KHCNMT" },
                    { 90, "Hoàng Văn Lợi", 28.00m, "KHCNMT" }
                });

            migrationBuilder.InsertData(
                table: "SinhVien",
                columns: new[] { "masv", "hotensv", "makhoa", "namsinh", "quequan" },
                values: new object[,]
                {
                    { 1001, "Lê Hoàng Khang", "CNTT", 2002, "TP.HCM" },
                    { 1002, "Trần Văn Minh", "CNTT", 2001, "Đà Nẵng" },
                    { 1003, "Phạm Thị Hồng Nhung", "CNTT", 2003, "Nghệ An" },
                    { 1004, "Đinh Văn Lâm", "CNTT", 2002, "Quảng Ninh" },
                    { 1005, "Hoàng Thị Thu Hà", "CNTT", 2001, "Huế" },
                    { 1006, "Nguyễn Văn Hùng", "CNCK", 2002, "Hà Nội" },
                    { 1007, "Trần Thị Mai", "CNCK", 2001, "Nam Định" },
                    { 1008, "Phạm Văn Nam", "CNCK", 2003, "Hải Phòng" },
                    { 1009, "Đỗ Thị Hoa", "CNCK", 2002, "Thái Bình" },
                    { 1010, "Lê Quang Vinh", "CNCK", 2001, "Thanh Hóa" },
                    { 1011, "Nguyễn Văn Phúc", "CNDIEN", 2002, "Bắc Giang" },
                    { 1012, "Trần Thị Bảo Trâm", "CNDIEN", 2003, "Phú Thọ" },
                    { 1013, "Phạm Văn Dũng", "CNDIEN", 2001, "Vĩnh Phúc" },
                    { 1014, "Đỗ Thị Hạnh", "CNDIEN", 2002, "Hưng Yên" },
                    { 1015, "Lê Minh Tuấn", "CNDIEN", 2001, "Ninh Bình" },
                    { 1016, "Nguyễn Thị Thanh Thảo", "CNDT", 2003, "Đắk Lắk" },
                    { 1017, "Trần Văn Quân", "CNDT", 2001, "Khánh Hòa" },
                    { 1018, "Phạm Thị Mỹ Duyên", "CNDT", 2002, "Cần Thơ" },
                    { 1019, "Đỗ Văn Thành", "CNDT", 2003, "Bình Định" },
                    { 1020, "Lê Thị Bích Ngọc", "CNDT", 2002, "Tiền Giang" },
                    { 1021, "Nguyễn Văn Khải", "CNDL", 2002, "Hà Nội" },
                    { 1022, "Trần Thị Minh Thư", "CNDL", 2001, "Hải Dương" },
                    { 1023, "Phạm Văn Lâm", "CNDL", 2003, "Quảng Nam" },
                    { 1024, "Đỗ Thị Kim Oanh", "CNDL", 2002, "Thanh Hóa" },
                    { 1025, "Lê Quang Huy", "CNDL", 2001, "Nghệ An" },
                    { 1026, "Nguyễn Thị Thu Hiền", "CNNL", 2002, "TP.HCM" },
                    { 1027, "Trần Văn Toàn", "CNNL", 2001, "Đà Nẵng" },
                    { 1028, "Phạm Thị Hồng Như", "CNNL", 2003, "Bình Định" },
                    { 1029, "Đỗ Văn Tuấn", "CNNL", 2002, "Gia Lai" },
                    { 1030, "Lê Thị Mỹ Linh", "CNNL", 2001, "Cà Mau" },
                    { 1031, "Nguyễn Thị Hồng Hạnh", "CNMT", 2003, "Hải Phòng" },
                    { 1032, "Trần Văn Đức", "CNMT", 2002, "Quảng Ninh" },
                    { 1033, "Phạm Thị Bảo Ngọc", "CNMT", 2001, "Bình Thuận" },
                    { 1034, "Đinh Văn Long", "CNMT", 2002, "Huế" },
                    { 1035, "Hoàng Thị Ngọc Mai", "CNMT", 2001, "Sóc Trăng" },
                    { 1036, "Nguyễn Thị Thu Trang", "CNHH", 2002, "Hà Nội" },
                    { 1037, "Trần Văn Khánh", "CNHH", 2001, "Hải Phòng" },
                    { 1038, "Phạm Thị Kim Oanh", "CNHH", 2003, "Nam Định" },
                    { 1039, "Đỗ Văn Thắng", "CNHH", 2002, "Thanh Hóa" },
                    { 1040, "Lê Thị Ngọc Hân", "CNHH", 2001, "Nghệ An" },
                    { 1041, "Nguyễn Văn Toàn", "KHCB", 2002, "Thái Bình" },
                    { 1042, "Trần Thị Bích Ngọc", "KHCB", 2001, "Bắc Ninh" },
                    { 1043, "Phạm Văn Khôi", "KHCB", 2003, "Phú Thọ" },
                    { 1044, "Đinh Thị Hồng Nhung", "KHCB", 2002, "Hà Tĩnh" },
                    { 1045, "Hoàng Văn Trường", "KHCB", 2001, "Quảng Bình" },
                    { 1046, "Nguyễn Thị Mỹ Hạnh", "LUAT", 2002, "TP.HCM" },
                    { 1047, "Trần Văn Lực", "LUAT", 2003, "Đồng Nai" },
                    { 1048, "Phạm Thị Ngọc Ánh", "LUAT", 2001, "Bình Dương" },
                    { 1049, "Đỗ Văn Thành", "LUAT", 2002, "Long An" },
                    { 1050, "Lê Thị Thanh Thủy", "LUAT", 2001, "Tây Ninh" },
                    { 1051, "Nguyễn Văn Hải", "NN", 2002, "Hải Phòng" },
                    { 1052, "Trần Thị Ngọc Mai", "NN", 2001, "Quảng Ninh" },
                    { 1053, "Phạm Văn Hòa", "NN", 2003, "Nghệ An" },
                    { 1054, "Đỗ Thị Kim Cúc", "NN", 2002, "Hà Nội" },
                    { 1055, "Lê Quang Bình", "NN", 2001, "Nam Định" },
                    { 1056, "Nguyễn Thị Bích Ngọc", "QTKD", 2002, "TP.HCM" },
                    { 1057, "Trần Văn Hoàng", "QTKD", 2001, "Đà Nẵng" },
                    { 1058, "Phạm Thị Yến Nhi", "QTKD", 2003, "Bình Định" },
                    { 1059, "Đinh Văn Quý", "QTKD", 2002, "Quảng Nam" },
                    { 1060, "Hoàng Thị Diễm My", "QTKD", 2001, "Huế" },
                    { 1061, "Nguyễn Văn Hưng", "TMDL", 2002, "Khánh Hòa" },
                    { 1062, "Trần Thị Mỹ Linh", "TMDL", 2001, "Cần Thơ" },
                    { 1063, "Phạm Văn Phước", "TMDL", 2003, "Tiền Giang" },
                    { 1064, "Đỗ Thị Như Ý", "TMDL", 2002, "An Giang" },
                    { 1065, "Lê Văn Dũng", "TMDL", 2001, "Đồng Tháp" },
                    { 1066, "Nguyễn Văn Phát", "KTXD", 2002, "Hà Nội" },
                    { 1067, "Trần Thị Thu Hằng", "KTXD", 2001, "Hải Phòng" },
                    { 1068, "Phạm Văn Thịnh", "KTXD", 2003, "Thanh Hóa" },
                    { 1069, "Đỗ Thị Mỹ Linh", "KTXD", 2002, "Nghệ An" },
                    { 1070, "Lê Quang Trọng", "KTXD", 2001, "Bắc Giang" },
                    { 1071, "Nguyễn Thị Thu Hà", "TCKT", 2002, "TP.HCM" },
                    { 1072, "Trần Văn Sơn", "TCKT", 2001, "Đồng Nai" },
                    { 1073, "Phạm Thị Ngọc Trâm", "TCKT", 2003, "Bình Dương" },
                    { 1074, "Đỗ Văn Hòa", "TCKT", 2002, "Long An" },
                    { 1075, "Lê Thị Mỹ Dung", "TCKT", 2001, "Tây Ninh" },
                    { 1076, "Nguyễn Văn Hậu", "DTQT", 2003, "Khánh Hòa" },
                    { 1077, "Trần Thị Minh Ngọc", "DTQT", 2002, "Cần Thơ" },
                    { 1078, "Phạm Văn Đạt", "DTQT", 2001, "Bình Định" },
                    { 1079, "Đỗ Thị Bích Thủy", "DTQT", 2002, "An Giang" },
                    { 1080, "Lê Văn Hồng", "DTQT", 2001, "Tiền Giang" },
                    { 1081, "Nguyễn Thị Hồng Nhung", "CNSH_TP", 2002, "Hà Nội" },
                    { 1082, "Trần Văn Thịnh", "CNSH_TP", 2001, "Hải Phòng" },
                    { 1083, "Phạm Thị Minh Châu", "CNSH_TP", 2003, "Nghệ An" },
                    { 1084, "Đỗ Văn Hùng", "CNSH_TP", 2002, "Thanh Hóa" },
                    { 1085, "Lê Thị Bảo Ngọc", "CNSH_TP", 2001, "Huế" },
                    { 1086, "Nguyễn Văn Dũng", "KHCNMT", 2002, "TP.HCM" },
                    { 1087, "Trần Thị Cẩm Tú", "KHCNMT", 2003, "Đà Nẵng" },
                    { 1088, "Phạm Văn Lộc", "KHCNMT", 2001, "Bình Định" },
                    { 1089, "Đinh Thị Thu Hằng", "KHCNMT", 2002, "Khánh Hòa" },
                    { 1090, "Hoàng Văn Tùng", "KHCNMT", 2001, "Cần Thơ" }
                });

            migrationBuilder.InsertData(
                table: "DeTai",
                columns: new[] { "madt", "hocky", "kinhphi", "magv", "namhoc", "noithucTap", "soluongtoida", "tendt" },
                values: new object[,]
                {
                    { "DT001", (byte)1, 10, 1, "2023-2024", "Công ty FPT Software", 2, "Hệ thống quản lý sinh viên" },
                    { "DT002", (byte)1, 12, 1, "2024-2025", "Công ty VNPT", 3, "Ứng dụng đặt lịch khám bệnh" },
                    { "DT003", (byte)2, 15, 1, "2022-2023", "Công ty MISA", 2, "Website thương mại điện tử" },
                    { "DT004", (byte)2, 14, 1, "2023-2024", "Công ty TMA Solutions", 3, "Ứng dụng chat realtime" },
                    { "DT005", (byte)1, 11, 1, "2021-2022", "Công ty Harvey Nash", 4, "Hệ thống quản lý thư viện" },
                    { "DT006", (byte)1, 16, 2, "2024-2025", "Công ty Viettel", 3, "Ứng dụng học trực tuyến" },
                    { "DT007", (byte)2, 18, 2, "2022-2023", "Công ty FPT IS", 2, "Phần mềm quản lý khách sạn" },
                    { "DT008", (byte)1, 14, 2, "2023-2024", "Công ty NashTech", 3, "Ứng dụng thương mại điện tử di động" },
                    { "DT009", (byte)2, 20, 2, "2024-2025", "Công ty Zalo", 4, "AI chatbot hỗ trợ khách hàng" },
                    { "DT010", (byte)1, 12, 2, "2021-2022", "Công ty VNG", 2, "Hệ thống quản lý tài chính cá nhân" },
                    { "DT011", (byte)2, 15, 3, "2022-2023", "Công ty Axon Active", 3, "Ứng dụng quản lý khóa học" },
                    { "DT012", (byte)1, 19, 3, "2023-2024", "Công ty VNPT", 2, "Hệ thống điểm danh khuôn mặt" },
                    { "DT013", (byte)2, 17, 3, "2024-2025", "Công ty Tiki", 3, "Ứng dụng thương mại điện tử đa nền tảng" },
                    { "DT014", (byte)1, 20, 3, "2021-2022", "Công ty CMC", 4, "Hệ thống phân tích dữ liệu lớn" },
                    { "DT015", (byte)2, 13, 3, "2023-2024", "Công ty Momo", 2, "Ứng dụng ngân hàng số" },
                    { "DT016", (byte)1, 18, 4, "2024-2025", "Công ty FPT Software", 3, "Ứng dụng quản lý nhân sự" },
                    { "DT017", (byte)2, 14, 4, "2022-2023", "Công ty Viettel", 4, "Hệ thống thương mại điện tử B2B" },
                    { "DT018", (byte)1, 12, 4, "2021-2022", "Công ty Traveloka", 2, "Ứng dụng du lịch thông minh" },
                    { "DT019", (byte)2, 19, 4, "2023-2024", "Công ty VNPT Technology", 3, "Hệ thống IoT giám sát môi trường" },
                    { "DT020", (byte)1, 16, 4, "2024-2025", "Công ty ZaloPay", 2, "Ứng dụng fintech quản lý chi tiêu" },
                    { "DT021", (byte)2, 11, 5, "2022-2023", "Công ty Topica", 3, "Ứng dụng học tiếng Anh" },
                    { "DT022", (byte)1, 18, 5, "2023-2024", "Công ty Sendo", 4, "Hệ thống thương mại điện tử cho SME" },
                    { "DT023", (byte)2, 13, 5, "2021-2022", "Công ty GetFit", 2, "Ứng dụng quản lý phòng gym" },
                    { "DT024", (byte)1, 20, 5, "2024-2025", "Công ty Doctor Anywhere", 3, "Hệ thống hỗ trợ tư vấn y tế" },
                    { "DT025", (byte)2, 15, 5, "2023-2024", "Công ty MB Bank", 2, "Ứng dụng ngân hàng trực tuyến" },
                    { "DT026", (byte)1, 14, 6, "2023-2024", "Công ty Datalogic Việt Nam", 3, "Thiết kế khuôn ép nhựa bằng CAD/CAM" },
                    { "DT027", (byte)2, 18, 6, "2022-2023", "Bosch Việt Nam", 2, "Tối ưu hóa quy trình gia công CNC 5 trục" },
                    { "DT028", (byte)1, 12, 6, "2021-2022", "Schneider Electric", 3, "Mô phỏng FEA cho chi tiết cơ khí mỏng" },
                    { "DT029", (byte)2, 16, 6, "2024-2025", "Thaco Auto", 4, "Thiết kế jig gá thông minh cho dây chuyền lắp ráp" },
                    { "DT030", (byte)1, 10, 6, "2023-2024", "Nidec Việt Nam", 2, "Ứng dụng Lean Manufacturing trong xưởng tiện" },
                    { "DT031", (byte)2, 19, 7, "2022-2023", "FPT Robotics", 3, "Thiết kế robot SCARA gắp sản phẩm" },
                    { "DT032", (byte)1, 13, 7, "2024-2025", "Công ty Vina CNC", 2, "Quy hoạch bảo trì dự phòng cho máy CNC" },
                    { "DT033", (byte)1, 11, 7, "2021-2022", "Nhựa Duy Tân", 3, "Thiết kế truyền động đai cho băng tải công nghiệp" },
                    { "DT034", (byte)2, 20, 7, "2023-2024", "VinFast", 4, "Ứng dụng IoT giám sát rung động trục chính" },
                    { "DT035", (byte)2, 15, 7, "2024-2025", "Cơ khí Hòa Phát", 2, "Thiết kế hệ thống bôi trơn tập trung" },
                    { "DT036", (byte)1, 12, 8, "2022-2023", "Yamazaki Mazak VN", 3, "Tối ưu dao phay ngón cho hợp kim nhôm 6061" },
                    { "DT037", (byte)2, 17, 8, "2023-2024", "Saigon Precision", 3, "Thiết kế mô-đun cấp phôi tự động" },
                    { "DT038", (byte)1, 16, 8, "2021-2022", "PTSC M&C", 2, "Phân tích mỏi chi tiết trục bằng ANSYS" },
                    { "DT039", (byte)2, 14, 8, "2023-2024", "Sunjin Vina", 4, "Thiết kế cơ cấu cam cho máy dập" },
                    { "DT040", (byte)2, 18, 8, "2023-2024", "Mekamic", 3, "Gia công tiên tiến bằng tia nước áp lực cao" },
                    { "DT041", (byte)1, 20, 9, "2024-2025", "Workshop IUH Racing", 4, "Thiết kế khung xe đua công thức sinh viên" },
                    { "DT042", (byte)2, 13, 9, "2020-2021", "Thép Pomina", 2, "Tối ưu hàn MIG cho thép tấm mỏng" },
                    { "DT043", (byte)1, 15, 9, "2019-2020", "Cơ khí Sài Gòn", 3, "Thiết kế hệ thống nâng hạ thủy lực" },
                    { "DT044", (byte)2, 19, 9, "2023-2024", "R&D Ô tô Trường Hải", 3, "Phân tích CFD luồng khí qua két nước" },
                    { "DT045", (byte)1, 12, 9, "2023-2024", "Công ty Ống Thép Hòa Phát", 2, "Thiết kế máy uốn ống cỡ nhỏ" },
                    { "DT046", (byte)1, 11, 10, "2023-2024", "ABB Việt Nam", 2, "Ứng dụng PLC điều khiển máy dập cơ" },
                    { "DT047", (byte)2, 18, 10, "2023-2024", "Foster Electric", 4, "Thiết kế dây chuyền lắp ráp bán tự động" },
                    { "DT048", (byte)2, 16, 10, "2023-2024", "Rorze Robotics", 3, "Giảm rung cho trục quay tốc độ cao" },
                    { "DT049", (byte)1, 13, 10, "2023-2024", "SumiRiko AVS", 2, "Thiết kế cơ cấu cấp liệu vít tải" },
                    { "DT050", (byte)2, 17, 10, "2023-2024", "Daihatsu VN", 3, "Quản lý chất lượng theo Six Sigma cho xưởng tiện" },
                    { "DT051", (byte)1, 12, 11, "2024-2025", "Công ty Điện Quang", 2, "Thiết kế mạch điều khiển LED thông minh" },
                    { "DT052", (byte)2, 18, 11, "2023-2024", "EVN SPC", 3, "Hệ thống giám sát điện năng bằng IoT" },
                    { "DT053", (byte)1, 14, 11, "2023-2024", "Công ty Điện tử Samco", 2, "Thiết kế nguồn chuyển mạch công suất nhỏ" },
                    { "DT054", (byte)2, 11, 11, "2023-2024", "Trung tâm IoT Lab", 3, "Ứng dụng Arduino điều khiển thiết bị điện" },
                    { "DT055", (byte)1, 15, 11, "2023-2024", "SolarBK", 4, "Thiết kế bộ sạc pin năng lượng mặt trời" },
                    { "DT056", (byte)1, 13, 12, "2023-2024", "Công ty TMA IoT", 2, "Mạch cảm biến nhiệt độ và độ ẩm" },
                    { "DT057", (byte)2, 17, 12, "2023-2024", "SHTP Labs", 3, "Hệ thống đèn đường thông minh" },
                    { "DT058", (byte)1, 19, 12, "2023-2024", "Viettel R&D", 4, "Thiết kế mạch RF cho IoT" },
                    { "DT059", (byte)2, 14, 12, "2023-2024", "Công ty AgriTech", 2, "Ứng dụng ESP32 trong nông nghiệp" },
                    { "DT060", (byte)1, 16, 12, "2023-2024", "Công ty Savis", 3, "Thiết kế hệ thống báo cháy tự động" },
                    { "DT061", (byte)1, 20, 13, "2024-2025", "Công ty Điện mặt trời TTC", 4, "Mạch nghịch lưu cho năng lượng tái tạo" },
                    { "DT062", (byte)2, 15, 13, "2023-2024", "APC by Schneider", 2, "Thiết kế hệ thống UPS cỡ nhỏ" },
                    { "DT063", (byte)1, 11, 13, "2023-2024", "FPT Robotics", 3, "Điều khiển động cơ DC bằng PWM" },
                    { "DT064", (byte)2, 18, 13, "2023-2024", "Samsung R&D VN", 3, "Thiết kế mạch sạc nhanh USB Type-C" },
                    { "DT065", (byte)1, 19, 13, "2023-2024", "Synopsys VN", 2, "Ứng dụng FPGA trong xử lý tín hiệu" },
                    { "DT066", (byte)2, 17, 14, "2023-2024", "Renesas VN", 3, "Thiết kế mạch ADC/DAC tốc độ cao" },
                    { "DT067", (byte)1, 12, 14, "2023-2024", "Công ty Zalo AI", 2, "Điều khiển thiết bị bằng giọng nói" },
                    { "DT068", (byte)1, 10, 14, "2023-2024", "Maker Lab HCM", 3, "Mạch cảm biến hồng ngoại PIR" },
                    { "DT069", (byte)2, 20, 14, "2023-2024", "VinAI", 4, "Ứng dụng AI nhận diện khuôn mặt" },
                    { "DT070", (byte)1, 14, 14, "2023-2024", "Texas Instruments VN", 2, "Thiết kế mạch nguồn DC-DC hiệu suất cao" },
                    { "DT071", (byte)2, 13, 15, "2020-2021", "Công ty Điện lực HCMC", 3, "Thiết kế hệ thống đo điện trở cách điện" },
                    { "DT072", (byte)1, 15, 15, "2023-2024", "NI Vietnam", 2, "Ứng dụng LabVIEW giám sát cảm biến" },
                    { "DT073", (byte)2, 16, 15, "2023-2024", "Philips Lighting VN", 3, "Thiết kế hệ thống điều khiển chiếu sáng" },
                    { "DT074", (byte)1, 18, 15, "2023-2024", "BKAV SmartHome", 4, "Ứng dụng Zigbee trong nhà thông minh" },
                    { "DT075", (byte)2, 19, 15, "2023-2024", "Công ty Viettel Networks", 2, "Thiết kế bộ khuếch đại công suất RF" },
                    { "DT076", (byte)1, 12, 16, "2023-2024", "Công ty Điện tử Việt Nhật", 2, "Thiết kế mạch khuếch đại âm thanh công suất" },
                    { "DT077", (byte)2, 18, 16, "2023-2024", "VNPT Technology", 3, "Ứng dụng IoT trong hệ thống nhà thông minh" },
                    { "DT078", (byte)1, 15, 16, "2023-2024", "Công ty Điện tử Samco", 2, "Thiết kế mạch dao động tần số cao" },
                    { "DT079", (byte)2, 11, 16, "2023-2024", "BKAV SmartHome", 3, "Xây dựng hệ thống báo trộm không dây" },
                    { "DT080", (byte)1, 20, 16, "2023-2024", "Viettel R&D", 4, "Thiết kế bộ khuếch đại công suất RF" },
                    { "DT081", (byte)1, 14, 17, "2019-2020", "Đài Tiếng nói Việt Nam", 2, "Thiết kế mạch thu phát sóng FM" },
                    { "DT082", (byte)2, 12, 17, "2023-2024", "Maker Innovation Lab", 3, "Ứng dụng cảm biến siêu âm đo khoảng cách" },
                    { "DT083", (byte)1, 17, 17, "2023-2024", "Synopsys Việt Nam", 2, "Thiết kế mạch lọc số FIR" },
                    { "DT084", (byte)2, 16, 17, "2023-2024", "EVN HCMC", 4, "Hệ thống giám sát điện áp qua Internet" },
                    { "DT085", (byte)1, 19, 17, "2023-2024", "Texas Instruments VN", 3, "Thiết kế bộ chuyển đổi ADC 12-bit" },
                    { "DT086", (byte)2, 18, 18, "2023-2024", "Công ty TMA Solutions", 2, "Ứng dụng Raspberry Pi trong IoT" },
                    { "DT087", (byte)1, 11, 18, "2023-2024", "Công ty Điện Quang", 3, "Thiết kế mạch cảm biến ánh sáng" },
                    { "DT088", (byte)2, 13, 18, "2023-2024", "VNPT SmartHome", 2, "Hệ thống khóa cửa thông minh RFID" },
                    { "DT089", (byte)1, 16, 18, "2023-2024", "Philips VN", 4, "Thiết kế mạch công suất cho đèn LED" },
                    { "DT090", (byte)2, 20, 18, "2023-2024", "Hitachi VN", 3, "Ứng dụng PLC trong điều khiển thang máy" },
                    { "DT091", (byte)1, 19, 19, "2024-2025", "Ericsson VN", 4, "Thiết kế anten vi dải cho 5G" },
                    { "DT092", (byte)2, 15, 19, "2023-2024", "BKAV SmartHome", 2, "Ứng dụng Zigbee trong nhà thông minh" },
                    { "DT093", (byte)1, 12, 19, "2023-2024", "Sony VN", 3, "Thiết kế mạch lọc thông dải cho âm thanh" },
                    { "DT094", (byte)2, 14, 19, "2023-2024", "Công ty Savis", 2, "Hệ thống cảnh báo cháy dùng cảm biến khói" },
                    { "DT095", (byte)1, 20, 19, "2023-2024", "VinAI", 4, "Ứng dụng AI trong xử lý ảnh y tế" },
                    { "DT096", (byte)1, 16, 20, "2023-2024", "Yamaha VN", 3, "Thiết kế mạch khuếch đại âm thanh Hi-Fi" },
                    { "DT097", (byte)2, 18, 20, "2023-2024", "Intel Products VN", 4, "Ứng dụng Bluetooth Low Energy trong IoT" },
                    { "DT098", (byte)1, 19, 20, "2023-2024", "Synopsys VN", 2, "Thiết kế mạch số với FPGA" },
                    { "DT099", (byte)2, 20, 20, "2023-2024", "Zalo AI", 3, "Ứng dụng AI trong nhận diện giọng nói" },
                    { "DT100", (byte)1, 17, 20, "2023-2024", "FPT Robotics", 2, "Thiết kế cảm biến siêu nhạy cho robot" },
                    { "DT101", (byte)1, 20, 21, "2024-2025", "VinFast", 3, "Thiết kế hệ thống truyền động ô tô điện" },
                    { "DT102", (byte)2, 16, 21, "2023-2024", "Thaco Auto", 2, "Mô phỏng động cơ diesel bằng Matlab" },
                    { "DT103", (byte)1, 15, 21, "2023-2024", "Honda VN", 3, "Hệ thống phanh ABS mô hình" },
                    { "DT104", (byte)2, 18, 21, "2023-2024", "Toyota VN", 4, "Thiết kế hệ thống lái trợ lực điện" },
                    { "DT105", (byte)1, 19, 21, "2023-2024", "Mazda VN", 2, "Mô hình hệ thống truyền lực hybrid" },
                    { "DT106", (byte)2, 17, 22, "2023-2024", "Isuzu VN", 3, "Thiết kế hệ thống treo khí nén" },
                    { "DT107", (byte)1, 20, 22, "2023-2024", "IUH Racing", 4, "Mô phỏng khí động học xe đua" },
                    { "DT108", (byte)2, 14, 22, "2023-2024", "Suzuki VN", 2, "Thiết kế hộp số tự động CVT" },
                    { "DT109", (byte)1, 13, 22, "2023-2024", "Ford VN", 3, "Phân tích hệ thống xả giảm khí thải" },
                    { "DT110", (byte)2, 18, 22, "2023-2024", "Thaco Truck", 2, "Ứng dụng IoT giám sát xe tải" },
                    { "DT111", (byte)1, 15, 23, "2023-2024", "Hyundai Thành Công", 3, "Thiết kế hệ thống làm mát động cơ" },
                    { "DT112", (byte)2, 16, 23, "2023-2024", "Kia VN", 4, "Mô phỏng động học hệ thống treo" },
                    { "DT113", (byte)1, 12, 23, "2023-2024", "Bosch VN", 2, "Ứng dụng PLC trong điều khiển băng thử" },
                    { "DT114", (byte)2, 18, 23, "2023-2024", "Continental VN", 3, "Thiết kế hệ thống khởi động thông minh" },
                    { "DT115", (byte)1, 19, 23, "2023-2024", "R&D VinFast", 2, "Phân tích rung động khung xe" },
                    { "DT116", (byte)2, 20, 24, "2024-2025", "EVN E-Mobility", 4, "Thiết kế hệ thống sạc nhanh cho ô tô điện" },
                    { "DT117", (byte)1, 14, 24, "2023-2024", "VinAI", 3, "Ứng dụng AI tối ưu tiêu hao nhiên liệu" },
                    { "DT118", (byte)2, 17, 24, "2023-2024", "Toyota VN", 2, "Thiết kế hệ thống truyền động hybrid song song" },
                    { "DT119", (byte)1, 15, 24, "2023-2024", "Honda VN", 3, "Mô phỏng động cơ xăng tăng áp" },
                    { "DT120", (byte)2, 13, 24, "2023-2024", "Mazda VN", 2, "Thiết kế hệ thống trợ lực điện" },
                    { "DT121", (byte)1, 16, 25, "2023-2024", "Denso VN", 3, "Thiết kế hệ thống điều hòa không khí ô tô" },
                    { "DT122", (byte)2, 12, 25, "2023-2024", "Bosch VN", 2, "Ứng dụng cảm biến áp suất trong động cơ" },
                    { "DT123", (byte)1, 20, 25, "2023-2024", "Thaco Auto", 4, "Mô phỏng CFD hệ thống nạp khí" },
                    { "DT124", (byte)2, 18, 25, "2023-2024", "Hyundai R&D", 3, "Thiết kế hộp số ly hợp kép DCT" },
                    { "DT125", (byte)1, 15, 25, "2023-2024", "Sở GTVT HCM", 2, "Ứng dụng IoT theo dõi xe buýt" },
                    { "DT126", (byte)1, 18, 26, "2024-2025", "Searefico", 3, "Thiết kế hệ thống lạnh công nghiệp" },
                    { "DT127", (byte)2, 20, 26, "2023-2024", "Satra Cold Storage", 4, "Ứng dụng IoT giám sát kho lạnh" },
                    { "DT128", (byte)1, 14, 26, "2023-2024", "Công ty Vinamilk", 2, "Mô phỏng chu trình lạnh NH3" },
                    { "DT129", (byte)2, 15, 26, "2023-2024", "Daikin VN", 3, "Thiết kế hệ thống điều hòa VRV" },
                    { "DT130", (byte)1, 19, 26, "2023-2024", "SolarBK", 2, "Ứng dụng năng lượng mặt trời trong điều hòa" },
                    { "DT131", (byte)2, 17, 27, "2023-2024", "Minh Phú Seafood", 3, "Thiết kế hệ thống cấp đông nhanh IQF" },
                    { "DT132", (byte)1, 20, 27, "2023-2024", "CP Vietnam", 4, "Mô phỏng chu trình lạnh CO2" },
                    { "DT133", (byte)2, 16, 27, "2023-2024", "Satra Foods", 2, "Ứng dụng SCADA trong hệ thống lạnh" },
                    { "DT134", (byte)1, 18, 27, "2023-2024", "VinFast", 3, "Thiết kế hệ thống điều hòa ô tô điện" },
                    { "DT135", (byte)2, 15, 27, "2023-2024", "Bitzer VN", 2, "Phân tích hiệu suất máy nén trục vít" },
                    { "DT136", (byte)1, 20, 28, "2024-2025", "REE M&E", 4, "Thiết kế hệ thống HVAC cho tòa nhà" },
                    { "DT137", (byte)2, 14, 28, "2023-2024", "Searefico", 3, "Mô phỏng truyền nhiệt trong kho lạnh" },
                    { "DT138", (byte)1, 19, 28, "2023-2024", "Daikin VN", 2, "Ứng dụng IoT giám sát HVAC" },
                    { "DT139", (byte)2, 16, 28, "2023-2024", "Mitsubishi Electric VN", 3, "Thiết kế hệ thống bơm nhiệt Heat Pump" },
                    { "DT140", (byte)1, 15, 28, "2023-2024", "Panasonic VN", 2, "Mô hình điều hòa không khí tiết kiệm năng lượng" },
                    { "DT141", (byte)2, 17, 29, "2023-2024", "Samco", 3, "Thiết kế hệ thống điều hòa xe buýt" },
                    { "DT142", (byte)1, 18, 29, "2023-2024", "Tháp Nước Alpha", 2, "Mô phỏng hiệu suất tháp giải nhiệt" },
                    { "DT143", (byte)2, 20, 29, "2023-2024", "VinAI", 4, "Ứng dụng AI dự đoán tiêu thụ điện năng" },
                    { "DT144", (byte)1, 15, 29, "2023-2024", "Maersk VN", 2, "Thiết kế hệ thống lạnh container" },
                    { "DT145", (byte)2, 19, 29, "2023-2024", "Trane VN", 3, "Mô phỏng hệ thống điều hòa trung tâm Chiller" },
                    { "DT146", (byte)1, 14, 30, "2023-2024", "REE M&E", 2, "Thiết kế hệ thống thông gió hầm để xe" },
                    { "DT147", (byte)2, 16, 30, "2023-2024", "Công ty Savis", 3, "Ứng dụng BMS trong quản lý HVAC" },
                    { "DT148", (byte)1, 18, 30, "2023-2024", "Daikin VN", 2, "Mô phỏng truyền nhiệt dàn trao đổi" },
                    { "DT149", (byte)2, 19, 30, "2023-2024", "Mitsubishi Electric VN", 4, "Thiết kế hệ thống điều hòa VRF" },
                    { "DT150", (byte)1, 20, 30, "2023-2024", "SolarBK", 3, "Ứng dụng năng lượng tái tạo trong hệ thống lạnh" },
                    { "DT151", (byte)1, 12, 31, "2020-2021", "Công ty Việt Tiến", 2, "Thiết kế bộ sưu tập thời trang công sở" },
                    { "DT152", (byte)2, 15, 31, "2023-2024", "Công ty May 10", 3, "Nghiên cứu vải tái chế trong may mặc" },
                    { "DT153", (byte)1, 18, 31, "2023-2024", "Ninomaxx", 4, "Ứng dụng 3D trong thiết kế thời trang" },
                    { "DT154", (byte)2, 14, 31, "2023-2024", "IVY Moda", 2, "Phát triển thương hiệu thời trang bền vững" },
                    { "DT155", (byte)1, 10, 31, "2023-2024", "Nhà may Áo dài Minh Thư", 3, "Thiết kế áo dài hiện đại" },
                    { "DT156", (byte)1, 11, 32, "2023-2024", "May Nhà Bè", 2, "Thiết kế đồng phục học sinh" },
                    { "DT157", (byte)2, 17, 32, "2023-2024", "Công ty Dệt Phong Phú", 3, "Nghiên cứu ứng dụng vải kháng khuẩn" },
                    { "DT158", (byte)1, 20, 32, "2023-2024", "Công ty Faslink", 4, "Ứng dụng AI trong thiết kế mẫu may" },
                    { "DT159", (byte)2, 13, 32, "2023-2024", "Sân khấu kịch Hồng Vân", 2, "Thiết kế trang phục biểu diễn nghệ thuật" },
                    { "DT160", (byte)1, 15, 32, "2023-2024", "Công ty May Nhà Bè", 3, "Quy trình may jacket xuất khẩu" },
                    { "DT161", (byte)1, 16, 33, "2023-2024", "Công ty Nike VN", 2, "Thiết kế áo thể thao sử dụng vải co giãn" },
                    { "DT162", (byte)2, 18, 33, "2023-2024", "Công ty Dệt May Việt Thắng", 3, "Nghiên cứu dệt nhuộm thân thiện môi trường" },
                    { "DT163", (byte)1, 19, 33, "2023-2024", "Công ty Leflair", 4, "Ứng dụng Clo3D trong mô phỏng trang phục" },
                    { "DT164", (byte)2, 14, 33, "2023-2024", "Công ty Owen", 2, "Thiết kế áo khoác dạ nữ cao cấp" },
                    { "DT165", (byte)1, 12, 33, "2023-2024", "Adidas VN", 3, "Phân tích quy trình sản xuất giày thể thao" },
                    { "DT166", (byte)2, 20, 34, "2024-2025", "NTK Lý Quý Khánh", 4, "Thiết kế váy dạ hội" },
                    { "DT167", (byte)1, 15, 34, "2023-2024", "Công ty Dệt Kim Đông Xuân", 2, "Nghiên cứu vật liệu vải không dệt" },
                    { "DT168", (byte)2, 18, 34, "2023-2024", "Công ty Faslink", 3, "Ứng dụng Blockchain trong truy xuất nguồn gốc may mặc" },
                    { "DT169", (byte)1, 12, 34, "2023-2024", "Công ty Levi’s VN", 2, "Thiết kế quần jeans thời trang" },
                    { "DT170", (byte)2, 14, 34, "2023-2024", "Công ty Việt Tiến", 3, "Quy trình sản xuất áo sơ mi xuất khẩu" },
                    { "DT171", (byte)1, 11, 35, "2023-2024", "Nhà may Ngân An", 2, "Thiết kế bộ sưu tập áo dài truyền thống" },
                    { "DT172", (byte)2, 13, 35, "2023-2024", "Công ty May Việt Tiến", 3, "Nghiên cứu vải pha cotton" },
                    { "DT173", (byte)1, 20, 35, "2023-2024", "Công ty Leflair", 4, "Ứng dụng AI phân tích xu hướng thời trang" },
                    { "DT174", (byte)2, 15, 35, "2023-2024", "Công ty An Phước", 2, "Thiết kế đồng phục doanh nghiệp" },
                    { "DT175", (byte)1, 19, 35, "2023-2024", "Công ty Faslink", 3, "Phát triển sản phẩm thời trang thông minh" },
                    { "DT176", (byte)1, 18, 36, "2024-2025", "BASF Việt Nam", 3, "Tổng hợp xúc tác nano cho phản ứng ester hóa" },
                    { "DT177", (byte)2, 15, 36, "2023-2024", "Vedan Việt Nam", 2, "Xử lý nước thải nhuộm bằng vật liệu hấp phụ sinh học" },
                    { "DT178", (byte)1, 20, 36, "2023-2024", "Viện Hóa học – VAST", 4, "Nghiên cứu pin kẽm–ion dung môi nước" },
                    { "DT179", (byte)2, 12, 36, "2023-2024", "Ajinomoto Việt Nam", 3, "Tối ưu hóa quy trình chiết xuất pectin từ vỏ trái cây" },
                    { "DT180", (byte)1, 16, 36, "2023-2024", "Dow Việt Nam", 2, "Tổng hợp polyme phân hủy sinh học trên cơ sở PLA" },
                    { "DT181", (byte)2, 19, 37, "2023-2024", "PetroVietnam R&D", 4, "Khảo sát đặc tính xúc tác zeolit trong cracking" },
                    { "DT182", (byte)1, 17, 37, "2023-2024", "Viện Môi trường & Tài nguyên", 3, "Chế tạo màng lọc nano loại bỏ kim loại nặng" },
                    { "DT183", (byte)2, 13, 37, "2023-2024", "UDEC – ĐH Bách Khoa HCM", 2, "Sản xuất biodiesel từ dầu thải nhà hàng" },
                    { "DT184", (byte)1, 11, 37, "2023-2024", "Khoa học & Công nghệ TP.HCM", 3, "Ứng dụng HPLC định lượng phụ gia thực phẩm" },
                    { "DT185", (byte)2, 18, 37, "2023-2024", "Viện Hóa học – VAST", 3, "Tổng hợp MOF cho hấp phụ CO₂" },
                    { "DT186", (byte)1, 16, 38, "2023-2024", "Sơn TOA Việt Nam", 2, "Chế tạo sơn kháng khuẩn dùng bạc nano" },
                    { "DT187", (byte)2, 14, 38, "2023-2024", "3M Việt Nam", 2, "Nghiên cứu keo dán thân thiện môi trường" },
                    { "DT188", (byte)1, 20, 38, "2023-2024", "DHG Pharma", 4, "Tổng hợp dược chất trung gian qua phản ứng Friedel–Crafts" },
                    { "DT189", (byte)2, 12, 38, "2023-2024", "Vinachem", 3, "Tối ưu hóa quy trình tạo hạt phân bón NPK" },
                    { "DT190", (byte)1, 18, 38, "2023-2024", "Trung tâm Quatest 3", 3, "Phân tích vi nhựa trong nước mặt bằng GC–MS" },
                    { "DT191", (byte)2, 15, 39, "2023-2024", "Công ty Dược OPC", 2, "Chiết tách tinh dầu sả bằng CO₂ siêu tới hạn" },
                    { "DT192", (byte)1, 13, 39, "2023-2024", "Sawaco", 2, "Xử lý Asen trong nước giếng khoan bằng vật liệu than hoạt tính" },
                    { "DT193", (byte)2, 11, 39, "2023-2024", "Perfetti Van Melle VN", 3, "Tạo hương liệu tự nhiên từ phụ phẩm nông nghiệp" },
                    { "DT194", (byte)1, 19, 39, "2023-2024", "Viện Vật liệu – VAST", 4, "Tổng hợp vật liệu perovskite cho pin mặt trời" },
                    { "DT195", (byte)2, 17, 39, "2023-2024", "Nhựa Bình Minh", 3, "Đánh giá độ bền nhiệt polyme gia cường sợi thủy tinh" },
                    { "DT196", (byte)1, 18, 40, "2024-2025", "Unilever Việt Nam", 3, "Tổng hợp chất hoạt động bề mặt sinh học" },
                    { "DT197", (byte)2, 12, 40, "2023-2024", "ACECOOK VN", 2, "Đánh giá độ bền oxy hóa của dầu ăn tái sử dụng" },
                    { "DT198", (byte)1, 20, 40, "2023-2024", "Viện Hóa học – VAST", 4, "Tổng hợp dung môi ion lỏng cho tách chiết cellulose" },
                    { "DT199", (byte)2, 16, 40, "2023-2024", "Tetra Pak VN", 3, "Phát triển bao bì sinh học kháng khuẩn" },
                    { "DT200", (byte)1, 14, 40, "2023-2024", "SABECO", 2, "Quy trình sản xuất bia thủ công tối ưu hóa" },
                    { "DT201", (byte)2, 11, 41, "2023-2024", "ĐH KHTN TP.HCM", 2, "Mô hình hóa lan truyền dịch bệnh SIR" },
                    { "DT202", (byte)1, 12, 41, "2023-2024", "Viện Toán Ứng dụng", 3, "Phân tích dữ liệu thống kê bằng R cho giáo dục" },
                    { "DT203", (byte)2, 14, 41, "2023-2024", "Viện Vật lý – VAST", 2, "Mô phỏng cơ học lượng tử một chiều" },
                    { "DT204", (byte)1, 13, 41, "2023-2024", "FPT Analytics", 3, "Ứng dụng tối ưu hóa tuyến tính trong logistics" },
                    { "DT205", (byte)2, 10, 41, "2023-2024", "NXB Giáo dục", 2, "Xây dựng bộ công cụ học tập xác suất số" },
                    { "DT206", (byte)1, 12, 42, "2023-2024", "Trung tâm Thí nghiệm Vật lý", 3, "Thí nghiệm giao thoa ánh sáng và ứng dụng" },
                    { "DT207", (byte)2, 11, 42, "2023-2024", "ĐH KHTN TP.HCM", 2, "Mô phỏng dao động tắt dần bằng Python" },
                    { "DT208", (byte)1, 15, 42, "2023-2024", "Đài Khí tượng Thủy văn", 3, "Phân tích chuỗi thời gian khí tượng" },
                    { "DT209", (byte)2, 14, 42, "2023-2024", "Sở GD&ĐT TP.HCM", 3, "Xây dựng mô hình hồi quy đa biến cho giáo dục" },
                    { "DT210", (byte)1, 13, 42, "2023-2024", "Viện Cơ học – VAST", 2, "Thiết kế bộ thí nghiệm cơ học chất lưu đơn giản" },
                    { "DT211", (byte)2, 18, 43, "2019-2020", "Bệnh viện Đại học Y Dược", 4, "Ứng dụng thống kê Bayes trong y sinh" },
                    { "DT212", (byte)1, 16, 43, "2023-2024", "EVN HCMC", 3, "Mô hình toán cho dự báo nhu cầu điện" },
                    { "DT213", (byte)2, 10, 43, "2023-2024", "Trung tâm E-learning", 2, "Thiết kế học liệu số môn Giải tích" },
                    { "DT214", (byte)1, 12, 43, "2023-2024", "Phòng Khảo thí & ĐBCL", 3, "Phân tích dữ liệu khảo sát sinh viên bằng SPSS" },
                    { "DT215", (byte)2, 14, 43, "2023-2024", "Viện Nhiệt – Lạnh", 2, "Mô phỏng truyền nhiệt thanh kim loại" },
                    { "DT216", (byte)1, 11, 44, "2023-2024", "Khoa KH Cơ bản", 2, "Xây dựng bộ câu hỏi trắc nghiệm Vật lý 1" },
                    { "DT217", (byte)2, 12, 44, "2023-2024", "Trung tâm Khảo thí", 3, "Thống kê suy luận cho dữ liệu giáo dục" },
                    { "DT218", (byte)1, 15, 44, "2023-2024", "Viện Toán Ứng dụng", 3, "Mô hình Monte Carlo trong tài chính cơ bản" },
                    { "DT219", (byte)2, 13, 44, "2023-2024", "ĐH Sư phạm Kỹ thuật", 2, "Xây dựng mô hình dịch chuyển Brown" },
                    { "DT220", (byte)1, 14, 44, "2023-2024", "Phòng Thí nghiệm Vật lý", 3, "Thiết kế bài thí nghiệm điện cơ bản cho năm nhất" },
                    { "DT221", (byte)2, 10, 45, "2023-2024", "Trung tâm CNTT IUH", 2, "Ứng dụng Python trong dạy học xác suất" },
                    { "DT222", (byte)1, 12, 45, "2023-2024", "Khoa KH Cơ bản", 3, "Phân tích dữ liệu khảo sát bằng phương pháp EFA" },
                    { "DT223", (byte)2, 13, 45, "2023-2024", "ĐH KHTN TP.HCM", 2, "Mô phỏng dao động điều hòa bằng MATLAB" },
                    { "DT224", (byte)1, 11, 45, "2023-2024", "Trung tâm E-learning", 3, "Thiết kế học liệu tương tác môn Xác suất–Thống kê" },
                    { "DT225", (byte)2, 15, 45, "2023-2024", "Viện Toán Ứng dụng", 3, "Ứng dụng mô hình Markov trong dự báo chuỗi thời gian" },
                    { "DT226", (byte)1, 12, 46, "2020-2021", "Sở Công Thương TP.HCM", 2, "Pháp luật về thương mại điện tử" },
                    { "DT227", (byte)2, 15, 46, "2023-2024", "Phòng Lao động – Thương binh & XH", 3, "Luật lao động và quan hệ việc làm" },
                    { "DT228", (byte)1, 18, 46, "2023-2024", "Cục SHTT Việt Nam", 4, "Quyền sở hữu trí tuệ trong khởi nghiệp" },
                    { "DT229", (byte)2, 14, 46, "2023-2024", "Tòa án ND TP.HCM", 2, "Pháp luật về hợp đồng dân sự" },
                    { "DT230", (byte)1, 16, 46, "2023-2024", "Học viện Chính trị Quốc gia", 3, "Pháp luật và chính trị quốc tế" },
                    { "DT231", (byte)2, 12, 47, "2023-2024", "Viện KSND TP.HCM", 3, "Luật hình sự và cải cách tư pháp" },
                    { "DT232", (byte)1, 17, 47, "2023-2024", "Liên Hợp Quốc tại Việt Nam", 2, "Quyền con người trong pháp luật quốc tế" },
                    { "DT233", (byte)2, 18, 47, "2023-2024", "Phòng Thương mại & Công nghiệp VN", 4, "Luật kinh doanh và cạnh tranh" },
                    { "DT234", (byte)1, 14, 47, "2023-2024", "UBND TP.HCM", 2, "Luật hành chính và quản lý nhà nước" },
                    { "DT235", (byte)2, 16, 47, "2023-2024", "Sở TNMT TP.HCM", 3, "Luật bảo vệ môi trường" },
                    { "DT236", (byte)1, 15, 48, "2023-2024", "Ngân hàng Nhà nước VN", 2, "Luật tài chính – ngân hàng" },
                    { "DT237", (byte)2, 18, 48, "2023-2024", "Sở Giao dịch Chứng khoán TP.HCM", 4, "Pháp luật về chứng khoán" },
                    { "DT238", (byte)1, 14, 48, "2023-2024", "Sở TNMT", 3, "Luật đất đai và bất động sản" },
                    { "DT239", (byte)2, 19, 48, "2023-2024", "Công ty Luật YKVN", 3, "Pháp luật về hợp đồng thương mại quốc tế" },
                    { "DT240", (byte)1, 12, 48, "2023-2024", "Thanh tra Chính phủ", 2, "Pháp luật chống tham nhũng" },
                    { "DT241", (byte)2, 13, 49, "2023-2024", "Tòa án ND Tối cao", 3, "Luật dân sự nâng cao" },
                    { "DT242", (byte)1, 17, 49, "2023-2024", "WIPO Việt Nam", 4, "Pháp luật về sở hữu trí tuệ quốc tế" },
                    { "DT243", (byte)2, 15, 49, "2023-2024", "VIAC Việt Nam", 2, "Luật hợp đồng và trọng tài thương mại" },
                    { "DT244", (byte)1, 14, 49, "2023-2024", "Hội Bảo vệ người tiêu dùng", 3, "Pháp luật bảo vệ người tiêu dùng" },
                    { "DT245", (byte)2, 16, 49, "2023-2024", "Bộ Công an", 4, "Luật an ninh mạng" },
                    { "DT246", (byte)1, 12, 50, "2023-2024", "Quốc hội VN", 2, "Luật hiến pháp và quyền công dân" },
                    { "DT247", (byte)2, 18, 50, "2023-2024", "IOM Việt Nam", 3, "Pháp luật quốc tế về di cư" },
                    { "DT248", (byte)1, 14, 50, "2023-2024", "Ban thư ký ASEAN", 3, "Pháp luật kinh tế ASEAN" },
                    { "DT249", (byte)2, 19, 50, "2023-2024", "Bộ Công Thương", 4, "Luật thương mại quốc tế" },
                    { "DT250", (byte)1, 13, 50, "2023-2024", "ĐH Luật TP.HCM", 2, "Pháp luật dân sự so sánh" },
                    { "DT251", (byte)1, 12, 51, "2024-2025", "British Council VN", 2, "Phương pháp giảng dạy tiếng Anh giao tiếp" },
                    { "DT252", (byte)2, 18, 51, "2023-2024", "Duolingo VN", 4, "Ứng dụng AI trong học từ vựng ngoại ngữ" },
                    { "DT253", (byte)1, 14, 51, "2023-2024", "Viện Khổng Tử", 3, "Giáo trình tiếng Trung thương mại" },
                    { "DT254", (byte)2, 15, 51, "2023-2024", "Japan Foundation VN", 2, "Phát triển năng lực nghe – nói tiếng Nhật" },
                    { "DT255", (byte)1, 16, 51, "2023-2024", "FPT Software", 3, "Dịch thuật Anh – Việt chuyên ngành CNTT" },
                    { "DT256", (byte)1, 14, 52, "2023-2024", "British Council VN", 3, "Phát triển kỹ năng viết học thuật tiếng Anh" },
                    { "DT257", (byte)2, 16, 52, "2023-2024", "Viện Pháp ngữ", 2, "Ứng dụng e-learning trong giảng dạy tiếng Pháp" },
                    { "DT258", (byte)1, 12, 52, "2023-2024", "Trung tâm Hoa Văn Thăng Long", 3, "Nghiên cứu phương pháp nghe – nói tiếng Trung" },
                    { "DT259", (byte)2, 18, 52, "2023-2024", "Duolingo VN", 4, "Ứng dụng ChatGPT trong học ngoại ngữ" },
                    { "DT260", (byte)1, 15, 52, "2023-2024", "Công ty Dịch thuật Expertrans", 2, "Biên – phiên dịch Anh – Việt thương mại" },
                    { "DT261", (byte)1, 13, 53, "2023-2024", "Japan Foundation VN", 2, "Xây dựng học liệu tiếng Nhật cho du lịch" },
                    { "DT262", (byte)2, 16, 53, "2023-2024", "Sejong Center", 3, "Phát triển năng lực giao tiếp tiếng Hàn" },
                    { "DT263", (byte)1, 12, 53, "2023-2024", "Apollo English", 3, "Ứng dụng phim ảnh trong dạy tiếng Anh" },
                    { "DT264", (byte)2, 17, 53, "2023-2024", "FPT Software", 4, "Biên dịch tài liệu kỹ thuật Anh – Việt" },
                    { "DT265", (byte)1, 18, 53, "2023-2024", "Topica Native", 2, "Ứng dụng gamification trong học ngoại ngữ" },
                    { "DT266", (byte)2, 14, 54, "2023-2024", "ĐH KHXH&NV TP.HCM", 3, "Nghiên cứu ngôn ngữ học so sánh Anh – Việt" },
                    { "DT267", (byte)1, 15, 54, "2023-2024", "British Council VN", 2, "Phương pháp giảng dạy tiếng Anh chuyên ngành" },
                    { "DT268", (byte)2, 19, 54, "2023-2024", "Duolingo VN", 4, "Ứng dụng công nghệ AR trong học ngoại ngữ" },
                    { "DT269", (byte)1, 12, 54, "2023-2024", "VUS", 3, "Dạy – học từ vựng theo ngữ cảnh" },
                    { "DT270", (byte)2, 16, 54, "2023-2024", "Báo Tuổi Trẻ", 2, "Biên dịch báo chí song ngữ Anh – Việt" },
                    { "DT271", (byte)1, 12, 55, "2023-2024", "NXB Trẻ", 3, "Nghiên cứu dịch văn học Anh – Việt" },
                    { "DT272", (byte)2, 14, 55, "2023-2024", "Topica Native", 2, "Ứng dụng podcast trong học ngoại ngữ" },
                    { "DT273", (byte)1, 16, 55, "2023-2024", "British Council VN", 3, "Kỹ năng viết báo cáo khoa học tiếng Anh" },
                    { "DT274", (byte)2, 18, 55, "2023-2024", "FPT Software", 4, "Xây dựng từ điển song ngữ Việt – Anh ngành CNTT" },
                    { "DT275", (byte)1, 20, 55, "2023-2024", "Zalo AI", 2, "Ứng dụng NLP trong dịch tự động" },
                    { "DT276", (byte)1, 15, 56, "2024-2025", "Grab VN", 2, "Chiến lược marketing cho startup" },
                    { "DT277", (byte)2, 18, 56, "2023-2024", "Talentnet", 3, "Quản trị nguồn nhân lực trong kỷ nguyên số" },
                    { "DT278", (byte)1, 14, 56, "2023-2024", "VNG", 4, "Ứng dụng CRM trong quản lý khách hàng" },
                    { "DT279", (byte)2, 16, 56, "2023-2024", "Saigon Co.op", 2, "Chiến lược cạnh tranh ngành bán lẻ" },
                    { "DT280", (byte)1, 20, 56, "2023-2024", "Shopee VN", 3, "Ứng dụng AI trong phân tích thị trường" },
                    { "DT281", (byte)2, 14, 57, "2023-2024", "BigC VN", 3, "Quản trị chuỗi cung ứng bán lẻ" },
                    { "DT282", (byte)1, 12, 57, "2023-2024", "Haravan", 2, "Phát triển thương hiệu cá nhân" },
                    { "DT283", (byte)2, 18, 57, "2023-2024", "MISA", 4, "Ứng dụng ERP trong doanh nghiệp nhỏ" },
                    { "DT284", (byte)1, 15, 57, "2023-2024", "Unilever VN", 2, "Chiến lược định giá sản phẩm" },
                    { "DT285", (byte)2, 16, 57, "2023-2024", "Nielsen VN", 3, "Phân tích hành vi người tiêu dùng" },
                    { "DT286", (byte)1, 17, 58, "2023-2024", "PwC VN", 2, "Quản trị tài chính doanh nghiệp" },
                    { "DT287", (byte)2, 20, 58, "2023-2024", "KPMG VN", 4, "Ứng dụng phân tích dữ liệu lớn trong kinh doanh" },
                    { "DT288", (byte)1, 18, 58, "2023-2024", "VCCI", 3, "Chiến lược mở rộng thị trường quốc tế" },
                    { "DT289", (byte)2, 19, 58, "2023-2024", "VeChain VN", 3, "Ứng dụng blockchain trong quản lý chuỗi cung ứng" },
                    { "DT290", (byte)1, 15, 58, "2023-2024", "EY VN", 2, "Quản trị rủi ro trong doanh nghiệp" },
                    { "DT291", (byte)2, 14, 59, "2023-2024", "BK Holdings", 2, "Khởi nghiệp và đổi mới sáng tạo" },
                    { "DT292", (byte)1, 12, 59, "2023-2024", "SSI", 3, "Phân tích báo cáo tài chính doanh nghiệp" },
                    { "DT293", (byte)2, 18, 59, "2023-2024", "Viettel Global", 4, "Chiến lược kinh doanh quốc tế" },
                    { "DT294", (byte)1, 13, 59, "2023-2024", "Haravan", 2, "Ứng dụng phân tích SWOT trong doanh nghiệp" },
                    { "DT295", (byte)2, 17, 59, "2023-2024", "FPT IS", 3, "Quản trị dự án CNTT" },
                    { "DT296", (byte)1, 20, 60, "2023-2024", "Google VN", 4, "Chiến lược kinh doanh trong kỷ nguyên số" },
                    { "DT297", (byte)2, 19, 60, "2023-2024", "HSBC VN", 3, "Ứng dụng AI trong dự báo tài chính" },
                    { "DT298", (byte)1, 16, 60, "2023-2024", "VCCI", 2, "Quản trị chiến lược đa quốc gia" },
                    { "DT299", (byte)2, 14, 60, "2023-2024", "HOSE", 3, "Phân tích thị trường chứng khoán" },
                    { "DT300", (byte)1, 18, 60, "2023-2024", "SAP VN", 2, "Ứng dụng ERP trong doanh nghiệp lớn" },
                    { "DT301", (byte)1, 18, 61, "2024-2025", "Sở Du lịch TP.HCM", 3, "Chiến lược phát triển điểm đến du lịch thông minh" },
                    { "DT302", (byte)2, 16, 61, "2023-2024", "Saigontourist", 2, "Ứng dụng chuyển đổi số trong lữ hành" },
                    { "DT303", (byte)1, 12, 61, "2023-2024", "UBND Huyện Cần Giờ", 3, "Phát triển sản phẩm du lịch cộng đồng" },
                    { "DT304", (byte)2, 14, 61, "2023-2024", "Vietravel", 4, "Marketing số cho doanh nghiệp lữ hành vừa và nhỏ" },
                    { "DT305", (byte)1, 15, 61, "2023-2024", "New World Saigon Hotel", 2, "Quản trị trải nghiệm khách hàng khách sạn 4*" },
                    { "DT306", (byte)1, 19, 62, "2023-2024", "SECC Quận 7", 4, "Chuỗi cung ứng dịch vụ MICE tại TP.HCM" },
                    { "DT307", (byte)2, 17, 62, "2023-2024", "InterContinental Saigon", 3, "Tối ưu doanh thu phòng (Revenue Management)" },
                    { "DT308", (byte)1, 13, 62, "2023-2024", "TST Tourist", 2, "Ứng dụng CRM trong doanh nghiệp lữ hành" },
                    { "DT309", (byte)2, 11, 62, "2023-2024", "Nhà hàng Gạo", 2, "Chuẩn hóa quy trình phục vụ nhà hàng" },
                    { "DT310", (byte)1, 16, 62, "2023-2024", "Agoda Việt Nam", 3, "Ứng dụng đánh giá trực tuyến (OTA) trong nâng cao chất lượng" },
                    { "DT311", (byte)1, 15, 63, "2023-2024", "BenThanh Tourist", 2, "Phát triển tour du lịch hướng đến bền vững" },
                    { "DT312", (byte)2, 12, 63, "2023-2024", "Saigon Food Tour", 3, "Thiết kế trải nghiệm ẩm thực trong city tour" },
                    { "DT313", (byte)1, 18, 63, "2023-2024", "Vietravel", 4, "Chuyển đổi số trong điều hành tour" },
                    { "DT314", (byte)2, 14, 63, "2023-2024", "Trung tâm Xúc tiến Du lịch HCM", 2, "Xây dựng thương hiệu điểm đến quận 1" },
                    { "DT315", (byte)1, 20, 63, "2023-2024", "Sở Du lịch TP.HCM", 3, "Ứng dụng dữ liệu lớn dự báo lượng khách" },
                    { "DT316", (byte)2, 13, 64, "2023-2024", "Novotel Saigon Centre", 2, "Thiết kế quy trình phục vụ buồng phòng chuẩn 4*" },
                    { "DT317", (byte)1, 16, 64, "2023-2024", "AIG VN", 3, "Quản trị rủi ro trong kinh doanh lữ hành" },
                    { "DT318", (byte)2, 18, 64, "2023-2024", "Klook Việt Nam", 4, "Ứng dụng AI chatbot tư vấn tour" },
                    { "DT319", (byte)1, 15, 64, "2023-2024", "Hotel De Arts Saigon", 2, "Chuẩn hóa quy trình check-in/out tự động" },
                    { "DT320", (byte)2, 12, 64, "2023-2024", "Riverside Hotel", 3, "Thiết kế gói sản phẩm staycation cuối tuần" },
                    { "DT321", (byte)1, 14, 65, "2023-2024", "Park Hyatt Saigon", 2, "Quản trị chất lượng dịch vụ spa trong khách sạn" },
                    { "DT322", (byte)2, 13, 65, "2023-2024", "KDL Sinh thái Củ Chi", 2, "Phát triển du lịch sinh thái tại Củ Chi" },
                    { "DT323", (byte)1, 17, 65, "2023-2024", "TikTok VN", 4, "Chiến lược truyền thông điểm đến số" },
                    { "DT324", (byte)2, 12, 65, "2023-2024", "Bảo tàng TP.HCM", 3, "Thiết kế tour trải nghiệm văn hóa Chợ Lớn" },
                    { "DT325", (byte)1, 19, 65, "2023-2024", "Booking.com VN", 3, "Ứng dụng dữ liệu OTA tối ưu giá phòng" },
                    { "DT326", (byte)1, 18, 66, "2018-2019", "Công ty Coteccons", 3, "Thiết kế kết cấu bê tông cốt thép nhà cao tầng" },
                    { "DT327", (byte)2, 20, 66, "2023-2024", "Hòa Bình Corp", 4, "Ứng dụng BIM trong quản lý công trình" },
                    { "DT328", (byte)1, 15, 66, "2023-2024", "Công ty Cầu đường HCM", 2, "Thiết kế cầu dầm liên tục bằng SAP2000" },
                    { "DT329", (byte)2, 13, 66, "2023-2024", "Delta Group", 2, "Quản lý tiến độ thi công bằng MS Project" },
                    { "DT330", (byte)1, 19, 66, "2023-2024", "Viện KTXD TP.HCM", 3, "Giải pháp kết cấu xanh cho đô thị bền vững" },
                    { "DT331", (byte)2, 17, 67, "2023-2024", "Công ty Steel Builder", 3, "Thiết kế nhà công nghiệp khung thép" },
                    { "DT332", (byte)1, 16, 67, "2023-2024", "Công ty TT-As", 2, "Ứng dụng Revit Architecture trong thiết kế" },
                    { "DT333", (byte)2, 15, 67, "2023-2024", "Sawaco", 3, "Thiết kế hệ thống thoát nước đô thị" },
                    { "DT334", (byte)1, 18, 67, "2023-2024", "Công ty Cofico", 4, "Quản lý chất lượng thi công công trình" },
                    { "DT335", (byte)2, 19, 67, "2023-2024", "Viện VLXD", 3, "Nghiên cứu ứng dụng vật liệu mới trong xây dựng" },
                    { "DT336", (byte)1, 14, 68, "2023-2024", "Công ty An Phú Gia", 2, "Thiết kế công trình dân dụng bằng Etabs" },
                    { "DT337", (byte)2, 20, 68, "2023-2024", "Viện Khoa học Thủy lợi", 4, "Phân tích động đất công trình nhà cao tầng" },
                    { "DT338", (byte)1, 17, 68, "2023-2024", "Sở Xây dựng TP.HCM", 3, "Ứng dụng GIS trong quy hoạch xây dựng" },
                    { "DT339", (byte)2, 15, 68, "2023-2024", "Công ty Cofico", 2, "Giải pháp quản lý rủi ro trong thi công" },
                    { "DT340", (byte)1, 13, 68, "2023-2024", "Viện VLXD", 3, "Thiết kế kết cấu gỗ công trình dân dụng" },
                    { "DT341", (byte)1, 19, 69, "2023-2024", "Viện Công nghệ VN", 3, "Ứng dụng công nghệ 3D in bê tông" },
                    { "DT342", (byte)2, 14, 69, "2023-2024", "Sở GTVT TP.HCM", 2, "Thiết kế công trình cầu vượt bộ hành" },
                    { "DT343", (byte)1, 20, 69, "2023-2024", "Công ty COTECCONS", 4, "Ứng dụng AI trong quản lý dự án xây dựng" },
                    { "DT344", (byte)2, 15, 69, "2023-2024", "Công ty Tư vấn GTVT", 2, "Thiết kế hệ thống giao thông đô thị" },
                    { "DT345", (byte)1, 18, 69, "2023-2024", "Viện VLXD", 3, "Nghiên cứu vật liệu bê tông cường độ siêu cao" },
                    { "DT346", (byte)2, 12, 70, "2023-2024", "HUD VN", 2, "Thiết kế công trình nhà ở xã hội" },
                    { "DT347", (byte)1, 15, 70, "2023-2024", "Viện Địa kỹ thuật", 3, "Phân tích ổn định mái dốc" },
                    { "DT348", (byte)2, 19, 70, "2023-2024", "Công ty COTECCONS", 4, "Ứng dụng Lean Construction trong quản lý thi công" },
                    { "DT349", (byte)1, 14, 70, "2023-2024", "Sawaco", 2, "Thiết kế hệ thống cấp thoát nước tòa nhà" },
                    { "DT350", (byte)2, 17, 70, "2023-2024", "Viện KTXD", 3, "Mô phỏng kết cấu nhà cao tầng bằng Etabs" },
                    { "DT351", (byte)1, 14, 71, "2018-2019", "KPMG VN", 2, "Phân tích tài chính doanh nghiệp vừa và nhỏ" },
                    { "DT352", (byte)2, 15, 71, "2023-2024", "PwC VN", 3, "Ứng dụng kế toán quản trị trong DN sản xuất" },
                    { "DT353", (byte)1, 13, 71, "2023-2024", "EY VN", 2, "Kế toán chi phí và ra quyết định" },
                    { "DT354", (byte)2, 18, 71, "2023-2024", "Deloitte VN", 4, "Ứng dụng IFRS trong báo cáo tài chính" },
                    { "DT355", (byte)1, 16, 71, "2023-2024", "VietinBank", 3, "Phân tích báo cáo hợp nhất" },
                    { "DT356", (byte)1, 15, 72, "2023-2024", "ACCA VN", 2, "Kế toán kiểm toán nội bộ" },
                    { "DT357", (byte)2, 17, 72, "2023-2024", "Ngân hàng BIDV", 3, "Phân tích chi phí – lợi ích dự án" },
                    { "DT358", (byte)1, 12, 72, "2023-2024", "MISA", 2, "Ứng dụng phần mềm kế toán trong DN nhỏ" },
                    { "DT359", (byte)2, 18, 72, "2023-2024", "Vietcombank", 4, "Quản trị tài chính doanh nghiệp" },
                    { "DT360", (byte)1, 16, 72, "2023-2024", "MB Bank", 3, "Phân tích rủi ro tín dụng" },
                    { "DT361", (byte)2, 14, 73, "2023-2024", "Deloitte VN", 2, "Kế toán quản lý công nợ" },
                    { "DT362", (byte)1, 19, 73, "2023-2024", "KPMG VN", 3, "Ứng dụng AI trong kiểm toán" },
                    { "DT363", (byte)2, 13, 73, "2023-2024", "EY VN", 2, "Phân tích tình hình tài chính bằng Excel nâng cao" },
                    { "DT364", (byte)1, 20, 73, "2023-2024", "PwC VN", 4, "Báo cáo tài chính hợp nhất IFRS" },
                    { "DT365", (byte)2, 15, 73, "2023-2024", "ACCA VN", 3, "Kế toán quốc tế và hội nhập" },
                    { "DT366", (byte)1, 18, 74, "2023-2024", "MISA", 4, "Ứng dụng phần mềm phân tích dữ liệu kế toán" },
                    { "DT367", (byte)2, 20, 74, "2023-2024", "KPMG VN", 3, "Kiểm toán công nghệ thông tin" },
                    { "DT368", (byte)1, 14, 74, "2023-2024", "VietinBank", 2, "Phân tích dòng tiền trong dự án đầu tư" },
                    { "DT369", (byte)2, 19, 74, "2023-2024", "HSBC VN", 4, "Ứng dụng Big Data trong tài chính" },
                    { "DT370", (byte)1, 15, 74, "2023-2024", "EY VN", 3, "Báo cáo tài chính hợp nhất DN đa quốc gia" },
                    { "DT371", (byte)2, 18, 75, "2023-2024", "HOSE", 4, "Ứng dụng IFRS cho DN niêm yết" },
                    { "DT372", (byte)1, 14, 75, "2023-2024", "Cục Thuế TP.HCM", 2, "Kế toán thuế trong DN FDI" },
                    { "DT373", (byte)2, 19, 75, "2023-2024", "SAP VN", 3, "Ứng dụng ERP trong kế toán tài chính" },
                    { "DT374", (byte)1, 13, 75, "2023-2024", "Hòa Bình Corp", 2, "Phân tích báo cáo tài chính DN xây dựng" },
                    { "DT375", (byte)2, 16, 75, "2023-2024", "Deloitte VN", 3, "Quản lý chi phí dự án bằng PM software" },
                    { "DT376", (byte)1, 20, 76, "2024-2025", "ĐH Sunderland (UK)", 4, "Chương trình MBA quốc tế – Quản trị chiến lược" },
                    { "DT377", (byte)2, 15, 76, "2023-2024", "IUH – DTQT", 2, "Đánh giá chất lượng đào tạo liên kết quốc tế" },
                    { "DT378", (byte)1, 18, 76, "2023-2024", "Coursera VN", 3, "Ứng dụng AI trong quản lý học tập LMS" },
                    { "DT379", (byte)2, 13, 76, "2023-2024", "British Council", 2, "So sánh hệ thống giáo dục Anh – Việt" },
                    { "DT380", (byte)1, 17, 76, "2023-2024", "IUH – DTQT", 3, "Phát triển kỹ năng lãnh đạo toàn cầu" },
                    { "DT381", (byte)1, 19, 77, "2023-2024", "Maersk VN", 3, "Quản trị chuỗi cung ứng toàn cầu" },
                    { "DT382", (byte)2, 20, 77, "2023-2024", "FPT IS", 4, "Ứng dụng Big Data trong quản lý đào tạo quốc tế" },
                    { "DT383", (byte)1, 15, 77, "2023-2024", "Fulbright VN", 2, "So sánh chương trình MBA Mỹ và Châu Á" },
                    { "DT384", (byte)2, 14, 77, "2023-2024", "ISO VN", 3, "Quản lý chất lượng đào tạo theo ISO" },
                    { "DT385", (byte)1, 16, 77, "2023-2024", "IUH – DTQT", 2, "Ứng dụng mô hình e-learning blended learning" },
                    { "DT386", (byte)2, 14, 78, "2023-2024", "Ban thư ký ASEAN", 3, "Hợp tác đào tạo quốc tế trong khối ASEAN" },
                    { "DT387", (byte)1, 13, 78, "2023-2024", "ĐH Sunderland", 2, "So sánh chương trình cử nhân quốc tế UK – VN" },
                    { "DT388", (byte)2, 19, 78, "2023-2024", "EdX VN", 4, "Ứng dụng AI trong giảng dạy online" },
                    { "DT389", (byte)1, 12, 78, "2023-2024", "British Council", 2, "Chính sách học bổng quốc tế và tác động" },
                    { "DT390", (byte)2, 16, 78, "2023-2024", "Cục Hợp tác quốc tế – Bộ GD&ĐT", 3, "Quản lý du học sinh tại VN" },
                    { "DT391", (byte)1, 15, 79, "2023-2024", "Fulbright VN", 2, "So sánh hệ thống quản trị ĐH Mỹ – VN" },
                    { "DT392", (byte)2, 20, 79, "2023-2024", "IUH – DTQT", 4, "Ứng dụng Blockchain trong xác thực bằng cấp" },
                    { "DT393", (byte)1, 14, 79, "2023-2024", "Viện Nghiên cứu GD", 3, "Đánh giá chuẩn đầu ra sinh viên quốc tế" },
                    { "DT394", (byte)2, 12, 79, "2023-2024", "Japan Foundation", 2, "So sánh kỹ năng mềm sinh viên VN – Nhật" },
                    { "DT395", (byte)1, 18, 79, "2023-2024", "MOET VN", 3, "Ứng dụng AI trong kiểm định chất lượng GDQT" },
                    { "DT396", (byte)1, 18, 80, "2023-2024", "AUN-QA Việt Nam", 3, "Chuẩn hóa kiểm định quốc tế AUN-QA cho CT liên kết" },
                    { "DT397", (byte)2, 19, 80, "2023-2024", "Coursera VN", 4, "Chiến lược micro-credential và công nhận tín chỉ" },
                    { "DT398", (byte)1, 14, 80, "2023-2024", "IUH – DTQT", 2, "Hệ thống proctoring trực tuyến và đạo đức học thuật" },
                    { "DT399", (byte)2, 20, 80, "2023-2024", "Turnitin VN", 4, "Ứng dụng học máy phát hiện đạo văn đa ngôn ngữ" },
                    { "DT400", (byte)1, 16, 80, "2023-2024", "ASEAN University Network", 3, "Tối ưu quy trình trao đổi SV quốc tế dựa trên dữ liệu" },
                    { "DT401", (byte)1, 15, 81, "2018-2019", "Vinamilk", 2, "Chiết xuất hợp chất chống oxy hóa từ trà xanh" },
                    { "DT402", (byte)2, 18, 81, "2023-2024", "Acecook VN", 3, "Sản xuất probiotic từ vi khuẩn lactic" },
                    { "DT403", (byte)1, 20, 81, "2023-2024", "Sabeco", 4, "Tối ưu hóa quy trình lên men bia" },
                    { "DT404", (byte)2, 14, 81, "2023-2024", "Viện CNSH", 2, "Nghiên cứu enzyme cellulase từ vi sinh vật" },
                    { "DT405", (byte)1, 17, 81, "2023-2024", "Nestlé VN", 3, "Ứng dụng công nghệ nano trong bảo quản thực phẩm" },
                    { "DT406", (byte)2, 16, 82, "2023-2024", "Dược OPC", 2, "Chiết tách tinh dầu sả bằng CO₂ siêu tới hạn" },
                    { "DT407", (byte)1, 19, 82, "2023-2024", "Vifon", 4, "Sản xuất peptide kháng khuẩn từ đậu nành" },
                    { "DT408", (byte)2, 14, 82, "2023-2024", "Vedan VN", 3, "Ứng dụng vi sinh vật trong xử lý phế thải thực phẩm" },
                    { "DT409", (byte)1, 13, 82, "2023-2024", "Vinamilk", 2, "Tối ưu hóa sản xuất sữa chua uống" },
                    { "DT410", (byte)2, 17, 82, "2023-2024", "Bibica", 3, "Phát triển snack từ hạt ngũ cốc lên men" },
                    { "DT411", (byte)1, 18, 83, "2023-2024", "Minh Phú Seafood", 3, "Ứng dụng enzyme protease trong thủy sản" },
                    { "DT412", (byte)2, 15, 83, "2023-2024", "Marou Chocolate", 2, "Chiết xuất polyphenol từ vỏ ca cao" },
                    { "DT413", (byte)1, 20, 83, "2023-2024", "Pasteur Street Brewing", 4, "Tối ưu hóa quy trình sản xuất bia thủ công" },
                    { "DT414", (byte)2, 14, 83, "2023-2024", "Start-up FoodTech", 2, "Ứng dụng vi sinh vật lên men kombucha" },
                    { "DT415", (byte)1, 17, 83, "2023-2024", "Nutifood", 3, "Phát triển thực phẩm chức năng từ gạo lứt" },
                    { "DT416", (byte)2, 15, 84, "2023-2024", "Trung tâm CNSH TP.HCM", 2, "Chiết xuất carotenoid từ gấc" },
                    { "DT417", (byte)1, 18, 84, "2023-2024", "Yakult VN", 3, "Nghiên cứu vi sinh vật probiotic mới" },
                    { "DT418", (byte)2, 20, 84, "2023-2024", "Vinamit", 4, "Tối ưu quy trình làm khô đông lạnh trái cây" },
                    { "DT419", (byte)1, 16, 84, "2023-2024", "Start-up FoodTech", 2, "Phát triển sản phẩm kefir từ sữa hạt" },
                    { "DT420", (byte)2, 19, 84, "2023-2024", "VinEco", 3, "Ứng dụng công nghệ nano trong bảo quản rau quả" },
                    { "DT421", (byte)1, 17, 85, "2023-2024", "Viện CNSH", 3, "Sản xuất enzyme amylase từ vi sinh vật" },
                    { "DT422", (byte)2, 14, 85, "2023-2024", "Tân Hiệp Phát", 2, "Nghiên cứu quy trình sản xuất nước trái cây lên men" },
                    { "DT423", (byte)1, 16, 85, "2023-2024", "TH True Milk", 2, "Ứng dụng probiotic trong chế biến sữa chua" },
                    { "DT424", (byte)2, 12, 85, "2023-2024", "ABC Bakery", 2, "Tối ưu hóa quy trình sản xuất bánh mì" },
                    { "DT425", (byte)1, 18, 85, "2023-2024", "Nestlé VN", 3, "Phát triển sản phẩm thức uống từ thảo mộc" },
                    { "DT426", (byte)1, 14, 86, "2024-2025", "Trung tâm Quan trắc TNMT", 2, "Đánh giá chất lượng không khí TP.HCM bằng mô hình AQI" },
                    { "DT427", (byte)2, 18, 86, "2023-2024", "Sở TNMT TP.HCM", 3, "Ứng dụng IoT giám sát bụi mịn PM2.5" },
                    { "DT428", (byte)1, 16, 86, "2023-2024", "Viện Môi trường & Tài nguyên", 4, "Mô phỏng lan truyền khí thải giao thông bằng CALINE" },
                    { "DT429", (byte)2, 12, 86, "2023-2024", "Sở GTVT TP.HCM", 2, "Xây dựng bản đồ tiếng ồn đô thị bằng GIS" },
                    { "DT430", (byte)1, 15, 86, "2023-2024", "Sở Xây dựng TP.HCM", 3, "Đánh giá hiệu quả cây xanh trong giảm UHI" },
                    { "DT431", (byte)2, 19, 87, "2023-2024", "Sawaco", 4, "Thiết kế hệ thống xử lý nước thải sinh hoạt MBR" },
                    { "DT432", (byte)1, 17, 87, "2023-2024", "Khu đô thị Thủ Đức", 3, "Khử N–P bằng quy trình A2/O quy mô pilot" },
                    { "DT433", (byte)2, 14, 87, "2023-2024", "Viện KHCNMT", 2, "Hấp phụ kim loại nặng bằng biochar từ vỏ trấu" },
                    { "DT434", (byte)1, 18, 87, "2023-2024", "Quatest 3", 3, "Ứng dụng UV/ozon phân hủy thuốc trừ sâu" },
                    { "DT435", (byte)2, 16, 87, "2023-2024", "KCN Tân Bình", 2, "Tối ưu bể UASB xử lý nước thải thực phẩm" },
                    { "DT436", (byte)1, 20, 88, "2024-2025", "Doanh nghiệp Bao bì Xanh", 4, "Đánh giá vòng đời (LCA) cho sản phẩm nhựa sinh học" },
                    { "DT437", (byte)2, 15, 88, "2023-2024", "IUH – KHCNMT", 2, "Tính toán dấu chân carbon cho trường đại học" },
                    { "DT438", (byte)1, 18, 88, "2023-2024", "Sở TNMT TP.HCM", 3, "Giải pháp kinh tế tuần hoàn cho rác thải nhựa" },
                    { "DT439", (byte)2, 17, 88, "2023-2024", "RECOF Vietnam", 3, "Thiết kế mô hình thu hồi nhiệt trong nhà máy" },
                    { "DT440", (byte)1, 14, 88, "2023-2024", "Doanh nghiệp Công nghiệp", 2, "Báo cáo phát thải KNK theo ISO 14064" },
                    { "DT441", (byte)2, 13, 89, "2023-2024", "Sở TNMT tỉnh Đồng Nai", 2, "ĐTM cho dự án nhà máy dệt nhuộm" },
                    { "DT442", (byte)1, 16, 89, "2023-2024", "KCN VSIP", 3, "Quản lý môi trường khu công nghiệp theo ISO 14001" },
                    { "DT443", (byte)2, 19, 89, "2023-2024", "Sở TNMT TP.HCM", 4, "Quan trắc tự động nước thải và liên thông dữ liệu" },
                    { "DT444", (byte)1, 15, 89, "2023-2024", "Viện Môi trường & Tài nguyên", 2, "Đánh giá rủi ro môi trường theo phương pháp ERA" },
                    { "DT445", (byte)2, 12, 89, "2023-2024", "Ban QLDA Giao thông", 2, "Kế hoạch quản lý môi trường EMP cho dự án giao thông" },
                    { "DT446", (byte)1, 18, 90, "2018-2019", "Khu Dự trữ Sinh quyển Cần Giờ", 3, "Phục hồi rừng ngập mặn Cần Giờ – đánh giá đa dạng sinh học" },
                    { "DT447", (byte)2, 14, 90, "2023-2024", "Khu bảo tồn thiên nhiên Bình Châu", 2, "Giám sát đa dạng sinh học bằng bẫy ảnh" },
                    { "DT448", (byte)1, 17, 90, "2023-2024", "Viện Sinh thái & Tài nguyên", 3, "Mô hình sinh cảnh phù hợp cho chim nước bằng MaxEnt" },
                    { "DT449", (byte)2, 12, 90, "2023-2024", "Sở NN&PTNT", 2, "Đánh giá tác động xâm lấn của loài ngoại lai" },
                    { "DT450", (byte)1, 20, 90, "2023-2024", "Trung tâm PTN Sinh học", 4, "Ứng dụng DNA barcoding trong giám định loài" },
                    { "DT451", (byte)1, 22, 1, "2025-2026", "FPT Software", 3, "Nền tảng quản lý khóa học microservices (.NET + React)" },
                    { "DT452", (byte)1, 24, 1, "2025-2026", "VNG Cloud", 4, "Trợ lý học tập dùng LLM (RAG + Azure OpenAI)" },
                    { "DT453", (byte)1, 18, 1, "2025-2026", "NashTech VN", 3, "Hệ thống chấm bài lập trình tự động (Online Judge)" },
                    { "DT454", (byte)1, 17, 1, "2025-2026", "Viettel Solutions", 3, "Dashboard IoT giám sát phòng lab (MQTT + Timeseries DB)" },
                    { "DT455", (byte)1, 20, 1, "2025-2026", "VNPT Data", 4, "Phân tích dữ liệu sinh viên và dự báo rủi ro học tập (BI/ML)" },
                    { "DT456", (byte)1, 19, 1, "2025-2026", "Axon Active", 3, "Cổng tuyển sinh số đa kênh (Next.js + Keycloak SSO)" },
                    { "DT457", (byte)1, 21, 1, "2025-2026", "Zalo AI", 4, "Chatbot hỗ trợ sinh viên (RAG + Vector DB + LangChain)" },
                    { "DT458", (byte)1, 18, 1, "2025-2026", "TopCV", 3, "Nền tảng kết nối thực tập & việc làm (Matching + Recommender)" },
                    { "DT459", (byte)1, 23, 1, "2025-2026", "CMC Global", 4, "Đăng ký học phần chịu tải cao (CQRS + Event Sourcing)" },
                    { "DT460", (byte)1, 16, 1, "2025-2026", "VinAI", 3, "Điểm danh nhận diện khuôn mặt (Edge AI + ONNX)" }
                });

            migrationBuilder.InsertData(
                table: "HuongDan",
                columns: new[] { "madt", "masv", "ngaychapnhan", "ngaydangky", "ghichu", "ketqua", "magv", "trangthai" },
                values: new object[,]
                {
                    { "DT001", 1001, new DateTime(2024, 9, 10, 0, 0, 0, 0, DateTimeKind.Unspecified), new DateTime(2024, 9, 5, 0, 0, 0, 0, DateTimeKind.Unspecified), "Đã được chấp nhận tham gia", null, 1, (byte)1 },
                    { "DT002", 1001, null, new DateTime(2024, 9, 15, 0, 0, 0, 0, DateTimeKind.Unspecified), "Đang chờ giảng viên duyệt", null, 1, (byte)0 },
                    { "DT001", 1002, new DateTime(2024, 9, 8, 0, 0, 0, 0, DateTimeKind.Unspecified), new DateTime(2024, 9, 3, 0, 0, 0, 0, DateTimeKind.Unspecified), "Đã hoàn thành đề tài", 8.5m, 1, (byte)3 },
                    { "DT003", 1002, new DateTime(2024, 9, 18, 0, 0, 0, 0, DateTimeKind.Unspecified), new DateTime(2024, 9, 12, 0, 0, 0, 0, DateTimeKind.Unspecified), "Đang thực hiện đề tài", null, 2, (byte)2 },
                    { "DT004", 1003, null, new DateTime(2024, 9, 20, 0, 0, 0, 0, DateTimeKind.Unspecified), "Đề tài không phù hợp", null, 2, (byte)4 },
                    { "DT005", 1004, null, new DateTime(2024, 9, 25, 0, 0, 0, 0, DateTimeKind.Unspecified), "Sinh viên xin rút đăng ký", null, 3, (byte)5 }
                });

            migrationBuilder.CreateIndex(
                name: "IX_AppUser_code",
                table: "AppUser",
                column: "code");

            migrationBuilder.CreateIndex(
                name: "IX_DeTai_hocky",
                table: "DeTai",
                column: "hocky");

            migrationBuilder.CreateIndex(
                name: "IX_DeTai_magv_namhoc_hocky",
                table: "DeTai",
                columns: new[] { "magv", "namhoc", "hocky" });

            migrationBuilder.CreateIndex(
                name: "IX_DeTai_namhoc",
                table: "DeTai",
                column: "namhoc");

            migrationBuilder.CreateIndex(
                name: "IX_GiangVien_makhoa",
                table: "GiangVien",
                column: "makhoa");

            migrationBuilder.CreateIndex(
                name: "IX_HuongDan_madt_trangthai",
                table: "HuongDan",
                columns: new[] { "madt", "trangthai" });

            migrationBuilder.CreateIndex(
                name: "IX_HuongDan_magv",
                table: "HuongDan",
                column: "magv");

            migrationBuilder.CreateIndex(
                name: "IX_HuongDan_ngaydangky",
                table: "HuongDan",
                column: "ngaydangky");

            migrationBuilder.CreateIndex(
                name: "IX_HuongDan_trangthai",
                table: "HuongDan",
                column: "trangthai");

            migrationBuilder.CreateIndex(
                name: "IX_Khoa_makhoa",
                table: "Khoa",
                column: "makhoa",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_SinhVien_makhoa",
                table: "SinhVien",
                column: "makhoa");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "AppUser");

            migrationBuilder.DropTable(
                name: "HuongDan");

            migrationBuilder.DropTable(
                name: "DeTai");

            migrationBuilder.DropTable(
                name: "SinhVien");

            migrationBuilder.DropTable(
                name: "GiangVien");

            migrationBuilder.DropTable(
                name: "Khoa");
        }
    }
}
