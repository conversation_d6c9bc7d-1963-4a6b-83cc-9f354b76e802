﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace InternshipManagement.Migrations
{
    [DbContext(typeof(AppDbContext))]
    [Migration("20250917015128_Init")]
    partial class Init
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "9.0.8")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("InternshipManagement.Models.AppUser", b =>
                {
                    b.Property<string>("Code")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)")
                        .HasColumnName("code");

                    b.Property<int>("Role")
                        .HasColumnType("int");

                    b.Property<string>("PasswordHash")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("passwordhash");

                    b.HasKey("Code", "Role");

                    b.HasIndex("Code");

                    b.ToTable("AppUser");
                });

            modelBuilder.Entity("InternshipManagement.Models.DeTai", b =>
                {
                    b.Property<string>("MaDt")
                        .HasMaxLength(10)
                        .HasColumnType("char(10)")
                        .HasColumnName("madt");

                    b.Property<byte>("HocKy")
                        .HasColumnType("tinyint")
                        .HasColumnName("hocky");

                    b.Property<int?>("KinhPhi")
                        .HasColumnType("int")
                        .HasColumnName("kinhphi");

                    b.Property<int>("MaGv")
                        .HasColumnType("int")
                        .HasColumnName("magv");

                    b.Property<string>("NamHoc")
                        .IsRequired()
                        .HasMaxLength(9)
                        .HasColumnType("varchar(9)")
                        .HasColumnName("namhoc");

                    b.Property<string>("NoiThucTap")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasColumnName("noithucTap");

                    b.Property<int>("SoLuongToiDa")
                        .HasColumnType("int")
                        .HasColumnName("soluongtoida");

                    b.Property<string>("TenDt")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasColumnName("tendt");

                    b.HasKey("MaDt");

                    b.HasIndex("HocKy");

                    b.HasIndex("NamHoc");

                    b.HasIndex("MaGv", "NamHoc", "HocKy");

                    b.ToTable("DeTai");

                    b.HasData(
                        new
                        {
                            MaDt = "DT001",
                            HocKy = (byte)1,
                            KinhPhi = 10,
                            MaGv = 1,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Công ty FPT Software",
                            SoLuongToiDa = 2,
                            TenDt = "Hệ thống quản lý sinh viên"
                        },
                        new
                        {
                            MaDt = "DT002",
                            HocKy = (byte)1,
                            KinhPhi = 12,
                            MaGv = 1,
                            NamHoc = "2024-2025",
                            NoiThucTap = "Công ty VNPT",
                            SoLuongToiDa = 3,
                            TenDt = "Ứng dụng đặt lịch khám bệnh"
                        },
                        new
                        {
                            MaDt = "DT003",
                            HocKy = (byte)2,
                            KinhPhi = 15,
                            MaGv = 1,
                            NamHoc = "2022-2023",
                            NoiThucTap = "Công ty MISA",
                            SoLuongToiDa = 2,
                            TenDt = "Website thương mại điện tử"
                        },
                        new
                        {
                            MaDt = "DT004",
                            HocKy = (byte)2,
                            KinhPhi = 14,
                            MaGv = 1,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Công ty TMA Solutions",
                            SoLuongToiDa = 3,
                            TenDt = "Ứng dụng chat realtime"
                        },
                        new
                        {
                            MaDt = "DT005",
                            HocKy = (byte)1,
                            KinhPhi = 11,
                            MaGv = 1,
                            NamHoc = "2021-2022",
                            NoiThucTap = "Công ty Harvey Nash",
                            SoLuongToiDa = 4,
                            TenDt = "Hệ thống quản lý thư viện"
                        },
                        new
                        {
                            MaDt = "DT006",
                            HocKy = (byte)1,
                            KinhPhi = 16,
                            MaGv = 2,
                            NamHoc = "2024-2025",
                            NoiThucTap = "Công ty Viettel",
                            SoLuongToiDa = 3,
                            TenDt = "Ứng dụng học trực tuyến"
                        },
                        new
                        {
                            MaDt = "DT007",
                            HocKy = (byte)2,
                            KinhPhi = 18,
                            MaGv = 2,
                            NamHoc = "2022-2023",
                            NoiThucTap = "Công ty FPT IS",
                            SoLuongToiDa = 2,
                            TenDt = "Phần mềm quản lý khách sạn"
                        },
                        new
                        {
                            MaDt = "DT008",
                            HocKy = (byte)1,
                            KinhPhi = 14,
                            MaGv = 2,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Công ty NashTech",
                            SoLuongToiDa = 3,
                            TenDt = "Ứng dụng thương mại điện tử di động"
                        },
                        new
                        {
                            MaDt = "DT009",
                            HocKy = (byte)2,
                            KinhPhi = 20,
                            MaGv = 2,
                            NamHoc = "2024-2025",
                            NoiThucTap = "Công ty Zalo",
                            SoLuongToiDa = 4,
                            TenDt = "AI chatbot hỗ trợ khách hàng"
                        },
                        new
                        {
                            MaDt = "DT010",
                            HocKy = (byte)1,
                            KinhPhi = 12,
                            MaGv = 2,
                            NamHoc = "2021-2022",
                            NoiThucTap = "Công ty VNG",
                            SoLuongToiDa = 2,
                            TenDt = "Hệ thống quản lý tài chính cá nhân"
                        },
                        new
                        {
                            MaDt = "DT011",
                            HocKy = (byte)2,
                            KinhPhi = 15,
                            MaGv = 3,
                            NamHoc = "2022-2023",
                            NoiThucTap = "Công ty Axon Active",
                            SoLuongToiDa = 3,
                            TenDt = "Ứng dụng quản lý khóa học"
                        },
                        new
                        {
                            MaDt = "DT012",
                            HocKy = (byte)1,
                            KinhPhi = 19,
                            MaGv = 3,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Công ty VNPT",
                            SoLuongToiDa = 2,
                            TenDt = "Hệ thống điểm danh khuôn mặt"
                        },
                        new
                        {
                            MaDt = "DT013",
                            HocKy = (byte)2,
                            KinhPhi = 17,
                            MaGv = 3,
                            NamHoc = "2024-2025",
                            NoiThucTap = "Công ty Tiki",
                            SoLuongToiDa = 3,
                            TenDt = "Ứng dụng thương mại điện tử đa nền tảng"
                        },
                        new
                        {
                            MaDt = "DT014",
                            HocKy = (byte)1,
                            KinhPhi = 20,
                            MaGv = 3,
                            NamHoc = "2021-2022",
                            NoiThucTap = "Công ty CMC",
                            SoLuongToiDa = 4,
                            TenDt = "Hệ thống phân tích dữ liệu lớn"
                        },
                        new
                        {
                            MaDt = "DT015",
                            HocKy = (byte)2,
                            KinhPhi = 13,
                            MaGv = 3,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Công ty Momo",
                            SoLuongToiDa = 2,
                            TenDt = "Ứng dụng ngân hàng số"
                        },
                        new
                        {
                            MaDt = "DT016",
                            HocKy = (byte)1,
                            KinhPhi = 18,
                            MaGv = 4,
                            NamHoc = "2024-2025",
                            NoiThucTap = "Công ty FPT Software",
                            SoLuongToiDa = 3,
                            TenDt = "Ứng dụng quản lý nhân sự"
                        },
                        new
                        {
                            MaDt = "DT017",
                            HocKy = (byte)2,
                            KinhPhi = 14,
                            MaGv = 4,
                            NamHoc = "2022-2023",
                            NoiThucTap = "Công ty Viettel",
                            SoLuongToiDa = 4,
                            TenDt = "Hệ thống thương mại điện tử B2B"
                        },
                        new
                        {
                            MaDt = "DT018",
                            HocKy = (byte)1,
                            KinhPhi = 12,
                            MaGv = 4,
                            NamHoc = "2021-2022",
                            NoiThucTap = "Công ty Traveloka",
                            SoLuongToiDa = 2,
                            TenDt = "Ứng dụng du lịch thông minh"
                        },
                        new
                        {
                            MaDt = "DT019",
                            HocKy = (byte)2,
                            KinhPhi = 19,
                            MaGv = 4,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Công ty VNPT Technology",
                            SoLuongToiDa = 3,
                            TenDt = "Hệ thống IoT giám sát môi trường"
                        },
                        new
                        {
                            MaDt = "DT020",
                            HocKy = (byte)1,
                            KinhPhi = 16,
                            MaGv = 4,
                            NamHoc = "2024-2025",
                            NoiThucTap = "Công ty ZaloPay",
                            SoLuongToiDa = 2,
                            TenDt = "Ứng dụng fintech quản lý chi tiêu"
                        },
                        new
                        {
                            MaDt = "DT021",
                            HocKy = (byte)2,
                            KinhPhi = 11,
                            MaGv = 5,
                            NamHoc = "2022-2023",
                            NoiThucTap = "Công ty Topica",
                            SoLuongToiDa = 3,
                            TenDt = "Ứng dụng học tiếng Anh"
                        },
                        new
                        {
                            MaDt = "DT022",
                            HocKy = (byte)1,
                            KinhPhi = 18,
                            MaGv = 5,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Công ty Sendo",
                            SoLuongToiDa = 4,
                            TenDt = "Hệ thống thương mại điện tử cho SME"
                        },
                        new
                        {
                            MaDt = "DT023",
                            HocKy = (byte)2,
                            KinhPhi = 13,
                            MaGv = 5,
                            NamHoc = "2021-2022",
                            NoiThucTap = "Công ty GetFit",
                            SoLuongToiDa = 2,
                            TenDt = "Ứng dụng quản lý phòng gym"
                        },
                        new
                        {
                            MaDt = "DT024",
                            HocKy = (byte)1,
                            KinhPhi = 20,
                            MaGv = 5,
                            NamHoc = "2024-2025",
                            NoiThucTap = "Công ty Doctor Anywhere",
                            SoLuongToiDa = 3,
                            TenDt = "Hệ thống hỗ trợ tư vấn y tế"
                        },
                        new
                        {
                            MaDt = "DT025",
                            HocKy = (byte)2,
                            KinhPhi = 15,
                            MaGv = 5,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Công ty MB Bank",
                            SoLuongToiDa = 2,
                            TenDt = "Ứng dụng ngân hàng trực tuyến"
                        },
                        new
                        {
                            MaDt = "DT026",
                            HocKy = (byte)1,
                            KinhPhi = 14,
                            MaGv = 6,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Công ty Datalogic Việt Nam",
                            SoLuongToiDa = 3,
                            TenDt = "Thiết kế khuôn ép nhựa bằng CAD/CAM"
                        },
                        new
                        {
                            MaDt = "DT027",
                            HocKy = (byte)2,
                            KinhPhi = 18,
                            MaGv = 6,
                            NamHoc = "2022-2023",
                            NoiThucTap = "Bosch Việt Nam",
                            SoLuongToiDa = 2,
                            TenDt = "Tối ưu hóa quy trình gia công CNC 5 trục"
                        },
                        new
                        {
                            MaDt = "DT028",
                            HocKy = (byte)1,
                            KinhPhi = 12,
                            MaGv = 6,
                            NamHoc = "2021-2022",
                            NoiThucTap = "Schneider Electric",
                            SoLuongToiDa = 3,
                            TenDt = "Mô phỏng FEA cho chi tiết cơ khí mỏng"
                        },
                        new
                        {
                            MaDt = "DT029",
                            HocKy = (byte)2,
                            KinhPhi = 16,
                            MaGv = 6,
                            NamHoc = "2024-2025",
                            NoiThucTap = "Thaco Auto",
                            SoLuongToiDa = 4,
                            TenDt = "Thiết kế jig gá thông minh cho dây chuyền lắp ráp"
                        },
                        new
                        {
                            MaDt = "DT030",
                            HocKy = (byte)1,
                            KinhPhi = 10,
                            MaGv = 6,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Nidec Việt Nam",
                            SoLuongToiDa = 2,
                            TenDt = "Ứng dụng Lean Manufacturing trong xưởng tiện"
                        },
                        new
                        {
                            MaDt = "DT031",
                            HocKy = (byte)2,
                            KinhPhi = 19,
                            MaGv = 7,
                            NamHoc = "2022-2023",
                            NoiThucTap = "FPT Robotics",
                            SoLuongToiDa = 3,
                            TenDt = "Thiết kế robot SCARA gắp sản phẩm"
                        },
                        new
                        {
                            MaDt = "DT032",
                            HocKy = (byte)1,
                            KinhPhi = 13,
                            MaGv = 7,
                            NamHoc = "2024-2025",
                            NoiThucTap = "Công ty Vina CNC",
                            SoLuongToiDa = 2,
                            TenDt = "Quy hoạch bảo trì dự phòng cho máy CNC"
                        },
                        new
                        {
                            MaDt = "DT033",
                            HocKy = (byte)1,
                            KinhPhi = 11,
                            MaGv = 7,
                            NamHoc = "2021-2022",
                            NoiThucTap = "Nhựa Duy Tân",
                            SoLuongToiDa = 3,
                            TenDt = "Thiết kế truyền động đai cho băng tải công nghiệp"
                        },
                        new
                        {
                            MaDt = "DT034",
                            HocKy = (byte)2,
                            KinhPhi = 20,
                            MaGv = 7,
                            NamHoc = "2023-2024",
                            NoiThucTap = "VinFast",
                            SoLuongToiDa = 4,
                            TenDt = "Ứng dụng IoT giám sát rung động trục chính"
                        },
                        new
                        {
                            MaDt = "DT035",
                            HocKy = (byte)2,
                            KinhPhi = 15,
                            MaGv = 7,
                            NamHoc = "2024-2025",
                            NoiThucTap = "Cơ khí Hòa Phát",
                            SoLuongToiDa = 2,
                            TenDt = "Thiết kế hệ thống bôi trơn tập trung"
                        },
                        new
                        {
                            MaDt = "DT036",
                            HocKy = (byte)1,
                            KinhPhi = 12,
                            MaGv = 8,
                            NamHoc = "2022-2023",
                            NoiThucTap = "Yamazaki Mazak VN",
                            SoLuongToiDa = 3,
                            TenDt = "Tối ưu dao phay ngón cho hợp kim nhôm 6061"
                        },
                        new
                        {
                            MaDt = "DT037",
                            HocKy = (byte)2,
                            KinhPhi = 17,
                            MaGv = 8,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Saigon Precision",
                            SoLuongToiDa = 3,
                            TenDt = "Thiết kế mô-đun cấp phôi tự động"
                        },
                        new
                        {
                            MaDt = "DT038",
                            HocKy = (byte)1,
                            KinhPhi = 16,
                            MaGv = 8,
                            NamHoc = "2021-2022",
                            NoiThucTap = "PTSC M&C",
                            SoLuongToiDa = 2,
                            TenDt = "Phân tích mỏi chi tiết trục bằng ANSYS"
                        },
                        new
                        {
                            MaDt = "DT039",
                            HocKy = (byte)2,
                            KinhPhi = 14,
                            MaGv = 8,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Sunjin Vina",
                            SoLuongToiDa = 4,
                            TenDt = "Thiết kế cơ cấu cam cho máy dập"
                        },
                        new
                        {
                            MaDt = "DT040",
                            HocKy = (byte)2,
                            KinhPhi = 18,
                            MaGv = 8,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Mekamic",
                            SoLuongToiDa = 3,
                            TenDt = "Gia công tiên tiến bằng tia nước áp lực cao"
                        },
                        new
                        {
                            MaDt = "DT041",
                            HocKy = (byte)1,
                            KinhPhi = 20,
                            MaGv = 9,
                            NamHoc = "2024-2025",
                            NoiThucTap = "Workshop IUH Racing",
                            SoLuongToiDa = 4,
                            TenDt = "Thiết kế khung xe đua công thức sinh viên"
                        },
                        new
                        {
                            MaDt = "DT042",
                            HocKy = (byte)2,
                            KinhPhi = 13,
                            MaGv = 9,
                            NamHoc = "2020-2021",
                            NoiThucTap = "Thép Pomina",
                            SoLuongToiDa = 2,
                            TenDt = "Tối ưu hàn MIG cho thép tấm mỏng"
                        },
                        new
                        {
                            MaDt = "DT043",
                            HocKy = (byte)1,
                            KinhPhi = 15,
                            MaGv = 9,
                            NamHoc = "2019-2020",
                            NoiThucTap = "Cơ khí Sài Gòn",
                            SoLuongToiDa = 3,
                            TenDt = "Thiết kế hệ thống nâng hạ thủy lực"
                        },
                        new
                        {
                            MaDt = "DT044",
                            HocKy = (byte)2,
                            KinhPhi = 19,
                            MaGv = 9,
                            NamHoc = "2023-2024",
                            NoiThucTap = "R&D Ô tô Trường Hải",
                            SoLuongToiDa = 3,
                            TenDt = "Phân tích CFD luồng khí qua két nước"
                        },
                        new
                        {
                            MaDt = "DT045",
                            HocKy = (byte)1,
                            KinhPhi = 12,
                            MaGv = 9,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Công ty Ống Thép Hòa Phát",
                            SoLuongToiDa = 2,
                            TenDt = "Thiết kế máy uốn ống cỡ nhỏ"
                        },
                        new
                        {
                            MaDt = "DT046",
                            HocKy = (byte)1,
                            KinhPhi = 11,
                            MaGv = 10,
                            NamHoc = "2023-2024",
                            NoiThucTap = "ABB Việt Nam",
                            SoLuongToiDa = 2,
                            TenDt = "Ứng dụng PLC điều khiển máy dập cơ"
                        },
                        new
                        {
                            MaDt = "DT047",
                            HocKy = (byte)2,
                            KinhPhi = 18,
                            MaGv = 10,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Foster Electric",
                            SoLuongToiDa = 4,
                            TenDt = "Thiết kế dây chuyền lắp ráp bán tự động"
                        },
                        new
                        {
                            MaDt = "DT048",
                            HocKy = (byte)2,
                            KinhPhi = 16,
                            MaGv = 10,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Rorze Robotics",
                            SoLuongToiDa = 3,
                            TenDt = "Giảm rung cho trục quay tốc độ cao"
                        },
                        new
                        {
                            MaDt = "DT049",
                            HocKy = (byte)1,
                            KinhPhi = 13,
                            MaGv = 10,
                            NamHoc = "2023-2024",
                            NoiThucTap = "SumiRiko AVS",
                            SoLuongToiDa = 2,
                            TenDt = "Thiết kế cơ cấu cấp liệu vít tải"
                        },
                        new
                        {
                            MaDt = "DT050",
                            HocKy = (byte)2,
                            KinhPhi = 17,
                            MaGv = 10,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Daihatsu VN",
                            SoLuongToiDa = 3,
                            TenDt = "Quản lý chất lượng theo Six Sigma cho xưởng tiện"
                        },
                        new
                        {
                            MaDt = "DT051",
                            HocKy = (byte)1,
                            KinhPhi = 12,
                            MaGv = 11,
                            NamHoc = "2024-2025",
                            NoiThucTap = "Công ty Điện Quang",
                            SoLuongToiDa = 2,
                            TenDt = "Thiết kế mạch điều khiển LED thông minh"
                        },
                        new
                        {
                            MaDt = "DT052",
                            HocKy = (byte)2,
                            KinhPhi = 18,
                            MaGv = 11,
                            NamHoc = "2023-2024",
                            NoiThucTap = "EVN SPC",
                            SoLuongToiDa = 3,
                            TenDt = "Hệ thống giám sát điện năng bằng IoT"
                        },
                        new
                        {
                            MaDt = "DT053",
                            HocKy = (byte)1,
                            KinhPhi = 14,
                            MaGv = 11,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Công ty Điện tử Samco",
                            SoLuongToiDa = 2,
                            TenDt = "Thiết kế nguồn chuyển mạch công suất nhỏ"
                        },
                        new
                        {
                            MaDt = "DT054",
                            HocKy = (byte)2,
                            KinhPhi = 11,
                            MaGv = 11,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Trung tâm IoT Lab",
                            SoLuongToiDa = 3,
                            TenDt = "Ứng dụng Arduino điều khiển thiết bị điện"
                        },
                        new
                        {
                            MaDt = "DT055",
                            HocKy = (byte)1,
                            KinhPhi = 15,
                            MaGv = 11,
                            NamHoc = "2023-2024",
                            NoiThucTap = "SolarBK",
                            SoLuongToiDa = 4,
                            TenDt = "Thiết kế bộ sạc pin năng lượng mặt trời"
                        },
                        new
                        {
                            MaDt = "DT056",
                            HocKy = (byte)1,
                            KinhPhi = 13,
                            MaGv = 12,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Công ty TMA IoT",
                            SoLuongToiDa = 2,
                            TenDt = "Mạch cảm biến nhiệt độ và độ ẩm"
                        },
                        new
                        {
                            MaDt = "DT057",
                            HocKy = (byte)2,
                            KinhPhi = 17,
                            MaGv = 12,
                            NamHoc = "2023-2024",
                            NoiThucTap = "SHTP Labs",
                            SoLuongToiDa = 3,
                            TenDt = "Hệ thống đèn đường thông minh"
                        },
                        new
                        {
                            MaDt = "DT058",
                            HocKy = (byte)1,
                            KinhPhi = 19,
                            MaGv = 12,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Viettel R&D",
                            SoLuongToiDa = 4,
                            TenDt = "Thiết kế mạch RF cho IoT"
                        },
                        new
                        {
                            MaDt = "DT059",
                            HocKy = (byte)2,
                            KinhPhi = 14,
                            MaGv = 12,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Công ty AgriTech",
                            SoLuongToiDa = 2,
                            TenDt = "Ứng dụng ESP32 trong nông nghiệp"
                        },
                        new
                        {
                            MaDt = "DT060",
                            HocKy = (byte)1,
                            KinhPhi = 16,
                            MaGv = 12,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Công ty Savis",
                            SoLuongToiDa = 3,
                            TenDt = "Thiết kế hệ thống báo cháy tự động"
                        },
                        new
                        {
                            MaDt = "DT061",
                            HocKy = (byte)1,
                            KinhPhi = 20,
                            MaGv = 13,
                            NamHoc = "2024-2025",
                            NoiThucTap = "Công ty Điện mặt trời TTC",
                            SoLuongToiDa = 4,
                            TenDt = "Mạch nghịch lưu cho năng lượng tái tạo"
                        },
                        new
                        {
                            MaDt = "DT062",
                            HocKy = (byte)2,
                            KinhPhi = 15,
                            MaGv = 13,
                            NamHoc = "2023-2024",
                            NoiThucTap = "APC by Schneider",
                            SoLuongToiDa = 2,
                            TenDt = "Thiết kế hệ thống UPS cỡ nhỏ"
                        },
                        new
                        {
                            MaDt = "DT063",
                            HocKy = (byte)1,
                            KinhPhi = 11,
                            MaGv = 13,
                            NamHoc = "2023-2024",
                            NoiThucTap = "FPT Robotics",
                            SoLuongToiDa = 3,
                            TenDt = "Điều khiển động cơ DC bằng PWM"
                        },
                        new
                        {
                            MaDt = "DT064",
                            HocKy = (byte)2,
                            KinhPhi = 18,
                            MaGv = 13,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Samsung R&D VN",
                            SoLuongToiDa = 3,
                            TenDt = "Thiết kế mạch sạc nhanh USB Type-C"
                        },
                        new
                        {
                            MaDt = "DT065",
                            HocKy = (byte)1,
                            KinhPhi = 19,
                            MaGv = 13,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Synopsys VN",
                            SoLuongToiDa = 2,
                            TenDt = "Ứng dụng FPGA trong xử lý tín hiệu"
                        },
                        new
                        {
                            MaDt = "DT066",
                            HocKy = (byte)2,
                            KinhPhi = 17,
                            MaGv = 14,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Renesas VN",
                            SoLuongToiDa = 3,
                            TenDt = "Thiết kế mạch ADC/DAC tốc độ cao"
                        },
                        new
                        {
                            MaDt = "DT067",
                            HocKy = (byte)1,
                            KinhPhi = 12,
                            MaGv = 14,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Công ty Zalo AI",
                            SoLuongToiDa = 2,
                            TenDt = "Điều khiển thiết bị bằng giọng nói"
                        },
                        new
                        {
                            MaDt = "DT068",
                            HocKy = (byte)1,
                            KinhPhi = 10,
                            MaGv = 14,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Maker Lab HCM",
                            SoLuongToiDa = 3,
                            TenDt = "Mạch cảm biến hồng ngoại PIR"
                        },
                        new
                        {
                            MaDt = "DT069",
                            HocKy = (byte)2,
                            KinhPhi = 20,
                            MaGv = 14,
                            NamHoc = "2023-2024",
                            NoiThucTap = "VinAI",
                            SoLuongToiDa = 4,
                            TenDt = "Ứng dụng AI nhận diện khuôn mặt"
                        },
                        new
                        {
                            MaDt = "DT070",
                            HocKy = (byte)1,
                            KinhPhi = 14,
                            MaGv = 14,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Texas Instruments VN",
                            SoLuongToiDa = 2,
                            TenDt = "Thiết kế mạch nguồn DC-DC hiệu suất cao"
                        },
                        new
                        {
                            MaDt = "DT071",
                            HocKy = (byte)2,
                            KinhPhi = 13,
                            MaGv = 15,
                            NamHoc = "2020-2021",
                            NoiThucTap = "Công ty Điện lực HCMC",
                            SoLuongToiDa = 3,
                            TenDt = "Thiết kế hệ thống đo điện trở cách điện"
                        },
                        new
                        {
                            MaDt = "DT072",
                            HocKy = (byte)1,
                            KinhPhi = 15,
                            MaGv = 15,
                            NamHoc = "2023-2024",
                            NoiThucTap = "NI Vietnam",
                            SoLuongToiDa = 2,
                            TenDt = "Ứng dụng LabVIEW giám sát cảm biến"
                        },
                        new
                        {
                            MaDt = "DT073",
                            HocKy = (byte)2,
                            KinhPhi = 16,
                            MaGv = 15,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Philips Lighting VN",
                            SoLuongToiDa = 3,
                            TenDt = "Thiết kế hệ thống điều khiển chiếu sáng"
                        },
                        new
                        {
                            MaDt = "DT074",
                            HocKy = (byte)1,
                            KinhPhi = 18,
                            MaGv = 15,
                            NamHoc = "2023-2024",
                            NoiThucTap = "BKAV SmartHome",
                            SoLuongToiDa = 4,
                            TenDt = "Ứng dụng Zigbee trong nhà thông minh"
                        },
                        new
                        {
                            MaDt = "DT075",
                            HocKy = (byte)2,
                            KinhPhi = 19,
                            MaGv = 15,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Công ty Viettel Networks",
                            SoLuongToiDa = 2,
                            TenDt = "Thiết kế bộ khuếch đại công suất RF"
                        },
                        new
                        {
                            MaDt = "DT076",
                            HocKy = (byte)1,
                            KinhPhi = 12,
                            MaGv = 16,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Công ty Điện tử Việt Nhật",
                            SoLuongToiDa = 2,
                            TenDt = "Thiết kế mạch khuếch đại âm thanh công suất"
                        },
                        new
                        {
                            MaDt = "DT077",
                            HocKy = (byte)2,
                            KinhPhi = 18,
                            MaGv = 16,
                            NamHoc = "2023-2024",
                            NoiThucTap = "VNPT Technology",
                            SoLuongToiDa = 3,
                            TenDt = "Ứng dụng IoT trong hệ thống nhà thông minh"
                        },
                        new
                        {
                            MaDt = "DT078",
                            HocKy = (byte)1,
                            KinhPhi = 15,
                            MaGv = 16,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Công ty Điện tử Samco",
                            SoLuongToiDa = 2,
                            TenDt = "Thiết kế mạch dao động tần số cao"
                        },
                        new
                        {
                            MaDt = "DT079",
                            HocKy = (byte)2,
                            KinhPhi = 11,
                            MaGv = 16,
                            NamHoc = "2023-2024",
                            NoiThucTap = "BKAV SmartHome",
                            SoLuongToiDa = 3,
                            TenDt = "Xây dựng hệ thống báo trộm không dây"
                        },
                        new
                        {
                            MaDt = "DT080",
                            HocKy = (byte)1,
                            KinhPhi = 20,
                            MaGv = 16,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Viettel R&D",
                            SoLuongToiDa = 4,
                            TenDt = "Thiết kế bộ khuếch đại công suất RF"
                        },
                        new
                        {
                            MaDt = "DT081",
                            HocKy = (byte)1,
                            KinhPhi = 14,
                            MaGv = 17,
                            NamHoc = "2019-2020",
                            NoiThucTap = "Đài Tiếng nói Việt Nam",
                            SoLuongToiDa = 2,
                            TenDt = "Thiết kế mạch thu phát sóng FM"
                        },
                        new
                        {
                            MaDt = "DT082",
                            HocKy = (byte)2,
                            KinhPhi = 12,
                            MaGv = 17,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Maker Innovation Lab",
                            SoLuongToiDa = 3,
                            TenDt = "Ứng dụng cảm biến siêu âm đo khoảng cách"
                        },
                        new
                        {
                            MaDt = "DT083",
                            HocKy = (byte)1,
                            KinhPhi = 17,
                            MaGv = 17,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Synopsys Việt Nam",
                            SoLuongToiDa = 2,
                            TenDt = "Thiết kế mạch lọc số FIR"
                        },
                        new
                        {
                            MaDt = "DT084",
                            HocKy = (byte)2,
                            KinhPhi = 16,
                            MaGv = 17,
                            NamHoc = "2023-2024",
                            NoiThucTap = "EVN HCMC",
                            SoLuongToiDa = 4,
                            TenDt = "Hệ thống giám sát điện áp qua Internet"
                        },
                        new
                        {
                            MaDt = "DT085",
                            HocKy = (byte)1,
                            KinhPhi = 19,
                            MaGv = 17,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Texas Instruments VN",
                            SoLuongToiDa = 3,
                            TenDt = "Thiết kế bộ chuyển đổi ADC 12-bit"
                        },
                        new
                        {
                            MaDt = "DT086",
                            HocKy = (byte)2,
                            KinhPhi = 18,
                            MaGv = 18,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Công ty TMA Solutions",
                            SoLuongToiDa = 2,
                            TenDt = "Ứng dụng Raspberry Pi trong IoT"
                        },
                        new
                        {
                            MaDt = "DT087",
                            HocKy = (byte)1,
                            KinhPhi = 11,
                            MaGv = 18,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Công ty Điện Quang",
                            SoLuongToiDa = 3,
                            TenDt = "Thiết kế mạch cảm biến ánh sáng"
                        },
                        new
                        {
                            MaDt = "DT088",
                            HocKy = (byte)2,
                            KinhPhi = 13,
                            MaGv = 18,
                            NamHoc = "2023-2024",
                            NoiThucTap = "VNPT SmartHome",
                            SoLuongToiDa = 2,
                            TenDt = "Hệ thống khóa cửa thông minh RFID"
                        },
                        new
                        {
                            MaDt = "DT089",
                            HocKy = (byte)1,
                            KinhPhi = 16,
                            MaGv = 18,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Philips VN",
                            SoLuongToiDa = 4,
                            TenDt = "Thiết kế mạch công suất cho đèn LED"
                        },
                        new
                        {
                            MaDt = "DT090",
                            HocKy = (byte)2,
                            KinhPhi = 20,
                            MaGv = 18,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Hitachi VN",
                            SoLuongToiDa = 3,
                            TenDt = "Ứng dụng PLC trong điều khiển thang máy"
                        },
                        new
                        {
                            MaDt = "DT091",
                            HocKy = (byte)1,
                            KinhPhi = 19,
                            MaGv = 19,
                            NamHoc = "2024-2025",
                            NoiThucTap = "Ericsson VN",
                            SoLuongToiDa = 4,
                            TenDt = "Thiết kế anten vi dải cho 5G"
                        },
                        new
                        {
                            MaDt = "DT092",
                            HocKy = (byte)2,
                            KinhPhi = 15,
                            MaGv = 19,
                            NamHoc = "2023-2024",
                            NoiThucTap = "BKAV SmartHome",
                            SoLuongToiDa = 2,
                            TenDt = "Ứng dụng Zigbee trong nhà thông minh"
                        },
                        new
                        {
                            MaDt = "DT093",
                            HocKy = (byte)1,
                            KinhPhi = 12,
                            MaGv = 19,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Sony VN",
                            SoLuongToiDa = 3,
                            TenDt = "Thiết kế mạch lọc thông dải cho âm thanh"
                        },
                        new
                        {
                            MaDt = "DT094",
                            HocKy = (byte)2,
                            KinhPhi = 14,
                            MaGv = 19,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Công ty Savis",
                            SoLuongToiDa = 2,
                            TenDt = "Hệ thống cảnh báo cháy dùng cảm biến khói"
                        },
                        new
                        {
                            MaDt = "DT095",
                            HocKy = (byte)1,
                            KinhPhi = 20,
                            MaGv = 19,
                            NamHoc = "2023-2024",
                            NoiThucTap = "VinAI",
                            SoLuongToiDa = 4,
                            TenDt = "Ứng dụng AI trong xử lý ảnh y tế"
                        },
                        new
                        {
                            MaDt = "DT096",
                            HocKy = (byte)1,
                            KinhPhi = 16,
                            MaGv = 20,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Yamaha VN",
                            SoLuongToiDa = 3,
                            TenDt = "Thiết kế mạch khuếch đại âm thanh Hi-Fi"
                        },
                        new
                        {
                            MaDt = "DT097",
                            HocKy = (byte)2,
                            KinhPhi = 18,
                            MaGv = 20,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Intel Products VN",
                            SoLuongToiDa = 4,
                            TenDt = "Ứng dụng Bluetooth Low Energy trong IoT"
                        },
                        new
                        {
                            MaDt = "DT098",
                            HocKy = (byte)1,
                            KinhPhi = 19,
                            MaGv = 20,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Synopsys VN",
                            SoLuongToiDa = 2,
                            TenDt = "Thiết kế mạch số với FPGA"
                        },
                        new
                        {
                            MaDt = "DT099",
                            HocKy = (byte)2,
                            KinhPhi = 20,
                            MaGv = 20,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Zalo AI",
                            SoLuongToiDa = 3,
                            TenDt = "Ứng dụng AI trong nhận diện giọng nói"
                        },
                        new
                        {
                            MaDt = "DT100",
                            HocKy = (byte)1,
                            KinhPhi = 17,
                            MaGv = 20,
                            NamHoc = "2023-2024",
                            NoiThucTap = "FPT Robotics",
                            SoLuongToiDa = 2,
                            TenDt = "Thiết kế cảm biến siêu nhạy cho robot"
                        },
                        new
                        {
                            MaDt = "DT101",
                            HocKy = (byte)1,
                            KinhPhi = 20,
                            MaGv = 21,
                            NamHoc = "2024-2025",
                            NoiThucTap = "VinFast",
                            SoLuongToiDa = 3,
                            TenDt = "Thiết kế hệ thống truyền động ô tô điện"
                        },
                        new
                        {
                            MaDt = "DT102",
                            HocKy = (byte)2,
                            KinhPhi = 16,
                            MaGv = 21,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Thaco Auto",
                            SoLuongToiDa = 2,
                            TenDt = "Mô phỏng động cơ diesel bằng Matlab"
                        },
                        new
                        {
                            MaDt = "DT103",
                            HocKy = (byte)1,
                            KinhPhi = 15,
                            MaGv = 21,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Honda VN",
                            SoLuongToiDa = 3,
                            TenDt = "Hệ thống phanh ABS mô hình"
                        },
                        new
                        {
                            MaDt = "DT104",
                            HocKy = (byte)2,
                            KinhPhi = 18,
                            MaGv = 21,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Toyota VN",
                            SoLuongToiDa = 4,
                            TenDt = "Thiết kế hệ thống lái trợ lực điện"
                        },
                        new
                        {
                            MaDt = "DT105",
                            HocKy = (byte)1,
                            KinhPhi = 19,
                            MaGv = 21,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Mazda VN",
                            SoLuongToiDa = 2,
                            TenDt = "Mô hình hệ thống truyền lực hybrid"
                        },
                        new
                        {
                            MaDt = "DT106",
                            HocKy = (byte)2,
                            KinhPhi = 17,
                            MaGv = 22,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Isuzu VN",
                            SoLuongToiDa = 3,
                            TenDt = "Thiết kế hệ thống treo khí nén"
                        },
                        new
                        {
                            MaDt = "DT107",
                            HocKy = (byte)1,
                            KinhPhi = 20,
                            MaGv = 22,
                            NamHoc = "2023-2024",
                            NoiThucTap = "IUH Racing",
                            SoLuongToiDa = 4,
                            TenDt = "Mô phỏng khí động học xe đua"
                        },
                        new
                        {
                            MaDt = "DT108",
                            HocKy = (byte)2,
                            KinhPhi = 14,
                            MaGv = 22,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Suzuki VN",
                            SoLuongToiDa = 2,
                            TenDt = "Thiết kế hộp số tự động CVT"
                        },
                        new
                        {
                            MaDt = "DT109",
                            HocKy = (byte)1,
                            KinhPhi = 13,
                            MaGv = 22,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Ford VN",
                            SoLuongToiDa = 3,
                            TenDt = "Phân tích hệ thống xả giảm khí thải"
                        },
                        new
                        {
                            MaDt = "DT110",
                            HocKy = (byte)2,
                            KinhPhi = 18,
                            MaGv = 22,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Thaco Truck",
                            SoLuongToiDa = 2,
                            TenDt = "Ứng dụng IoT giám sát xe tải"
                        },
                        new
                        {
                            MaDt = "DT111",
                            HocKy = (byte)1,
                            KinhPhi = 15,
                            MaGv = 23,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Hyundai Thành Công",
                            SoLuongToiDa = 3,
                            TenDt = "Thiết kế hệ thống làm mát động cơ"
                        },
                        new
                        {
                            MaDt = "DT112",
                            HocKy = (byte)2,
                            KinhPhi = 16,
                            MaGv = 23,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Kia VN",
                            SoLuongToiDa = 4,
                            TenDt = "Mô phỏng động học hệ thống treo"
                        },
                        new
                        {
                            MaDt = "DT113",
                            HocKy = (byte)1,
                            KinhPhi = 12,
                            MaGv = 23,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Bosch VN",
                            SoLuongToiDa = 2,
                            TenDt = "Ứng dụng PLC trong điều khiển băng thử"
                        },
                        new
                        {
                            MaDt = "DT114",
                            HocKy = (byte)2,
                            KinhPhi = 18,
                            MaGv = 23,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Continental VN",
                            SoLuongToiDa = 3,
                            TenDt = "Thiết kế hệ thống khởi động thông minh"
                        },
                        new
                        {
                            MaDt = "DT115",
                            HocKy = (byte)1,
                            KinhPhi = 19,
                            MaGv = 23,
                            NamHoc = "2023-2024",
                            NoiThucTap = "R&D VinFast",
                            SoLuongToiDa = 2,
                            TenDt = "Phân tích rung động khung xe"
                        },
                        new
                        {
                            MaDt = "DT116",
                            HocKy = (byte)2,
                            KinhPhi = 20,
                            MaGv = 24,
                            NamHoc = "2024-2025",
                            NoiThucTap = "EVN E-Mobility",
                            SoLuongToiDa = 4,
                            TenDt = "Thiết kế hệ thống sạc nhanh cho ô tô điện"
                        },
                        new
                        {
                            MaDt = "DT117",
                            HocKy = (byte)1,
                            KinhPhi = 14,
                            MaGv = 24,
                            NamHoc = "2023-2024",
                            NoiThucTap = "VinAI",
                            SoLuongToiDa = 3,
                            TenDt = "Ứng dụng AI tối ưu tiêu hao nhiên liệu"
                        },
                        new
                        {
                            MaDt = "DT118",
                            HocKy = (byte)2,
                            KinhPhi = 17,
                            MaGv = 24,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Toyota VN",
                            SoLuongToiDa = 2,
                            TenDt = "Thiết kế hệ thống truyền động hybrid song song"
                        },
                        new
                        {
                            MaDt = "DT119",
                            HocKy = (byte)1,
                            KinhPhi = 15,
                            MaGv = 24,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Honda VN",
                            SoLuongToiDa = 3,
                            TenDt = "Mô phỏng động cơ xăng tăng áp"
                        },
                        new
                        {
                            MaDt = "DT120",
                            HocKy = (byte)2,
                            KinhPhi = 13,
                            MaGv = 24,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Mazda VN",
                            SoLuongToiDa = 2,
                            TenDt = "Thiết kế hệ thống trợ lực điện"
                        },
                        new
                        {
                            MaDt = "DT121",
                            HocKy = (byte)1,
                            KinhPhi = 16,
                            MaGv = 25,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Denso VN",
                            SoLuongToiDa = 3,
                            TenDt = "Thiết kế hệ thống điều hòa không khí ô tô"
                        },
                        new
                        {
                            MaDt = "DT122",
                            HocKy = (byte)2,
                            KinhPhi = 12,
                            MaGv = 25,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Bosch VN",
                            SoLuongToiDa = 2,
                            TenDt = "Ứng dụng cảm biến áp suất trong động cơ"
                        },
                        new
                        {
                            MaDt = "DT123",
                            HocKy = (byte)1,
                            KinhPhi = 20,
                            MaGv = 25,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Thaco Auto",
                            SoLuongToiDa = 4,
                            TenDt = "Mô phỏng CFD hệ thống nạp khí"
                        },
                        new
                        {
                            MaDt = "DT124",
                            HocKy = (byte)2,
                            KinhPhi = 18,
                            MaGv = 25,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Hyundai R&D",
                            SoLuongToiDa = 3,
                            TenDt = "Thiết kế hộp số ly hợp kép DCT"
                        },
                        new
                        {
                            MaDt = "DT125",
                            HocKy = (byte)1,
                            KinhPhi = 15,
                            MaGv = 25,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Sở GTVT HCM",
                            SoLuongToiDa = 2,
                            TenDt = "Ứng dụng IoT theo dõi xe buýt"
                        },
                        new
                        {
                            MaDt = "DT126",
                            HocKy = (byte)1,
                            KinhPhi = 18,
                            MaGv = 26,
                            NamHoc = "2024-2025",
                            NoiThucTap = "Searefico",
                            SoLuongToiDa = 3,
                            TenDt = "Thiết kế hệ thống lạnh công nghiệp"
                        },
                        new
                        {
                            MaDt = "DT127",
                            HocKy = (byte)2,
                            KinhPhi = 20,
                            MaGv = 26,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Satra Cold Storage",
                            SoLuongToiDa = 4,
                            TenDt = "Ứng dụng IoT giám sát kho lạnh"
                        },
                        new
                        {
                            MaDt = "DT128",
                            HocKy = (byte)1,
                            KinhPhi = 14,
                            MaGv = 26,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Công ty Vinamilk",
                            SoLuongToiDa = 2,
                            TenDt = "Mô phỏng chu trình lạnh NH3"
                        },
                        new
                        {
                            MaDt = "DT129",
                            HocKy = (byte)2,
                            KinhPhi = 15,
                            MaGv = 26,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Daikin VN",
                            SoLuongToiDa = 3,
                            TenDt = "Thiết kế hệ thống điều hòa VRV"
                        },
                        new
                        {
                            MaDt = "DT130",
                            HocKy = (byte)1,
                            KinhPhi = 19,
                            MaGv = 26,
                            NamHoc = "2023-2024",
                            NoiThucTap = "SolarBK",
                            SoLuongToiDa = 2,
                            TenDt = "Ứng dụng năng lượng mặt trời trong điều hòa"
                        },
                        new
                        {
                            MaDt = "DT131",
                            HocKy = (byte)2,
                            KinhPhi = 17,
                            MaGv = 27,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Minh Phú Seafood",
                            SoLuongToiDa = 3,
                            TenDt = "Thiết kế hệ thống cấp đông nhanh IQF"
                        },
                        new
                        {
                            MaDt = "DT132",
                            HocKy = (byte)1,
                            KinhPhi = 20,
                            MaGv = 27,
                            NamHoc = "2023-2024",
                            NoiThucTap = "CP Vietnam",
                            SoLuongToiDa = 4,
                            TenDt = "Mô phỏng chu trình lạnh CO2"
                        },
                        new
                        {
                            MaDt = "DT133",
                            HocKy = (byte)2,
                            KinhPhi = 16,
                            MaGv = 27,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Satra Foods",
                            SoLuongToiDa = 2,
                            TenDt = "Ứng dụng SCADA trong hệ thống lạnh"
                        },
                        new
                        {
                            MaDt = "DT134",
                            HocKy = (byte)1,
                            KinhPhi = 18,
                            MaGv = 27,
                            NamHoc = "2023-2024",
                            NoiThucTap = "VinFast",
                            SoLuongToiDa = 3,
                            TenDt = "Thiết kế hệ thống điều hòa ô tô điện"
                        },
                        new
                        {
                            MaDt = "DT135",
                            HocKy = (byte)2,
                            KinhPhi = 15,
                            MaGv = 27,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Bitzer VN",
                            SoLuongToiDa = 2,
                            TenDt = "Phân tích hiệu suất máy nén trục vít"
                        },
                        new
                        {
                            MaDt = "DT136",
                            HocKy = (byte)1,
                            KinhPhi = 20,
                            MaGv = 28,
                            NamHoc = "2024-2025",
                            NoiThucTap = "REE M&E",
                            SoLuongToiDa = 4,
                            TenDt = "Thiết kế hệ thống HVAC cho tòa nhà"
                        },
                        new
                        {
                            MaDt = "DT137",
                            HocKy = (byte)2,
                            KinhPhi = 14,
                            MaGv = 28,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Searefico",
                            SoLuongToiDa = 3,
                            TenDt = "Mô phỏng truyền nhiệt trong kho lạnh"
                        },
                        new
                        {
                            MaDt = "DT138",
                            HocKy = (byte)1,
                            KinhPhi = 19,
                            MaGv = 28,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Daikin VN",
                            SoLuongToiDa = 2,
                            TenDt = "Ứng dụng IoT giám sát HVAC"
                        },
                        new
                        {
                            MaDt = "DT139",
                            HocKy = (byte)2,
                            KinhPhi = 16,
                            MaGv = 28,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Mitsubishi Electric VN",
                            SoLuongToiDa = 3,
                            TenDt = "Thiết kế hệ thống bơm nhiệt Heat Pump"
                        },
                        new
                        {
                            MaDt = "DT140",
                            HocKy = (byte)1,
                            KinhPhi = 15,
                            MaGv = 28,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Panasonic VN",
                            SoLuongToiDa = 2,
                            TenDt = "Mô hình điều hòa không khí tiết kiệm năng lượng"
                        },
                        new
                        {
                            MaDt = "DT141",
                            HocKy = (byte)2,
                            KinhPhi = 17,
                            MaGv = 29,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Samco",
                            SoLuongToiDa = 3,
                            TenDt = "Thiết kế hệ thống điều hòa xe buýt"
                        },
                        new
                        {
                            MaDt = "DT142",
                            HocKy = (byte)1,
                            KinhPhi = 18,
                            MaGv = 29,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Tháp Nước Alpha",
                            SoLuongToiDa = 2,
                            TenDt = "Mô phỏng hiệu suất tháp giải nhiệt"
                        },
                        new
                        {
                            MaDt = "DT143",
                            HocKy = (byte)2,
                            KinhPhi = 20,
                            MaGv = 29,
                            NamHoc = "2023-2024",
                            NoiThucTap = "VinAI",
                            SoLuongToiDa = 4,
                            TenDt = "Ứng dụng AI dự đoán tiêu thụ điện năng"
                        },
                        new
                        {
                            MaDt = "DT144",
                            HocKy = (byte)1,
                            KinhPhi = 15,
                            MaGv = 29,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Maersk VN",
                            SoLuongToiDa = 2,
                            TenDt = "Thiết kế hệ thống lạnh container"
                        },
                        new
                        {
                            MaDt = "DT145",
                            HocKy = (byte)2,
                            KinhPhi = 19,
                            MaGv = 29,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Trane VN",
                            SoLuongToiDa = 3,
                            TenDt = "Mô phỏng hệ thống điều hòa trung tâm Chiller"
                        },
                        new
                        {
                            MaDt = "DT146",
                            HocKy = (byte)1,
                            KinhPhi = 14,
                            MaGv = 30,
                            NamHoc = "2023-2024",
                            NoiThucTap = "REE M&E",
                            SoLuongToiDa = 2,
                            TenDt = "Thiết kế hệ thống thông gió hầm để xe"
                        },
                        new
                        {
                            MaDt = "DT147",
                            HocKy = (byte)2,
                            KinhPhi = 16,
                            MaGv = 30,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Công ty Savis",
                            SoLuongToiDa = 3,
                            TenDt = "Ứng dụng BMS trong quản lý HVAC"
                        },
                        new
                        {
                            MaDt = "DT148",
                            HocKy = (byte)1,
                            KinhPhi = 18,
                            MaGv = 30,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Daikin VN",
                            SoLuongToiDa = 2,
                            TenDt = "Mô phỏng truyền nhiệt dàn trao đổi"
                        },
                        new
                        {
                            MaDt = "DT149",
                            HocKy = (byte)2,
                            KinhPhi = 19,
                            MaGv = 30,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Mitsubishi Electric VN",
                            SoLuongToiDa = 4,
                            TenDt = "Thiết kế hệ thống điều hòa VRF"
                        },
                        new
                        {
                            MaDt = "DT150",
                            HocKy = (byte)1,
                            KinhPhi = 20,
                            MaGv = 30,
                            NamHoc = "2023-2024",
                            NoiThucTap = "SolarBK",
                            SoLuongToiDa = 3,
                            TenDt = "Ứng dụng năng lượng tái tạo trong hệ thống lạnh"
                        },
                        new
                        {
                            MaDt = "DT151",
                            HocKy = (byte)1,
                            KinhPhi = 12,
                            MaGv = 31,
                            NamHoc = "2020-2021",
                            NoiThucTap = "Công ty Việt Tiến",
                            SoLuongToiDa = 2,
                            TenDt = "Thiết kế bộ sưu tập thời trang công sở"
                        },
                        new
                        {
                            MaDt = "DT152",
                            HocKy = (byte)2,
                            KinhPhi = 15,
                            MaGv = 31,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Công ty May 10",
                            SoLuongToiDa = 3,
                            TenDt = "Nghiên cứu vải tái chế trong may mặc"
                        },
                        new
                        {
                            MaDt = "DT153",
                            HocKy = (byte)1,
                            KinhPhi = 18,
                            MaGv = 31,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Ninomaxx",
                            SoLuongToiDa = 4,
                            TenDt = "Ứng dụng 3D trong thiết kế thời trang"
                        },
                        new
                        {
                            MaDt = "DT154",
                            HocKy = (byte)2,
                            KinhPhi = 14,
                            MaGv = 31,
                            NamHoc = "2023-2024",
                            NoiThucTap = "IVY Moda",
                            SoLuongToiDa = 2,
                            TenDt = "Phát triển thương hiệu thời trang bền vững"
                        },
                        new
                        {
                            MaDt = "DT155",
                            HocKy = (byte)1,
                            KinhPhi = 10,
                            MaGv = 31,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Nhà may Áo dài Minh Thư",
                            SoLuongToiDa = 3,
                            TenDt = "Thiết kế áo dài hiện đại"
                        },
                        new
                        {
                            MaDt = "DT156",
                            HocKy = (byte)1,
                            KinhPhi = 11,
                            MaGv = 32,
                            NamHoc = "2023-2024",
                            NoiThucTap = "May Nhà Bè",
                            SoLuongToiDa = 2,
                            TenDt = "Thiết kế đồng phục học sinh"
                        },
                        new
                        {
                            MaDt = "DT157",
                            HocKy = (byte)2,
                            KinhPhi = 17,
                            MaGv = 32,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Công ty Dệt Phong Phú",
                            SoLuongToiDa = 3,
                            TenDt = "Nghiên cứu ứng dụng vải kháng khuẩn"
                        },
                        new
                        {
                            MaDt = "DT158",
                            HocKy = (byte)1,
                            KinhPhi = 20,
                            MaGv = 32,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Công ty Faslink",
                            SoLuongToiDa = 4,
                            TenDt = "Ứng dụng AI trong thiết kế mẫu may"
                        },
                        new
                        {
                            MaDt = "DT159",
                            HocKy = (byte)2,
                            KinhPhi = 13,
                            MaGv = 32,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Sân khấu kịch Hồng Vân",
                            SoLuongToiDa = 2,
                            TenDt = "Thiết kế trang phục biểu diễn nghệ thuật"
                        },
                        new
                        {
                            MaDt = "DT160",
                            HocKy = (byte)1,
                            KinhPhi = 15,
                            MaGv = 32,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Công ty May Nhà Bè",
                            SoLuongToiDa = 3,
                            TenDt = "Quy trình may jacket xuất khẩu"
                        },
                        new
                        {
                            MaDt = "DT161",
                            HocKy = (byte)1,
                            KinhPhi = 16,
                            MaGv = 33,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Công ty Nike VN",
                            SoLuongToiDa = 2,
                            TenDt = "Thiết kế áo thể thao sử dụng vải co giãn"
                        },
                        new
                        {
                            MaDt = "DT162",
                            HocKy = (byte)2,
                            KinhPhi = 18,
                            MaGv = 33,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Công ty Dệt May Việt Thắng",
                            SoLuongToiDa = 3,
                            TenDt = "Nghiên cứu dệt nhuộm thân thiện môi trường"
                        },
                        new
                        {
                            MaDt = "DT163",
                            HocKy = (byte)1,
                            KinhPhi = 19,
                            MaGv = 33,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Công ty Leflair",
                            SoLuongToiDa = 4,
                            TenDt = "Ứng dụng Clo3D trong mô phỏng trang phục"
                        },
                        new
                        {
                            MaDt = "DT164",
                            HocKy = (byte)2,
                            KinhPhi = 14,
                            MaGv = 33,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Công ty Owen",
                            SoLuongToiDa = 2,
                            TenDt = "Thiết kế áo khoác dạ nữ cao cấp"
                        },
                        new
                        {
                            MaDt = "DT165",
                            HocKy = (byte)1,
                            KinhPhi = 12,
                            MaGv = 33,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Adidas VN",
                            SoLuongToiDa = 3,
                            TenDt = "Phân tích quy trình sản xuất giày thể thao"
                        },
                        new
                        {
                            MaDt = "DT166",
                            HocKy = (byte)2,
                            KinhPhi = 20,
                            MaGv = 34,
                            NamHoc = "2024-2025",
                            NoiThucTap = "NTK Lý Quý Khánh",
                            SoLuongToiDa = 4,
                            TenDt = "Thiết kế váy dạ hội"
                        },
                        new
                        {
                            MaDt = "DT167",
                            HocKy = (byte)1,
                            KinhPhi = 15,
                            MaGv = 34,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Công ty Dệt Kim Đông Xuân",
                            SoLuongToiDa = 2,
                            TenDt = "Nghiên cứu vật liệu vải không dệt"
                        },
                        new
                        {
                            MaDt = "DT168",
                            HocKy = (byte)2,
                            KinhPhi = 18,
                            MaGv = 34,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Công ty Faslink",
                            SoLuongToiDa = 3,
                            TenDt = "Ứng dụng Blockchain trong truy xuất nguồn gốc may mặc"
                        },
                        new
                        {
                            MaDt = "DT169",
                            HocKy = (byte)1,
                            KinhPhi = 12,
                            MaGv = 34,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Công ty Levi’s VN",
                            SoLuongToiDa = 2,
                            TenDt = "Thiết kế quần jeans thời trang"
                        },
                        new
                        {
                            MaDt = "DT170",
                            HocKy = (byte)2,
                            KinhPhi = 14,
                            MaGv = 34,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Công ty Việt Tiến",
                            SoLuongToiDa = 3,
                            TenDt = "Quy trình sản xuất áo sơ mi xuất khẩu"
                        },
                        new
                        {
                            MaDt = "DT171",
                            HocKy = (byte)1,
                            KinhPhi = 11,
                            MaGv = 35,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Nhà may Ngân An",
                            SoLuongToiDa = 2,
                            TenDt = "Thiết kế bộ sưu tập áo dài truyền thống"
                        },
                        new
                        {
                            MaDt = "DT172",
                            HocKy = (byte)2,
                            KinhPhi = 13,
                            MaGv = 35,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Công ty May Việt Tiến",
                            SoLuongToiDa = 3,
                            TenDt = "Nghiên cứu vải pha cotton"
                        },
                        new
                        {
                            MaDt = "DT173",
                            HocKy = (byte)1,
                            KinhPhi = 20,
                            MaGv = 35,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Công ty Leflair",
                            SoLuongToiDa = 4,
                            TenDt = "Ứng dụng AI phân tích xu hướng thời trang"
                        },
                        new
                        {
                            MaDt = "DT174",
                            HocKy = (byte)2,
                            KinhPhi = 15,
                            MaGv = 35,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Công ty An Phước",
                            SoLuongToiDa = 2,
                            TenDt = "Thiết kế đồng phục doanh nghiệp"
                        },
                        new
                        {
                            MaDt = "DT175",
                            HocKy = (byte)1,
                            KinhPhi = 19,
                            MaGv = 35,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Công ty Faslink",
                            SoLuongToiDa = 3,
                            TenDt = "Phát triển sản phẩm thời trang thông minh"
                        },
                        new
                        {
                            MaDt = "DT176",
                            HocKy = (byte)1,
                            KinhPhi = 18,
                            MaGv = 36,
                            NamHoc = "2024-2025",
                            NoiThucTap = "BASF Việt Nam",
                            SoLuongToiDa = 3,
                            TenDt = "Tổng hợp xúc tác nano cho phản ứng ester hóa"
                        },
                        new
                        {
                            MaDt = "DT177",
                            HocKy = (byte)2,
                            KinhPhi = 15,
                            MaGv = 36,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Vedan Việt Nam",
                            SoLuongToiDa = 2,
                            TenDt = "Xử lý nước thải nhuộm bằng vật liệu hấp phụ sinh học"
                        },
                        new
                        {
                            MaDt = "DT178",
                            HocKy = (byte)1,
                            KinhPhi = 20,
                            MaGv = 36,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Viện Hóa học – VAST",
                            SoLuongToiDa = 4,
                            TenDt = "Nghiên cứu pin kẽm–ion dung môi nước"
                        },
                        new
                        {
                            MaDt = "DT179",
                            HocKy = (byte)2,
                            KinhPhi = 12,
                            MaGv = 36,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Ajinomoto Việt Nam",
                            SoLuongToiDa = 3,
                            TenDt = "Tối ưu hóa quy trình chiết xuất pectin từ vỏ trái cây"
                        },
                        new
                        {
                            MaDt = "DT180",
                            HocKy = (byte)1,
                            KinhPhi = 16,
                            MaGv = 36,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Dow Việt Nam",
                            SoLuongToiDa = 2,
                            TenDt = "Tổng hợp polyme phân hủy sinh học trên cơ sở PLA"
                        },
                        new
                        {
                            MaDt = "DT181",
                            HocKy = (byte)2,
                            KinhPhi = 19,
                            MaGv = 37,
                            NamHoc = "2023-2024",
                            NoiThucTap = "PetroVietnam R&D",
                            SoLuongToiDa = 4,
                            TenDt = "Khảo sát đặc tính xúc tác zeolit trong cracking"
                        },
                        new
                        {
                            MaDt = "DT182",
                            HocKy = (byte)1,
                            KinhPhi = 17,
                            MaGv = 37,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Viện Môi trường & Tài nguyên",
                            SoLuongToiDa = 3,
                            TenDt = "Chế tạo màng lọc nano loại bỏ kim loại nặng"
                        },
                        new
                        {
                            MaDt = "DT183",
                            HocKy = (byte)2,
                            KinhPhi = 13,
                            MaGv = 37,
                            NamHoc = "2023-2024",
                            NoiThucTap = "UDEC – ĐH Bách Khoa HCM",
                            SoLuongToiDa = 2,
                            TenDt = "Sản xuất biodiesel từ dầu thải nhà hàng"
                        },
                        new
                        {
                            MaDt = "DT184",
                            HocKy = (byte)1,
                            KinhPhi = 11,
                            MaGv = 37,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Khoa học & Công nghệ TP.HCM",
                            SoLuongToiDa = 3,
                            TenDt = "Ứng dụng HPLC định lượng phụ gia thực phẩm"
                        },
                        new
                        {
                            MaDt = "DT185",
                            HocKy = (byte)2,
                            KinhPhi = 18,
                            MaGv = 37,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Viện Hóa học – VAST",
                            SoLuongToiDa = 3,
                            TenDt = "Tổng hợp MOF cho hấp phụ CO₂"
                        },
                        new
                        {
                            MaDt = "DT186",
                            HocKy = (byte)1,
                            KinhPhi = 16,
                            MaGv = 38,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Sơn TOA Việt Nam",
                            SoLuongToiDa = 2,
                            TenDt = "Chế tạo sơn kháng khuẩn dùng bạc nano"
                        },
                        new
                        {
                            MaDt = "DT187",
                            HocKy = (byte)2,
                            KinhPhi = 14,
                            MaGv = 38,
                            NamHoc = "2023-2024",
                            NoiThucTap = "3M Việt Nam",
                            SoLuongToiDa = 2,
                            TenDt = "Nghiên cứu keo dán thân thiện môi trường"
                        },
                        new
                        {
                            MaDt = "DT188",
                            HocKy = (byte)1,
                            KinhPhi = 20,
                            MaGv = 38,
                            NamHoc = "2023-2024",
                            NoiThucTap = "DHG Pharma",
                            SoLuongToiDa = 4,
                            TenDt = "Tổng hợp dược chất trung gian qua phản ứng Friedel–Crafts"
                        },
                        new
                        {
                            MaDt = "DT189",
                            HocKy = (byte)2,
                            KinhPhi = 12,
                            MaGv = 38,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Vinachem",
                            SoLuongToiDa = 3,
                            TenDt = "Tối ưu hóa quy trình tạo hạt phân bón NPK"
                        },
                        new
                        {
                            MaDt = "DT190",
                            HocKy = (byte)1,
                            KinhPhi = 18,
                            MaGv = 38,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Trung tâm Quatest 3",
                            SoLuongToiDa = 3,
                            TenDt = "Phân tích vi nhựa trong nước mặt bằng GC–MS"
                        },
                        new
                        {
                            MaDt = "DT191",
                            HocKy = (byte)2,
                            KinhPhi = 15,
                            MaGv = 39,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Công ty Dược OPC",
                            SoLuongToiDa = 2,
                            TenDt = "Chiết tách tinh dầu sả bằng CO₂ siêu tới hạn"
                        },
                        new
                        {
                            MaDt = "DT192",
                            HocKy = (byte)1,
                            KinhPhi = 13,
                            MaGv = 39,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Sawaco",
                            SoLuongToiDa = 2,
                            TenDt = "Xử lý Asen trong nước giếng khoan bằng vật liệu than hoạt tính"
                        },
                        new
                        {
                            MaDt = "DT193",
                            HocKy = (byte)2,
                            KinhPhi = 11,
                            MaGv = 39,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Perfetti Van Melle VN",
                            SoLuongToiDa = 3,
                            TenDt = "Tạo hương liệu tự nhiên từ phụ phẩm nông nghiệp"
                        },
                        new
                        {
                            MaDt = "DT194",
                            HocKy = (byte)1,
                            KinhPhi = 19,
                            MaGv = 39,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Viện Vật liệu – VAST",
                            SoLuongToiDa = 4,
                            TenDt = "Tổng hợp vật liệu perovskite cho pin mặt trời"
                        },
                        new
                        {
                            MaDt = "DT195",
                            HocKy = (byte)2,
                            KinhPhi = 17,
                            MaGv = 39,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Nhựa Bình Minh",
                            SoLuongToiDa = 3,
                            TenDt = "Đánh giá độ bền nhiệt polyme gia cường sợi thủy tinh"
                        },
                        new
                        {
                            MaDt = "DT196",
                            HocKy = (byte)1,
                            KinhPhi = 18,
                            MaGv = 40,
                            NamHoc = "2024-2025",
                            NoiThucTap = "Unilever Việt Nam",
                            SoLuongToiDa = 3,
                            TenDt = "Tổng hợp chất hoạt động bề mặt sinh học"
                        },
                        new
                        {
                            MaDt = "DT197",
                            HocKy = (byte)2,
                            KinhPhi = 12,
                            MaGv = 40,
                            NamHoc = "2023-2024",
                            NoiThucTap = "ACECOOK VN",
                            SoLuongToiDa = 2,
                            TenDt = "Đánh giá độ bền oxy hóa của dầu ăn tái sử dụng"
                        },
                        new
                        {
                            MaDt = "DT198",
                            HocKy = (byte)1,
                            KinhPhi = 20,
                            MaGv = 40,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Viện Hóa học – VAST",
                            SoLuongToiDa = 4,
                            TenDt = "Tổng hợp dung môi ion lỏng cho tách chiết cellulose"
                        },
                        new
                        {
                            MaDt = "DT199",
                            HocKy = (byte)2,
                            KinhPhi = 16,
                            MaGv = 40,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Tetra Pak VN",
                            SoLuongToiDa = 3,
                            TenDt = "Phát triển bao bì sinh học kháng khuẩn"
                        },
                        new
                        {
                            MaDt = "DT200",
                            HocKy = (byte)1,
                            KinhPhi = 14,
                            MaGv = 40,
                            NamHoc = "2023-2024",
                            NoiThucTap = "SABECO",
                            SoLuongToiDa = 2,
                            TenDt = "Quy trình sản xuất bia thủ công tối ưu hóa"
                        },
                        new
                        {
                            MaDt = "DT201",
                            HocKy = (byte)2,
                            KinhPhi = 11,
                            MaGv = 41,
                            NamHoc = "2023-2024",
                            NoiThucTap = "ĐH KHTN TP.HCM",
                            SoLuongToiDa = 2,
                            TenDt = "Mô hình hóa lan truyền dịch bệnh SIR"
                        },
                        new
                        {
                            MaDt = "DT202",
                            HocKy = (byte)1,
                            KinhPhi = 12,
                            MaGv = 41,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Viện Toán Ứng dụng",
                            SoLuongToiDa = 3,
                            TenDt = "Phân tích dữ liệu thống kê bằng R cho giáo dục"
                        },
                        new
                        {
                            MaDt = "DT203",
                            HocKy = (byte)2,
                            KinhPhi = 14,
                            MaGv = 41,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Viện Vật lý – VAST",
                            SoLuongToiDa = 2,
                            TenDt = "Mô phỏng cơ học lượng tử một chiều"
                        },
                        new
                        {
                            MaDt = "DT204",
                            HocKy = (byte)1,
                            KinhPhi = 13,
                            MaGv = 41,
                            NamHoc = "2023-2024",
                            NoiThucTap = "FPT Analytics",
                            SoLuongToiDa = 3,
                            TenDt = "Ứng dụng tối ưu hóa tuyến tính trong logistics"
                        },
                        new
                        {
                            MaDt = "DT205",
                            HocKy = (byte)2,
                            KinhPhi = 10,
                            MaGv = 41,
                            NamHoc = "2023-2024",
                            NoiThucTap = "NXB Giáo dục",
                            SoLuongToiDa = 2,
                            TenDt = "Xây dựng bộ công cụ học tập xác suất số"
                        },
                        new
                        {
                            MaDt = "DT206",
                            HocKy = (byte)1,
                            KinhPhi = 12,
                            MaGv = 42,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Trung tâm Thí nghiệm Vật lý",
                            SoLuongToiDa = 3,
                            TenDt = "Thí nghiệm giao thoa ánh sáng và ứng dụng"
                        },
                        new
                        {
                            MaDt = "DT207",
                            HocKy = (byte)2,
                            KinhPhi = 11,
                            MaGv = 42,
                            NamHoc = "2023-2024",
                            NoiThucTap = "ĐH KHTN TP.HCM",
                            SoLuongToiDa = 2,
                            TenDt = "Mô phỏng dao động tắt dần bằng Python"
                        },
                        new
                        {
                            MaDt = "DT208",
                            HocKy = (byte)1,
                            KinhPhi = 15,
                            MaGv = 42,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Đài Khí tượng Thủy văn",
                            SoLuongToiDa = 3,
                            TenDt = "Phân tích chuỗi thời gian khí tượng"
                        },
                        new
                        {
                            MaDt = "DT209",
                            HocKy = (byte)2,
                            KinhPhi = 14,
                            MaGv = 42,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Sở GD&ĐT TP.HCM",
                            SoLuongToiDa = 3,
                            TenDt = "Xây dựng mô hình hồi quy đa biến cho giáo dục"
                        },
                        new
                        {
                            MaDt = "DT210",
                            HocKy = (byte)1,
                            KinhPhi = 13,
                            MaGv = 42,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Viện Cơ học – VAST",
                            SoLuongToiDa = 2,
                            TenDt = "Thiết kế bộ thí nghiệm cơ học chất lưu đơn giản"
                        },
                        new
                        {
                            MaDt = "DT211",
                            HocKy = (byte)2,
                            KinhPhi = 18,
                            MaGv = 43,
                            NamHoc = "2019-2020",
                            NoiThucTap = "Bệnh viện Đại học Y Dược",
                            SoLuongToiDa = 4,
                            TenDt = "Ứng dụng thống kê Bayes trong y sinh"
                        },
                        new
                        {
                            MaDt = "DT212",
                            HocKy = (byte)1,
                            KinhPhi = 16,
                            MaGv = 43,
                            NamHoc = "2023-2024",
                            NoiThucTap = "EVN HCMC",
                            SoLuongToiDa = 3,
                            TenDt = "Mô hình toán cho dự báo nhu cầu điện"
                        },
                        new
                        {
                            MaDt = "DT213",
                            HocKy = (byte)2,
                            KinhPhi = 10,
                            MaGv = 43,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Trung tâm E-learning",
                            SoLuongToiDa = 2,
                            TenDt = "Thiết kế học liệu số môn Giải tích"
                        },
                        new
                        {
                            MaDt = "DT214",
                            HocKy = (byte)1,
                            KinhPhi = 12,
                            MaGv = 43,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Phòng Khảo thí & ĐBCL",
                            SoLuongToiDa = 3,
                            TenDt = "Phân tích dữ liệu khảo sát sinh viên bằng SPSS"
                        },
                        new
                        {
                            MaDt = "DT215",
                            HocKy = (byte)2,
                            KinhPhi = 14,
                            MaGv = 43,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Viện Nhiệt – Lạnh",
                            SoLuongToiDa = 2,
                            TenDt = "Mô phỏng truyền nhiệt thanh kim loại"
                        },
                        new
                        {
                            MaDt = "DT216",
                            HocKy = (byte)1,
                            KinhPhi = 11,
                            MaGv = 44,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Khoa KH Cơ bản",
                            SoLuongToiDa = 2,
                            TenDt = "Xây dựng bộ câu hỏi trắc nghiệm Vật lý 1"
                        },
                        new
                        {
                            MaDt = "DT217",
                            HocKy = (byte)2,
                            KinhPhi = 12,
                            MaGv = 44,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Trung tâm Khảo thí",
                            SoLuongToiDa = 3,
                            TenDt = "Thống kê suy luận cho dữ liệu giáo dục"
                        },
                        new
                        {
                            MaDt = "DT218",
                            HocKy = (byte)1,
                            KinhPhi = 15,
                            MaGv = 44,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Viện Toán Ứng dụng",
                            SoLuongToiDa = 3,
                            TenDt = "Mô hình Monte Carlo trong tài chính cơ bản"
                        },
                        new
                        {
                            MaDt = "DT219",
                            HocKy = (byte)2,
                            KinhPhi = 13,
                            MaGv = 44,
                            NamHoc = "2023-2024",
                            NoiThucTap = "ĐH Sư phạm Kỹ thuật",
                            SoLuongToiDa = 2,
                            TenDt = "Xây dựng mô hình dịch chuyển Brown"
                        },
                        new
                        {
                            MaDt = "DT220",
                            HocKy = (byte)1,
                            KinhPhi = 14,
                            MaGv = 44,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Phòng Thí nghiệm Vật lý",
                            SoLuongToiDa = 3,
                            TenDt = "Thiết kế bài thí nghiệm điện cơ bản cho năm nhất"
                        },
                        new
                        {
                            MaDt = "DT221",
                            HocKy = (byte)2,
                            KinhPhi = 10,
                            MaGv = 45,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Trung tâm CNTT IUH",
                            SoLuongToiDa = 2,
                            TenDt = "Ứng dụng Python trong dạy học xác suất"
                        },
                        new
                        {
                            MaDt = "DT222",
                            HocKy = (byte)1,
                            KinhPhi = 12,
                            MaGv = 45,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Khoa KH Cơ bản",
                            SoLuongToiDa = 3,
                            TenDt = "Phân tích dữ liệu khảo sát bằng phương pháp EFA"
                        },
                        new
                        {
                            MaDt = "DT223",
                            HocKy = (byte)2,
                            KinhPhi = 13,
                            MaGv = 45,
                            NamHoc = "2023-2024",
                            NoiThucTap = "ĐH KHTN TP.HCM",
                            SoLuongToiDa = 2,
                            TenDt = "Mô phỏng dao động điều hòa bằng MATLAB"
                        },
                        new
                        {
                            MaDt = "DT224",
                            HocKy = (byte)1,
                            KinhPhi = 11,
                            MaGv = 45,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Trung tâm E-learning",
                            SoLuongToiDa = 3,
                            TenDt = "Thiết kế học liệu tương tác môn Xác suất–Thống kê"
                        },
                        new
                        {
                            MaDt = "DT225",
                            HocKy = (byte)2,
                            KinhPhi = 15,
                            MaGv = 45,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Viện Toán Ứng dụng",
                            SoLuongToiDa = 3,
                            TenDt = "Ứng dụng mô hình Markov trong dự báo chuỗi thời gian"
                        },
                        new
                        {
                            MaDt = "DT226",
                            HocKy = (byte)1,
                            KinhPhi = 12,
                            MaGv = 46,
                            NamHoc = "2020-2021",
                            NoiThucTap = "Sở Công Thương TP.HCM",
                            SoLuongToiDa = 2,
                            TenDt = "Pháp luật về thương mại điện tử"
                        },
                        new
                        {
                            MaDt = "DT227",
                            HocKy = (byte)2,
                            KinhPhi = 15,
                            MaGv = 46,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Phòng Lao động – Thương binh & XH",
                            SoLuongToiDa = 3,
                            TenDt = "Luật lao động và quan hệ việc làm"
                        },
                        new
                        {
                            MaDt = "DT228",
                            HocKy = (byte)1,
                            KinhPhi = 18,
                            MaGv = 46,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Cục SHTT Việt Nam",
                            SoLuongToiDa = 4,
                            TenDt = "Quyền sở hữu trí tuệ trong khởi nghiệp"
                        },
                        new
                        {
                            MaDt = "DT229",
                            HocKy = (byte)2,
                            KinhPhi = 14,
                            MaGv = 46,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Tòa án ND TP.HCM",
                            SoLuongToiDa = 2,
                            TenDt = "Pháp luật về hợp đồng dân sự"
                        },
                        new
                        {
                            MaDt = "DT230",
                            HocKy = (byte)1,
                            KinhPhi = 16,
                            MaGv = 46,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Học viện Chính trị Quốc gia",
                            SoLuongToiDa = 3,
                            TenDt = "Pháp luật và chính trị quốc tế"
                        },
                        new
                        {
                            MaDt = "DT231",
                            HocKy = (byte)2,
                            KinhPhi = 12,
                            MaGv = 47,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Viện KSND TP.HCM",
                            SoLuongToiDa = 3,
                            TenDt = "Luật hình sự và cải cách tư pháp"
                        },
                        new
                        {
                            MaDt = "DT232",
                            HocKy = (byte)1,
                            KinhPhi = 17,
                            MaGv = 47,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Liên Hợp Quốc tại Việt Nam",
                            SoLuongToiDa = 2,
                            TenDt = "Quyền con người trong pháp luật quốc tế"
                        },
                        new
                        {
                            MaDt = "DT233",
                            HocKy = (byte)2,
                            KinhPhi = 18,
                            MaGv = 47,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Phòng Thương mại & Công nghiệp VN",
                            SoLuongToiDa = 4,
                            TenDt = "Luật kinh doanh và cạnh tranh"
                        },
                        new
                        {
                            MaDt = "DT234",
                            HocKy = (byte)1,
                            KinhPhi = 14,
                            MaGv = 47,
                            NamHoc = "2023-2024",
                            NoiThucTap = "UBND TP.HCM",
                            SoLuongToiDa = 2,
                            TenDt = "Luật hành chính và quản lý nhà nước"
                        },
                        new
                        {
                            MaDt = "DT235",
                            HocKy = (byte)2,
                            KinhPhi = 16,
                            MaGv = 47,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Sở TNMT TP.HCM",
                            SoLuongToiDa = 3,
                            TenDt = "Luật bảo vệ môi trường"
                        },
                        new
                        {
                            MaDt = "DT236",
                            HocKy = (byte)1,
                            KinhPhi = 15,
                            MaGv = 48,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Ngân hàng Nhà nước VN",
                            SoLuongToiDa = 2,
                            TenDt = "Luật tài chính – ngân hàng"
                        },
                        new
                        {
                            MaDt = "DT237",
                            HocKy = (byte)2,
                            KinhPhi = 18,
                            MaGv = 48,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Sở Giao dịch Chứng khoán TP.HCM",
                            SoLuongToiDa = 4,
                            TenDt = "Pháp luật về chứng khoán"
                        },
                        new
                        {
                            MaDt = "DT238",
                            HocKy = (byte)1,
                            KinhPhi = 14,
                            MaGv = 48,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Sở TNMT",
                            SoLuongToiDa = 3,
                            TenDt = "Luật đất đai và bất động sản"
                        },
                        new
                        {
                            MaDt = "DT239",
                            HocKy = (byte)2,
                            KinhPhi = 19,
                            MaGv = 48,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Công ty Luật YKVN",
                            SoLuongToiDa = 3,
                            TenDt = "Pháp luật về hợp đồng thương mại quốc tế"
                        },
                        new
                        {
                            MaDt = "DT240",
                            HocKy = (byte)1,
                            KinhPhi = 12,
                            MaGv = 48,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Thanh tra Chính phủ",
                            SoLuongToiDa = 2,
                            TenDt = "Pháp luật chống tham nhũng"
                        },
                        new
                        {
                            MaDt = "DT241",
                            HocKy = (byte)2,
                            KinhPhi = 13,
                            MaGv = 49,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Tòa án ND Tối cao",
                            SoLuongToiDa = 3,
                            TenDt = "Luật dân sự nâng cao"
                        },
                        new
                        {
                            MaDt = "DT242",
                            HocKy = (byte)1,
                            KinhPhi = 17,
                            MaGv = 49,
                            NamHoc = "2023-2024",
                            NoiThucTap = "WIPO Việt Nam",
                            SoLuongToiDa = 4,
                            TenDt = "Pháp luật về sở hữu trí tuệ quốc tế"
                        },
                        new
                        {
                            MaDt = "DT243",
                            HocKy = (byte)2,
                            KinhPhi = 15,
                            MaGv = 49,
                            NamHoc = "2023-2024",
                            NoiThucTap = "VIAC Việt Nam",
                            SoLuongToiDa = 2,
                            TenDt = "Luật hợp đồng và trọng tài thương mại"
                        },
                        new
                        {
                            MaDt = "DT244",
                            HocKy = (byte)1,
                            KinhPhi = 14,
                            MaGv = 49,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Hội Bảo vệ người tiêu dùng",
                            SoLuongToiDa = 3,
                            TenDt = "Pháp luật bảo vệ người tiêu dùng"
                        },
                        new
                        {
                            MaDt = "DT245",
                            HocKy = (byte)2,
                            KinhPhi = 16,
                            MaGv = 49,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Bộ Công an",
                            SoLuongToiDa = 4,
                            TenDt = "Luật an ninh mạng"
                        },
                        new
                        {
                            MaDt = "DT246",
                            HocKy = (byte)1,
                            KinhPhi = 12,
                            MaGv = 50,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Quốc hội VN",
                            SoLuongToiDa = 2,
                            TenDt = "Luật hiến pháp và quyền công dân"
                        },
                        new
                        {
                            MaDt = "DT247",
                            HocKy = (byte)2,
                            KinhPhi = 18,
                            MaGv = 50,
                            NamHoc = "2023-2024",
                            NoiThucTap = "IOM Việt Nam",
                            SoLuongToiDa = 3,
                            TenDt = "Pháp luật quốc tế về di cư"
                        },
                        new
                        {
                            MaDt = "DT248",
                            HocKy = (byte)1,
                            KinhPhi = 14,
                            MaGv = 50,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Ban thư ký ASEAN",
                            SoLuongToiDa = 3,
                            TenDt = "Pháp luật kinh tế ASEAN"
                        },
                        new
                        {
                            MaDt = "DT249",
                            HocKy = (byte)2,
                            KinhPhi = 19,
                            MaGv = 50,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Bộ Công Thương",
                            SoLuongToiDa = 4,
                            TenDt = "Luật thương mại quốc tế"
                        },
                        new
                        {
                            MaDt = "DT250",
                            HocKy = (byte)1,
                            KinhPhi = 13,
                            MaGv = 50,
                            NamHoc = "2023-2024",
                            NoiThucTap = "ĐH Luật TP.HCM",
                            SoLuongToiDa = 2,
                            TenDt = "Pháp luật dân sự so sánh"
                        },
                        new
                        {
                            MaDt = "DT251",
                            HocKy = (byte)1,
                            KinhPhi = 12,
                            MaGv = 51,
                            NamHoc = "2024-2025",
                            NoiThucTap = "British Council VN",
                            SoLuongToiDa = 2,
                            TenDt = "Phương pháp giảng dạy tiếng Anh giao tiếp"
                        },
                        new
                        {
                            MaDt = "DT252",
                            HocKy = (byte)2,
                            KinhPhi = 18,
                            MaGv = 51,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Duolingo VN",
                            SoLuongToiDa = 4,
                            TenDt = "Ứng dụng AI trong học từ vựng ngoại ngữ"
                        },
                        new
                        {
                            MaDt = "DT253",
                            HocKy = (byte)1,
                            KinhPhi = 14,
                            MaGv = 51,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Viện Khổng Tử",
                            SoLuongToiDa = 3,
                            TenDt = "Giáo trình tiếng Trung thương mại"
                        },
                        new
                        {
                            MaDt = "DT254",
                            HocKy = (byte)2,
                            KinhPhi = 15,
                            MaGv = 51,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Japan Foundation VN",
                            SoLuongToiDa = 2,
                            TenDt = "Phát triển năng lực nghe – nói tiếng Nhật"
                        },
                        new
                        {
                            MaDt = "DT255",
                            HocKy = (byte)1,
                            KinhPhi = 16,
                            MaGv = 51,
                            NamHoc = "2023-2024",
                            NoiThucTap = "FPT Software",
                            SoLuongToiDa = 3,
                            TenDt = "Dịch thuật Anh – Việt chuyên ngành CNTT"
                        },
                        new
                        {
                            MaDt = "DT256",
                            HocKy = (byte)1,
                            KinhPhi = 14,
                            MaGv = 52,
                            NamHoc = "2023-2024",
                            NoiThucTap = "British Council VN",
                            SoLuongToiDa = 3,
                            TenDt = "Phát triển kỹ năng viết học thuật tiếng Anh"
                        },
                        new
                        {
                            MaDt = "DT257",
                            HocKy = (byte)2,
                            KinhPhi = 16,
                            MaGv = 52,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Viện Pháp ngữ",
                            SoLuongToiDa = 2,
                            TenDt = "Ứng dụng e-learning trong giảng dạy tiếng Pháp"
                        },
                        new
                        {
                            MaDt = "DT258",
                            HocKy = (byte)1,
                            KinhPhi = 12,
                            MaGv = 52,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Trung tâm Hoa Văn Thăng Long",
                            SoLuongToiDa = 3,
                            TenDt = "Nghiên cứu phương pháp nghe – nói tiếng Trung"
                        },
                        new
                        {
                            MaDt = "DT259",
                            HocKy = (byte)2,
                            KinhPhi = 18,
                            MaGv = 52,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Duolingo VN",
                            SoLuongToiDa = 4,
                            TenDt = "Ứng dụng ChatGPT trong học ngoại ngữ"
                        },
                        new
                        {
                            MaDt = "DT260",
                            HocKy = (byte)1,
                            KinhPhi = 15,
                            MaGv = 52,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Công ty Dịch thuật Expertrans",
                            SoLuongToiDa = 2,
                            TenDt = "Biên – phiên dịch Anh – Việt thương mại"
                        },
                        new
                        {
                            MaDt = "DT261",
                            HocKy = (byte)1,
                            KinhPhi = 13,
                            MaGv = 53,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Japan Foundation VN",
                            SoLuongToiDa = 2,
                            TenDt = "Xây dựng học liệu tiếng Nhật cho du lịch"
                        },
                        new
                        {
                            MaDt = "DT262",
                            HocKy = (byte)2,
                            KinhPhi = 16,
                            MaGv = 53,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Sejong Center",
                            SoLuongToiDa = 3,
                            TenDt = "Phát triển năng lực giao tiếp tiếng Hàn"
                        },
                        new
                        {
                            MaDt = "DT263",
                            HocKy = (byte)1,
                            KinhPhi = 12,
                            MaGv = 53,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Apollo English",
                            SoLuongToiDa = 3,
                            TenDt = "Ứng dụng phim ảnh trong dạy tiếng Anh"
                        },
                        new
                        {
                            MaDt = "DT264",
                            HocKy = (byte)2,
                            KinhPhi = 17,
                            MaGv = 53,
                            NamHoc = "2023-2024",
                            NoiThucTap = "FPT Software",
                            SoLuongToiDa = 4,
                            TenDt = "Biên dịch tài liệu kỹ thuật Anh – Việt"
                        },
                        new
                        {
                            MaDt = "DT265",
                            HocKy = (byte)1,
                            KinhPhi = 18,
                            MaGv = 53,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Topica Native",
                            SoLuongToiDa = 2,
                            TenDt = "Ứng dụng gamification trong học ngoại ngữ"
                        },
                        new
                        {
                            MaDt = "DT266",
                            HocKy = (byte)2,
                            KinhPhi = 14,
                            MaGv = 54,
                            NamHoc = "2023-2024",
                            NoiThucTap = "ĐH KHXH&NV TP.HCM",
                            SoLuongToiDa = 3,
                            TenDt = "Nghiên cứu ngôn ngữ học so sánh Anh – Việt"
                        },
                        new
                        {
                            MaDt = "DT267",
                            HocKy = (byte)1,
                            KinhPhi = 15,
                            MaGv = 54,
                            NamHoc = "2023-2024",
                            NoiThucTap = "British Council VN",
                            SoLuongToiDa = 2,
                            TenDt = "Phương pháp giảng dạy tiếng Anh chuyên ngành"
                        },
                        new
                        {
                            MaDt = "DT268",
                            HocKy = (byte)2,
                            KinhPhi = 19,
                            MaGv = 54,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Duolingo VN",
                            SoLuongToiDa = 4,
                            TenDt = "Ứng dụng công nghệ AR trong học ngoại ngữ"
                        },
                        new
                        {
                            MaDt = "DT269",
                            HocKy = (byte)1,
                            KinhPhi = 12,
                            MaGv = 54,
                            NamHoc = "2023-2024",
                            NoiThucTap = "VUS",
                            SoLuongToiDa = 3,
                            TenDt = "Dạy – học từ vựng theo ngữ cảnh"
                        },
                        new
                        {
                            MaDt = "DT270",
                            HocKy = (byte)2,
                            KinhPhi = 16,
                            MaGv = 54,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Báo Tuổi Trẻ",
                            SoLuongToiDa = 2,
                            TenDt = "Biên dịch báo chí song ngữ Anh – Việt"
                        },
                        new
                        {
                            MaDt = "DT271",
                            HocKy = (byte)1,
                            KinhPhi = 12,
                            MaGv = 55,
                            NamHoc = "2023-2024",
                            NoiThucTap = "NXB Trẻ",
                            SoLuongToiDa = 3,
                            TenDt = "Nghiên cứu dịch văn học Anh – Việt"
                        },
                        new
                        {
                            MaDt = "DT272",
                            HocKy = (byte)2,
                            KinhPhi = 14,
                            MaGv = 55,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Topica Native",
                            SoLuongToiDa = 2,
                            TenDt = "Ứng dụng podcast trong học ngoại ngữ"
                        },
                        new
                        {
                            MaDt = "DT273",
                            HocKy = (byte)1,
                            KinhPhi = 16,
                            MaGv = 55,
                            NamHoc = "2023-2024",
                            NoiThucTap = "British Council VN",
                            SoLuongToiDa = 3,
                            TenDt = "Kỹ năng viết báo cáo khoa học tiếng Anh"
                        },
                        new
                        {
                            MaDt = "DT274",
                            HocKy = (byte)2,
                            KinhPhi = 18,
                            MaGv = 55,
                            NamHoc = "2023-2024",
                            NoiThucTap = "FPT Software",
                            SoLuongToiDa = 4,
                            TenDt = "Xây dựng từ điển song ngữ Việt – Anh ngành CNTT"
                        },
                        new
                        {
                            MaDt = "DT275",
                            HocKy = (byte)1,
                            KinhPhi = 20,
                            MaGv = 55,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Zalo AI",
                            SoLuongToiDa = 2,
                            TenDt = "Ứng dụng NLP trong dịch tự động"
                        },
                        new
                        {
                            MaDt = "DT276",
                            HocKy = (byte)1,
                            KinhPhi = 15,
                            MaGv = 56,
                            NamHoc = "2024-2025",
                            NoiThucTap = "Grab VN",
                            SoLuongToiDa = 2,
                            TenDt = "Chiến lược marketing cho startup"
                        },
                        new
                        {
                            MaDt = "DT277",
                            HocKy = (byte)2,
                            KinhPhi = 18,
                            MaGv = 56,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Talentnet",
                            SoLuongToiDa = 3,
                            TenDt = "Quản trị nguồn nhân lực trong kỷ nguyên số"
                        },
                        new
                        {
                            MaDt = "DT278",
                            HocKy = (byte)1,
                            KinhPhi = 14,
                            MaGv = 56,
                            NamHoc = "2023-2024",
                            NoiThucTap = "VNG",
                            SoLuongToiDa = 4,
                            TenDt = "Ứng dụng CRM trong quản lý khách hàng"
                        },
                        new
                        {
                            MaDt = "DT279",
                            HocKy = (byte)2,
                            KinhPhi = 16,
                            MaGv = 56,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Saigon Co.op",
                            SoLuongToiDa = 2,
                            TenDt = "Chiến lược cạnh tranh ngành bán lẻ"
                        },
                        new
                        {
                            MaDt = "DT280",
                            HocKy = (byte)1,
                            KinhPhi = 20,
                            MaGv = 56,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Shopee VN",
                            SoLuongToiDa = 3,
                            TenDt = "Ứng dụng AI trong phân tích thị trường"
                        },
                        new
                        {
                            MaDt = "DT281",
                            HocKy = (byte)2,
                            KinhPhi = 14,
                            MaGv = 57,
                            NamHoc = "2023-2024",
                            NoiThucTap = "BigC VN",
                            SoLuongToiDa = 3,
                            TenDt = "Quản trị chuỗi cung ứng bán lẻ"
                        },
                        new
                        {
                            MaDt = "DT282",
                            HocKy = (byte)1,
                            KinhPhi = 12,
                            MaGv = 57,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Haravan",
                            SoLuongToiDa = 2,
                            TenDt = "Phát triển thương hiệu cá nhân"
                        },
                        new
                        {
                            MaDt = "DT283",
                            HocKy = (byte)2,
                            KinhPhi = 18,
                            MaGv = 57,
                            NamHoc = "2023-2024",
                            NoiThucTap = "MISA",
                            SoLuongToiDa = 4,
                            TenDt = "Ứng dụng ERP trong doanh nghiệp nhỏ"
                        },
                        new
                        {
                            MaDt = "DT284",
                            HocKy = (byte)1,
                            KinhPhi = 15,
                            MaGv = 57,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Unilever VN",
                            SoLuongToiDa = 2,
                            TenDt = "Chiến lược định giá sản phẩm"
                        },
                        new
                        {
                            MaDt = "DT285",
                            HocKy = (byte)2,
                            KinhPhi = 16,
                            MaGv = 57,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Nielsen VN",
                            SoLuongToiDa = 3,
                            TenDt = "Phân tích hành vi người tiêu dùng"
                        },
                        new
                        {
                            MaDt = "DT286",
                            HocKy = (byte)1,
                            KinhPhi = 17,
                            MaGv = 58,
                            NamHoc = "2023-2024",
                            NoiThucTap = "PwC VN",
                            SoLuongToiDa = 2,
                            TenDt = "Quản trị tài chính doanh nghiệp"
                        },
                        new
                        {
                            MaDt = "DT287",
                            HocKy = (byte)2,
                            KinhPhi = 20,
                            MaGv = 58,
                            NamHoc = "2023-2024",
                            NoiThucTap = "KPMG VN",
                            SoLuongToiDa = 4,
                            TenDt = "Ứng dụng phân tích dữ liệu lớn trong kinh doanh"
                        },
                        new
                        {
                            MaDt = "DT288",
                            HocKy = (byte)1,
                            KinhPhi = 18,
                            MaGv = 58,
                            NamHoc = "2023-2024",
                            NoiThucTap = "VCCI",
                            SoLuongToiDa = 3,
                            TenDt = "Chiến lược mở rộng thị trường quốc tế"
                        },
                        new
                        {
                            MaDt = "DT289",
                            HocKy = (byte)2,
                            KinhPhi = 19,
                            MaGv = 58,
                            NamHoc = "2023-2024",
                            NoiThucTap = "VeChain VN",
                            SoLuongToiDa = 3,
                            TenDt = "Ứng dụng blockchain trong quản lý chuỗi cung ứng"
                        },
                        new
                        {
                            MaDt = "DT290",
                            HocKy = (byte)1,
                            KinhPhi = 15,
                            MaGv = 58,
                            NamHoc = "2023-2024",
                            NoiThucTap = "EY VN",
                            SoLuongToiDa = 2,
                            TenDt = "Quản trị rủi ro trong doanh nghiệp"
                        },
                        new
                        {
                            MaDt = "DT291",
                            HocKy = (byte)2,
                            KinhPhi = 14,
                            MaGv = 59,
                            NamHoc = "2023-2024",
                            NoiThucTap = "BK Holdings",
                            SoLuongToiDa = 2,
                            TenDt = "Khởi nghiệp và đổi mới sáng tạo"
                        },
                        new
                        {
                            MaDt = "DT292",
                            HocKy = (byte)1,
                            KinhPhi = 12,
                            MaGv = 59,
                            NamHoc = "2023-2024",
                            NoiThucTap = "SSI",
                            SoLuongToiDa = 3,
                            TenDt = "Phân tích báo cáo tài chính doanh nghiệp"
                        },
                        new
                        {
                            MaDt = "DT293",
                            HocKy = (byte)2,
                            KinhPhi = 18,
                            MaGv = 59,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Viettel Global",
                            SoLuongToiDa = 4,
                            TenDt = "Chiến lược kinh doanh quốc tế"
                        },
                        new
                        {
                            MaDt = "DT294",
                            HocKy = (byte)1,
                            KinhPhi = 13,
                            MaGv = 59,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Haravan",
                            SoLuongToiDa = 2,
                            TenDt = "Ứng dụng phân tích SWOT trong doanh nghiệp"
                        },
                        new
                        {
                            MaDt = "DT295",
                            HocKy = (byte)2,
                            KinhPhi = 17,
                            MaGv = 59,
                            NamHoc = "2023-2024",
                            NoiThucTap = "FPT IS",
                            SoLuongToiDa = 3,
                            TenDt = "Quản trị dự án CNTT"
                        },
                        new
                        {
                            MaDt = "DT296",
                            HocKy = (byte)1,
                            KinhPhi = 20,
                            MaGv = 60,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Google VN",
                            SoLuongToiDa = 4,
                            TenDt = "Chiến lược kinh doanh trong kỷ nguyên số"
                        },
                        new
                        {
                            MaDt = "DT297",
                            HocKy = (byte)2,
                            KinhPhi = 19,
                            MaGv = 60,
                            NamHoc = "2023-2024",
                            NoiThucTap = "HSBC VN",
                            SoLuongToiDa = 3,
                            TenDt = "Ứng dụng AI trong dự báo tài chính"
                        },
                        new
                        {
                            MaDt = "DT298",
                            HocKy = (byte)1,
                            KinhPhi = 16,
                            MaGv = 60,
                            NamHoc = "2023-2024",
                            NoiThucTap = "VCCI",
                            SoLuongToiDa = 2,
                            TenDt = "Quản trị chiến lược đa quốc gia"
                        },
                        new
                        {
                            MaDt = "DT299",
                            HocKy = (byte)2,
                            KinhPhi = 14,
                            MaGv = 60,
                            NamHoc = "2023-2024",
                            NoiThucTap = "HOSE",
                            SoLuongToiDa = 3,
                            TenDt = "Phân tích thị trường chứng khoán"
                        },
                        new
                        {
                            MaDt = "DT300",
                            HocKy = (byte)1,
                            KinhPhi = 18,
                            MaGv = 60,
                            NamHoc = "2023-2024",
                            NoiThucTap = "SAP VN",
                            SoLuongToiDa = 2,
                            TenDt = "Ứng dụng ERP trong doanh nghiệp lớn"
                        },
                        new
                        {
                            MaDt = "DT301",
                            HocKy = (byte)1,
                            KinhPhi = 18,
                            MaGv = 61,
                            NamHoc = "2024-2025",
                            NoiThucTap = "Sở Du lịch TP.HCM",
                            SoLuongToiDa = 3,
                            TenDt = "Chiến lược phát triển điểm đến du lịch thông minh"
                        },
                        new
                        {
                            MaDt = "DT302",
                            HocKy = (byte)2,
                            KinhPhi = 16,
                            MaGv = 61,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Saigontourist",
                            SoLuongToiDa = 2,
                            TenDt = "Ứng dụng chuyển đổi số trong lữ hành"
                        },
                        new
                        {
                            MaDt = "DT303",
                            HocKy = (byte)1,
                            KinhPhi = 12,
                            MaGv = 61,
                            NamHoc = "2023-2024",
                            NoiThucTap = "UBND Huyện Cần Giờ",
                            SoLuongToiDa = 3,
                            TenDt = "Phát triển sản phẩm du lịch cộng đồng"
                        },
                        new
                        {
                            MaDt = "DT304",
                            HocKy = (byte)2,
                            KinhPhi = 14,
                            MaGv = 61,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Vietravel",
                            SoLuongToiDa = 4,
                            TenDt = "Marketing số cho doanh nghiệp lữ hành vừa và nhỏ"
                        },
                        new
                        {
                            MaDt = "DT305",
                            HocKy = (byte)1,
                            KinhPhi = 15,
                            MaGv = 61,
                            NamHoc = "2023-2024",
                            NoiThucTap = "New World Saigon Hotel",
                            SoLuongToiDa = 2,
                            TenDt = "Quản trị trải nghiệm khách hàng khách sạn 4*"
                        },
                        new
                        {
                            MaDt = "DT306",
                            HocKy = (byte)1,
                            KinhPhi = 19,
                            MaGv = 62,
                            NamHoc = "2023-2024",
                            NoiThucTap = "SECC Quận 7",
                            SoLuongToiDa = 4,
                            TenDt = "Chuỗi cung ứng dịch vụ MICE tại TP.HCM"
                        },
                        new
                        {
                            MaDt = "DT307",
                            HocKy = (byte)2,
                            KinhPhi = 17,
                            MaGv = 62,
                            NamHoc = "2023-2024",
                            NoiThucTap = "InterContinental Saigon",
                            SoLuongToiDa = 3,
                            TenDt = "Tối ưu doanh thu phòng (Revenue Management)"
                        },
                        new
                        {
                            MaDt = "DT308",
                            HocKy = (byte)1,
                            KinhPhi = 13,
                            MaGv = 62,
                            NamHoc = "2023-2024",
                            NoiThucTap = "TST Tourist",
                            SoLuongToiDa = 2,
                            TenDt = "Ứng dụng CRM trong doanh nghiệp lữ hành"
                        },
                        new
                        {
                            MaDt = "DT309",
                            HocKy = (byte)2,
                            KinhPhi = 11,
                            MaGv = 62,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Nhà hàng Gạo",
                            SoLuongToiDa = 2,
                            TenDt = "Chuẩn hóa quy trình phục vụ nhà hàng"
                        },
                        new
                        {
                            MaDt = "DT310",
                            HocKy = (byte)1,
                            KinhPhi = 16,
                            MaGv = 62,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Agoda Việt Nam",
                            SoLuongToiDa = 3,
                            TenDt = "Ứng dụng đánh giá trực tuyến (OTA) trong nâng cao chất lượng"
                        },
                        new
                        {
                            MaDt = "DT311",
                            HocKy = (byte)1,
                            KinhPhi = 15,
                            MaGv = 63,
                            NamHoc = "2023-2024",
                            NoiThucTap = "BenThanh Tourist",
                            SoLuongToiDa = 2,
                            TenDt = "Phát triển tour du lịch hướng đến bền vững"
                        },
                        new
                        {
                            MaDt = "DT312",
                            HocKy = (byte)2,
                            KinhPhi = 12,
                            MaGv = 63,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Saigon Food Tour",
                            SoLuongToiDa = 3,
                            TenDt = "Thiết kế trải nghiệm ẩm thực trong city tour"
                        },
                        new
                        {
                            MaDt = "DT313",
                            HocKy = (byte)1,
                            KinhPhi = 18,
                            MaGv = 63,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Vietravel",
                            SoLuongToiDa = 4,
                            TenDt = "Chuyển đổi số trong điều hành tour"
                        },
                        new
                        {
                            MaDt = "DT314",
                            HocKy = (byte)2,
                            KinhPhi = 14,
                            MaGv = 63,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Trung tâm Xúc tiến Du lịch HCM",
                            SoLuongToiDa = 2,
                            TenDt = "Xây dựng thương hiệu điểm đến quận 1"
                        },
                        new
                        {
                            MaDt = "DT315",
                            HocKy = (byte)1,
                            KinhPhi = 20,
                            MaGv = 63,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Sở Du lịch TP.HCM",
                            SoLuongToiDa = 3,
                            TenDt = "Ứng dụng dữ liệu lớn dự báo lượng khách"
                        },
                        new
                        {
                            MaDt = "DT316",
                            HocKy = (byte)2,
                            KinhPhi = 13,
                            MaGv = 64,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Novotel Saigon Centre",
                            SoLuongToiDa = 2,
                            TenDt = "Thiết kế quy trình phục vụ buồng phòng chuẩn 4*"
                        },
                        new
                        {
                            MaDt = "DT317",
                            HocKy = (byte)1,
                            KinhPhi = 16,
                            MaGv = 64,
                            NamHoc = "2023-2024",
                            NoiThucTap = "AIG VN",
                            SoLuongToiDa = 3,
                            TenDt = "Quản trị rủi ro trong kinh doanh lữ hành"
                        },
                        new
                        {
                            MaDt = "DT318",
                            HocKy = (byte)2,
                            KinhPhi = 18,
                            MaGv = 64,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Klook Việt Nam",
                            SoLuongToiDa = 4,
                            TenDt = "Ứng dụng AI chatbot tư vấn tour"
                        },
                        new
                        {
                            MaDt = "DT319",
                            HocKy = (byte)1,
                            KinhPhi = 15,
                            MaGv = 64,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Hotel De Arts Saigon",
                            SoLuongToiDa = 2,
                            TenDt = "Chuẩn hóa quy trình check-in/out tự động"
                        },
                        new
                        {
                            MaDt = "DT320",
                            HocKy = (byte)2,
                            KinhPhi = 12,
                            MaGv = 64,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Riverside Hotel",
                            SoLuongToiDa = 3,
                            TenDt = "Thiết kế gói sản phẩm staycation cuối tuần"
                        },
                        new
                        {
                            MaDt = "DT321",
                            HocKy = (byte)1,
                            KinhPhi = 14,
                            MaGv = 65,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Park Hyatt Saigon",
                            SoLuongToiDa = 2,
                            TenDt = "Quản trị chất lượng dịch vụ spa trong khách sạn"
                        },
                        new
                        {
                            MaDt = "DT322",
                            HocKy = (byte)2,
                            KinhPhi = 13,
                            MaGv = 65,
                            NamHoc = "2023-2024",
                            NoiThucTap = "KDL Sinh thái Củ Chi",
                            SoLuongToiDa = 2,
                            TenDt = "Phát triển du lịch sinh thái tại Củ Chi"
                        },
                        new
                        {
                            MaDt = "DT323",
                            HocKy = (byte)1,
                            KinhPhi = 17,
                            MaGv = 65,
                            NamHoc = "2023-2024",
                            NoiThucTap = "TikTok VN",
                            SoLuongToiDa = 4,
                            TenDt = "Chiến lược truyền thông điểm đến số"
                        },
                        new
                        {
                            MaDt = "DT324",
                            HocKy = (byte)2,
                            KinhPhi = 12,
                            MaGv = 65,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Bảo tàng TP.HCM",
                            SoLuongToiDa = 3,
                            TenDt = "Thiết kế tour trải nghiệm văn hóa Chợ Lớn"
                        },
                        new
                        {
                            MaDt = "DT325",
                            HocKy = (byte)1,
                            KinhPhi = 19,
                            MaGv = 65,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Booking.com VN",
                            SoLuongToiDa = 3,
                            TenDt = "Ứng dụng dữ liệu OTA tối ưu giá phòng"
                        },
                        new
                        {
                            MaDt = "DT326",
                            HocKy = (byte)1,
                            KinhPhi = 18,
                            MaGv = 66,
                            NamHoc = "2018-2019",
                            NoiThucTap = "Công ty Coteccons",
                            SoLuongToiDa = 3,
                            TenDt = "Thiết kế kết cấu bê tông cốt thép nhà cao tầng"
                        },
                        new
                        {
                            MaDt = "DT327",
                            HocKy = (byte)2,
                            KinhPhi = 20,
                            MaGv = 66,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Hòa Bình Corp",
                            SoLuongToiDa = 4,
                            TenDt = "Ứng dụng BIM trong quản lý công trình"
                        },
                        new
                        {
                            MaDt = "DT328",
                            HocKy = (byte)1,
                            KinhPhi = 15,
                            MaGv = 66,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Công ty Cầu đường HCM",
                            SoLuongToiDa = 2,
                            TenDt = "Thiết kế cầu dầm liên tục bằng SAP2000"
                        },
                        new
                        {
                            MaDt = "DT329",
                            HocKy = (byte)2,
                            KinhPhi = 13,
                            MaGv = 66,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Delta Group",
                            SoLuongToiDa = 2,
                            TenDt = "Quản lý tiến độ thi công bằng MS Project"
                        },
                        new
                        {
                            MaDt = "DT330",
                            HocKy = (byte)1,
                            KinhPhi = 19,
                            MaGv = 66,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Viện KTXD TP.HCM",
                            SoLuongToiDa = 3,
                            TenDt = "Giải pháp kết cấu xanh cho đô thị bền vững"
                        },
                        new
                        {
                            MaDt = "DT331",
                            HocKy = (byte)2,
                            KinhPhi = 17,
                            MaGv = 67,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Công ty Steel Builder",
                            SoLuongToiDa = 3,
                            TenDt = "Thiết kế nhà công nghiệp khung thép"
                        },
                        new
                        {
                            MaDt = "DT332",
                            HocKy = (byte)1,
                            KinhPhi = 16,
                            MaGv = 67,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Công ty TT-As",
                            SoLuongToiDa = 2,
                            TenDt = "Ứng dụng Revit Architecture trong thiết kế"
                        },
                        new
                        {
                            MaDt = "DT333",
                            HocKy = (byte)2,
                            KinhPhi = 15,
                            MaGv = 67,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Sawaco",
                            SoLuongToiDa = 3,
                            TenDt = "Thiết kế hệ thống thoát nước đô thị"
                        },
                        new
                        {
                            MaDt = "DT334",
                            HocKy = (byte)1,
                            KinhPhi = 18,
                            MaGv = 67,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Công ty Cofico",
                            SoLuongToiDa = 4,
                            TenDt = "Quản lý chất lượng thi công công trình"
                        },
                        new
                        {
                            MaDt = "DT335",
                            HocKy = (byte)2,
                            KinhPhi = 19,
                            MaGv = 67,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Viện VLXD",
                            SoLuongToiDa = 3,
                            TenDt = "Nghiên cứu ứng dụng vật liệu mới trong xây dựng"
                        },
                        new
                        {
                            MaDt = "DT336",
                            HocKy = (byte)1,
                            KinhPhi = 14,
                            MaGv = 68,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Công ty An Phú Gia",
                            SoLuongToiDa = 2,
                            TenDt = "Thiết kế công trình dân dụng bằng Etabs"
                        },
                        new
                        {
                            MaDt = "DT337",
                            HocKy = (byte)2,
                            KinhPhi = 20,
                            MaGv = 68,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Viện Khoa học Thủy lợi",
                            SoLuongToiDa = 4,
                            TenDt = "Phân tích động đất công trình nhà cao tầng"
                        },
                        new
                        {
                            MaDt = "DT338",
                            HocKy = (byte)1,
                            KinhPhi = 17,
                            MaGv = 68,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Sở Xây dựng TP.HCM",
                            SoLuongToiDa = 3,
                            TenDt = "Ứng dụng GIS trong quy hoạch xây dựng"
                        },
                        new
                        {
                            MaDt = "DT339",
                            HocKy = (byte)2,
                            KinhPhi = 15,
                            MaGv = 68,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Công ty Cofico",
                            SoLuongToiDa = 2,
                            TenDt = "Giải pháp quản lý rủi ro trong thi công"
                        },
                        new
                        {
                            MaDt = "DT340",
                            HocKy = (byte)1,
                            KinhPhi = 13,
                            MaGv = 68,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Viện VLXD",
                            SoLuongToiDa = 3,
                            TenDt = "Thiết kế kết cấu gỗ công trình dân dụng"
                        },
                        new
                        {
                            MaDt = "DT341",
                            HocKy = (byte)1,
                            KinhPhi = 19,
                            MaGv = 69,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Viện Công nghệ VN",
                            SoLuongToiDa = 3,
                            TenDt = "Ứng dụng công nghệ 3D in bê tông"
                        },
                        new
                        {
                            MaDt = "DT342",
                            HocKy = (byte)2,
                            KinhPhi = 14,
                            MaGv = 69,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Sở GTVT TP.HCM",
                            SoLuongToiDa = 2,
                            TenDt = "Thiết kế công trình cầu vượt bộ hành"
                        },
                        new
                        {
                            MaDt = "DT343",
                            HocKy = (byte)1,
                            KinhPhi = 20,
                            MaGv = 69,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Công ty COTECCONS",
                            SoLuongToiDa = 4,
                            TenDt = "Ứng dụng AI trong quản lý dự án xây dựng"
                        },
                        new
                        {
                            MaDt = "DT344",
                            HocKy = (byte)2,
                            KinhPhi = 15,
                            MaGv = 69,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Công ty Tư vấn GTVT",
                            SoLuongToiDa = 2,
                            TenDt = "Thiết kế hệ thống giao thông đô thị"
                        },
                        new
                        {
                            MaDt = "DT345",
                            HocKy = (byte)1,
                            KinhPhi = 18,
                            MaGv = 69,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Viện VLXD",
                            SoLuongToiDa = 3,
                            TenDt = "Nghiên cứu vật liệu bê tông cường độ siêu cao"
                        },
                        new
                        {
                            MaDt = "DT346",
                            HocKy = (byte)2,
                            KinhPhi = 12,
                            MaGv = 70,
                            NamHoc = "2023-2024",
                            NoiThucTap = "HUD VN",
                            SoLuongToiDa = 2,
                            TenDt = "Thiết kế công trình nhà ở xã hội"
                        },
                        new
                        {
                            MaDt = "DT347",
                            HocKy = (byte)1,
                            KinhPhi = 15,
                            MaGv = 70,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Viện Địa kỹ thuật",
                            SoLuongToiDa = 3,
                            TenDt = "Phân tích ổn định mái dốc"
                        },
                        new
                        {
                            MaDt = "DT348",
                            HocKy = (byte)2,
                            KinhPhi = 19,
                            MaGv = 70,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Công ty COTECCONS",
                            SoLuongToiDa = 4,
                            TenDt = "Ứng dụng Lean Construction trong quản lý thi công"
                        },
                        new
                        {
                            MaDt = "DT349",
                            HocKy = (byte)1,
                            KinhPhi = 14,
                            MaGv = 70,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Sawaco",
                            SoLuongToiDa = 2,
                            TenDt = "Thiết kế hệ thống cấp thoát nước tòa nhà"
                        },
                        new
                        {
                            MaDt = "DT350",
                            HocKy = (byte)2,
                            KinhPhi = 17,
                            MaGv = 70,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Viện KTXD",
                            SoLuongToiDa = 3,
                            TenDt = "Mô phỏng kết cấu nhà cao tầng bằng Etabs"
                        },
                        new
                        {
                            MaDt = "DT351",
                            HocKy = (byte)1,
                            KinhPhi = 14,
                            MaGv = 71,
                            NamHoc = "2018-2019",
                            NoiThucTap = "KPMG VN",
                            SoLuongToiDa = 2,
                            TenDt = "Phân tích tài chính doanh nghiệp vừa và nhỏ"
                        },
                        new
                        {
                            MaDt = "DT352",
                            HocKy = (byte)2,
                            KinhPhi = 15,
                            MaGv = 71,
                            NamHoc = "2023-2024",
                            NoiThucTap = "PwC VN",
                            SoLuongToiDa = 3,
                            TenDt = "Ứng dụng kế toán quản trị trong DN sản xuất"
                        },
                        new
                        {
                            MaDt = "DT353",
                            HocKy = (byte)1,
                            KinhPhi = 13,
                            MaGv = 71,
                            NamHoc = "2023-2024",
                            NoiThucTap = "EY VN",
                            SoLuongToiDa = 2,
                            TenDt = "Kế toán chi phí và ra quyết định"
                        },
                        new
                        {
                            MaDt = "DT354",
                            HocKy = (byte)2,
                            KinhPhi = 18,
                            MaGv = 71,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Deloitte VN",
                            SoLuongToiDa = 4,
                            TenDt = "Ứng dụng IFRS trong báo cáo tài chính"
                        },
                        new
                        {
                            MaDt = "DT355",
                            HocKy = (byte)1,
                            KinhPhi = 16,
                            MaGv = 71,
                            NamHoc = "2023-2024",
                            NoiThucTap = "VietinBank",
                            SoLuongToiDa = 3,
                            TenDt = "Phân tích báo cáo hợp nhất"
                        },
                        new
                        {
                            MaDt = "DT356",
                            HocKy = (byte)1,
                            KinhPhi = 15,
                            MaGv = 72,
                            NamHoc = "2023-2024",
                            NoiThucTap = "ACCA VN",
                            SoLuongToiDa = 2,
                            TenDt = "Kế toán kiểm toán nội bộ"
                        },
                        new
                        {
                            MaDt = "DT357",
                            HocKy = (byte)2,
                            KinhPhi = 17,
                            MaGv = 72,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Ngân hàng BIDV",
                            SoLuongToiDa = 3,
                            TenDt = "Phân tích chi phí – lợi ích dự án"
                        },
                        new
                        {
                            MaDt = "DT358",
                            HocKy = (byte)1,
                            KinhPhi = 12,
                            MaGv = 72,
                            NamHoc = "2023-2024",
                            NoiThucTap = "MISA",
                            SoLuongToiDa = 2,
                            TenDt = "Ứng dụng phần mềm kế toán trong DN nhỏ"
                        },
                        new
                        {
                            MaDt = "DT359",
                            HocKy = (byte)2,
                            KinhPhi = 18,
                            MaGv = 72,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Vietcombank",
                            SoLuongToiDa = 4,
                            TenDt = "Quản trị tài chính doanh nghiệp"
                        },
                        new
                        {
                            MaDt = "DT360",
                            HocKy = (byte)1,
                            KinhPhi = 16,
                            MaGv = 72,
                            NamHoc = "2023-2024",
                            NoiThucTap = "MB Bank",
                            SoLuongToiDa = 3,
                            TenDt = "Phân tích rủi ro tín dụng"
                        },
                        new
                        {
                            MaDt = "DT361",
                            HocKy = (byte)2,
                            KinhPhi = 14,
                            MaGv = 73,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Deloitte VN",
                            SoLuongToiDa = 2,
                            TenDt = "Kế toán quản lý công nợ"
                        },
                        new
                        {
                            MaDt = "DT362",
                            HocKy = (byte)1,
                            KinhPhi = 19,
                            MaGv = 73,
                            NamHoc = "2023-2024",
                            NoiThucTap = "KPMG VN",
                            SoLuongToiDa = 3,
                            TenDt = "Ứng dụng AI trong kiểm toán"
                        },
                        new
                        {
                            MaDt = "DT363",
                            HocKy = (byte)2,
                            KinhPhi = 13,
                            MaGv = 73,
                            NamHoc = "2023-2024",
                            NoiThucTap = "EY VN",
                            SoLuongToiDa = 2,
                            TenDt = "Phân tích tình hình tài chính bằng Excel nâng cao"
                        },
                        new
                        {
                            MaDt = "DT364",
                            HocKy = (byte)1,
                            KinhPhi = 20,
                            MaGv = 73,
                            NamHoc = "2023-2024",
                            NoiThucTap = "PwC VN",
                            SoLuongToiDa = 4,
                            TenDt = "Báo cáo tài chính hợp nhất IFRS"
                        },
                        new
                        {
                            MaDt = "DT365",
                            HocKy = (byte)2,
                            KinhPhi = 15,
                            MaGv = 73,
                            NamHoc = "2023-2024",
                            NoiThucTap = "ACCA VN",
                            SoLuongToiDa = 3,
                            TenDt = "Kế toán quốc tế và hội nhập"
                        },
                        new
                        {
                            MaDt = "DT366",
                            HocKy = (byte)1,
                            KinhPhi = 18,
                            MaGv = 74,
                            NamHoc = "2023-2024",
                            NoiThucTap = "MISA",
                            SoLuongToiDa = 4,
                            TenDt = "Ứng dụng phần mềm phân tích dữ liệu kế toán"
                        },
                        new
                        {
                            MaDt = "DT367",
                            HocKy = (byte)2,
                            KinhPhi = 20,
                            MaGv = 74,
                            NamHoc = "2023-2024",
                            NoiThucTap = "KPMG VN",
                            SoLuongToiDa = 3,
                            TenDt = "Kiểm toán công nghệ thông tin"
                        },
                        new
                        {
                            MaDt = "DT368",
                            HocKy = (byte)1,
                            KinhPhi = 14,
                            MaGv = 74,
                            NamHoc = "2023-2024",
                            NoiThucTap = "VietinBank",
                            SoLuongToiDa = 2,
                            TenDt = "Phân tích dòng tiền trong dự án đầu tư"
                        },
                        new
                        {
                            MaDt = "DT369",
                            HocKy = (byte)2,
                            KinhPhi = 19,
                            MaGv = 74,
                            NamHoc = "2023-2024",
                            NoiThucTap = "HSBC VN",
                            SoLuongToiDa = 4,
                            TenDt = "Ứng dụng Big Data trong tài chính"
                        },
                        new
                        {
                            MaDt = "DT370",
                            HocKy = (byte)1,
                            KinhPhi = 15,
                            MaGv = 74,
                            NamHoc = "2023-2024",
                            NoiThucTap = "EY VN",
                            SoLuongToiDa = 3,
                            TenDt = "Báo cáo tài chính hợp nhất DN đa quốc gia"
                        },
                        new
                        {
                            MaDt = "DT371",
                            HocKy = (byte)2,
                            KinhPhi = 18,
                            MaGv = 75,
                            NamHoc = "2023-2024",
                            NoiThucTap = "HOSE",
                            SoLuongToiDa = 4,
                            TenDt = "Ứng dụng IFRS cho DN niêm yết"
                        },
                        new
                        {
                            MaDt = "DT372",
                            HocKy = (byte)1,
                            KinhPhi = 14,
                            MaGv = 75,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Cục Thuế TP.HCM",
                            SoLuongToiDa = 2,
                            TenDt = "Kế toán thuế trong DN FDI"
                        },
                        new
                        {
                            MaDt = "DT373",
                            HocKy = (byte)2,
                            KinhPhi = 19,
                            MaGv = 75,
                            NamHoc = "2023-2024",
                            NoiThucTap = "SAP VN",
                            SoLuongToiDa = 3,
                            TenDt = "Ứng dụng ERP trong kế toán tài chính"
                        },
                        new
                        {
                            MaDt = "DT374",
                            HocKy = (byte)1,
                            KinhPhi = 13,
                            MaGv = 75,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Hòa Bình Corp",
                            SoLuongToiDa = 2,
                            TenDt = "Phân tích báo cáo tài chính DN xây dựng"
                        },
                        new
                        {
                            MaDt = "DT375",
                            HocKy = (byte)2,
                            KinhPhi = 16,
                            MaGv = 75,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Deloitte VN",
                            SoLuongToiDa = 3,
                            TenDt = "Quản lý chi phí dự án bằng PM software"
                        },
                        new
                        {
                            MaDt = "DT376",
                            HocKy = (byte)1,
                            KinhPhi = 20,
                            MaGv = 76,
                            NamHoc = "2024-2025",
                            NoiThucTap = "ĐH Sunderland (UK)",
                            SoLuongToiDa = 4,
                            TenDt = "Chương trình MBA quốc tế – Quản trị chiến lược"
                        },
                        new
                        {
                            MaDt = "DT377",
                            HocKy = (byte)2,
                            KinhPhi = 15,
                            MaGv = 76,
                            NamHoc = "2023-2024",
                            NoiThucTap = "IUH – DTQT",
                            SoLuongToiDa = 2,
                            TenDt = "Đánh giá chất lượng đào tạo liên kết quốc tế"
                        },
                        new
                        {
                            MaDt = "DT378",
                            HocKy = (byte)1,
                            KinhPhi = 18,
                            MaGv = 76,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Coursera VN",
                            SoLuongToiDa = 3,
                            TenDt = "Ứng dụng AI trong quản lý học tập LMS"
                        },
                        new
                        {
                            MaDt = "DT379",
                            HocKy = (byte)2,
                            KinhPhi = 13,
                            MaGv = 76,
                            NamHoc = "2023-2024",
                            NoiThucTap = "British Council",
                            SoLuongToiDa = 2,
                            TenDt = "So sánh hệ thống giáo dục Anh – Việt"
                        },
                        new
                        {
                            MaDt = "DT380",
                            HocKy = (byte)1,
                            KinhPhi = 17,
                            MaGv = 76,
                            NamHoc = "2023-2024",
                            NoiThucTap = "IUH – DTQT",
                            SoLuongToiDa = 3,
                            TenDt = "Phát triển kỹ năng lãnh đạo toàn cầu"
                        },
                        new
                        {
                            MaDt = "DT381",
                            HocKy = (byte)1,
                            KinhPhi = 19,
                            MaGv = 77,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Maersk VN",
                            SoLuongToiDa = 3,
                            TenDt = "Quản trị chuỗi cung ứng toàn cầu"
                        },
                        new
                        {
                            MaDt = "DT382",
                            HocKy = (byte)2,
                            KinhPhi = 20,
                            MaGv = 77,
                            NamHoc = "2023-2024",
                            NoiThucTap = "FPT IS",
                            SoLuongToiDa = 4,
                            TenDt = "Ứng dụng Big Data trong quản lý đào tạo quốc tế"
                        },
                        new
                        {
                            MaDt = "DT383",
                            HocKy = (byte)1,
                            KinhPhi = 15,
                            MaGv = 77,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Fulbright VN",
                            SoLuongToiDa = 2,
                            TenDt = "So sánh chương trình MBA Mỹ và Châu Á"
                        },
                        new
                        {
                            MaDt = "DT384",
                            HocKy = (byte)2,
                            KinhPhi = 14,
                            MaGv = 77,
                            NamHoc = "2023-2024",
                            NoiThucTap = "ISO VN",
                            SoLuongToiDa = 3,
                            TenDt = "Quản lý chất lượng đào tạo theo ISO"
                        },
                        new
                        {
                            MaDt = "DT385",
                            HocKy = (byte)1,
                            KinhPhi = 16,
                            MaGv = 77,
                            NamHoc = "2023-2024",
                            NoiThucTap = "IUH – DTQT",
                            SoLuongToiDa = 2,
                            TenDt = "Ứng dụng mô hình e-learning blended learning"
                        },
                        new
                        {
                            MaDt = "DT386",
                            HocKy = (byte)2,
                            KinhPhi = 14,
                            MaGv = 78,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Ban thư ký ASEAN",
                            SoLuongToiDa = 3,
                            TenDt = "Hợp tác đào tạo quốc tế trong khối ASEAN"
                        },
                        new
                        {
                            MaDt = "DT387",
                            HocKy = (byte)1,
                            KinhPhi = 13,
                            MaGv = 78,
                            NamHoc = "2023-2024",
                            NoiThucTap = "ĐH Sunderland",
                            SoLuongToiDa = 2,
                            TenDt = "So sánh chương trình cử nhân quốc tế UK – VN"
                        },
                        new
                        {
                            MaDt = "DT388",
                            HocKy = (byte)2,
                            KinhPhi = 19,
                            MaGv = 78,
                            NamHoc = "2023-2024",
                            NoiThucTap = "EdX VN",
                            SoLuongToiDa = 4,
                            TenDt = "Ứng dụng AI trong giảng dạy online"
                        },
                        new
                        {
                            MaDt = "DT389",
                            HocKy = (byte)1,
                            KinhPhi = 12,
                            MaGv = 78,
                            NamHoc = "2023-2024",
                            NoiThucTap = "British Council",
                            SoLuongToiDa = 2,
                            TenDt = "Chính sách học bổng quốc tế và tác động"
                        },
                        new
                        {
                            MaDt = "DT390",
                            HocKy = (byte)2,
                            KinhPhi = 16,
                            MaGv = 78,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Cục Hợp tác quốc tế – Bộ GD&ĐT",
                            SoLuongToiDa = 3,
                            TenDt = "Quản lý du học sinh tại VN"
                        },
                        new
                        {
                            MaDt = "DT391",
                            HocKy = (byte)1,
                            KinhPhi = 15,
                            MaGv = 79,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Fulbright VN",
                            SoLuongToiDa = 2,
                            TenDt = "So sánh hệ thống quản trị ĐH Mỹ – VN"
                        },
                        new
                        {
                            MaDt = "DT392",
                            HocKy = (byte)2,
                            KinhPhi = 20,
                            MaGv = 79,
                            NamHoc = "2023-2024",
                            NoiThucTap = "IUH – DTQT",
                            SoLuongToiDa = 4,
                            TenDt = "Ứng dụng Blockchain trong xác thực bằng cấp"
                        },
                        new
                        {
                            MaDt = "DT393",
                            HocKy = (byte)1,
                            KinhPhi = 14,
                            MaGv = 79,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Viện Nghiên cứu GD",
                            SoLuongToiDa = 3,
                            TenDt = "Đánh giá chuẩn đầu ra sinh viên quốc tế"
                        },
                        new
                        {
                            MaDt = "DT394",
                            HocKy = (byte)2,
                            KinhPhi = 12,
                            MaGv = 79,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Japan Foundation",
                            SoLuongToiDa = 2,
                            TenDt = "So sánh kỹ năng mềm sinh viên VN – Nhật"
                        },
                        new
                        {
                            MaDt = "DT395",
                            HocKy = (byte)1,
                            KinhPhi = 18,
                            MaGv = 79,
                            NamHoc = "2023-2024",
                            NoiThucTap = "MOET VN",
                            SoLuongToiDa = 3,
                            TenDt = "Ứng dụng AI trong kiểm định chất lượng GDQT"
                        },
                        new
                        {
                            MaDt = "DT396",
                            HocKy = (byte)1,
                            KinhPhi = 18,
                            MaGv = 80,
                            NamHoc = "2023-2024",
                            NoiThucTap = "AUN-QA Việt Nam",
                            SoLuongToiDa = 3,
                            TenDt = "Chuẩn hóa kiểm định quốc tế AUN-QA cho CT liên kết"
                        },
                        new
                        {
                            MaDt = "DT397",
                            HocKy = (byte)2,
                            KinhPhi = 19,
                            MaGv = 80,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Coursera VN",
                            SoLuongToiDa = 4,
                            TenDt = "Chiến lược micro-credential và công nhận tín chỉ"
                        },
                        new
                        {
                            MaDt = "DT398",
                            HocKy = (byte)1,
                            KinhPhi = 14,
                            MaGv = 80,
                            NamHoc = "2023-2024",
                            NoiThucTap = "IUH – DTQT",
                            SoLuongToiDa = 2,
                            TenDt = "Hệ thống proctoring trực tuyến và đạo đức học thuật"
                        },
                        new
                        {
                            MaDt = "DT399",
                            HocKy = (byte)2,
                            KinhPhi = 20,
                            MaGv = 80,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Turnitin VN",
                            SoLuongToiDa = 4,
                            TenDt = "Ứng dụng học máy phát hiện đạo văn đa ngôn ngữ"
                        },
                        new
                        {
                            MaDt = "DT400",
                            HocKy = (byte)1,
                            KinhPhi = 16,
                            MaGv = 80,
                            NamHoc = "2023-2024",
                            NoiThucTap = "ASEAN University Network",
                            SoLuongToiDa = 3,
                            TenDt = "Tối ưu quy trình trao đổi SV quốc tế dựa trên dữ liệu"
                        },
                        new
                        {
                            MaDt = "DT401",
                            HocKy = (byte)1,
                            KinhPhi = 15,
                            MaGv = 81,
                            NamHoc = "2018-2019",
                            NoiThucTap = "Vinamilk",
                            SoLuongToiDa = 2,
                            TenDt = "Chiết xuất hợp chất chống oxy hóa từ trà xanh"
                        },
                        new
                        {
                            MaDt = "DT402",
                            HocKy = (byte)2,
                            KinhPhi = 18,
                            MaGv = 81,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Acecook VN",
                            SoLuongToiDa = 3,
                            TenDt = "Sản xuất probiotic từ vi khuẩn lactic"
                        },
                        new
                        {
                            MaDt = "DT403",
                            HocKy = (byte)1,
                            KinhPhi = 20,
                            MaGv = 81,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Sabeco",
                            SoLuongToiDa = 4,
                            TenDt = "Tối ưu hóa quy trình lên men bia"
                        },
                        new
                        {
                            MaDt = "DT404",
                            HocKy = (byte)2,
                            KinhPhi = 14,
                            MaGv = 81,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Viện CNSH",
                            SoLuongToiDa = 2,
                            TenDt = "Nghiên cứu enzyme cellulase từ vi sinh vật"
                        },
                        new
                        {
                            MaDt = "DT405",
                            HocKy = (byte)1,
                            KinhPhi = 17,
                            MaGv = 81,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Nestlé VN",
                            SoLuongToiDa = 3,
                            TenDt = "Ứng dụng công nghệ nano trong bảo quản thực phẩm"
                        },
                        new
                        {
                            MaDt = "DT406",
                            HocKy = (byte)2,
                            KinhPhi = 16,
                            MaGv = 82,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Dược OPC",
                            SoLuongToiDa = 2,
                            TenDt = "Chiết tách tinh dầu sả bằng CO₂ siêu tới hạn"
                        },
                        new
                        {
                            MaDt = "DT407",
                            HocKy = (byte)1,
                            KinhPhi = 19,
                            MaGv = 82,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Vifon",
                            SoLuongToiDa = 4,
                            TenDt = "Sản xuất peptide kháng khuẩn từ đậu nành"
                        },
                        new
                        {
                            MaDt = "DT408",
                            HocKy = (byte)2,
                            KinhPhi = 14,
                            MaGv = 82,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Vedan VN",
                            SoLuongToiDa = 3,
                            TenDt = "Ứng dụng vi sinh vật trong xử lý phế thải thực phẩm"
                        },
                        new
                        {
                            MaDt = "DT409",
                            HocKy = (byte)1,
                            KinhPhi = 13,
                            MaGv = 82,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Vinamilk",
                            SoLuongToiDa = 2,
                            TenDt = "Tối ưu hóa sản xuất sữa chua uống"
                        },
                        new
                        {
                            MaDt = "DT410",
                            HocKy = (byte)2,
                            KinhPhi = 17,
                            MaGv = 82,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Bibica",
                            SoLuongToiDa = 3,
                            TenDt = "Phát triển snack từ hạt ngũ cốc lên men"
                        },
                        new
                        {
                            MaDt = "DT411",
                            HocKy = (byte)1,
                            KinhPhi = 18,
                            MaGv = 83,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Minh Phú Seafood",
                            SoLuongToiDa = 3,
                            TenDt = "Ứng dụng enzyme protease trong thủy sản"
                        },
                        new
                        {
                            MaDt = "DT412",
                            HocKy = (byte)2,
                            KinhPhi = 15,
                            MaGv = 83,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Marou Chocolate",
                            SoLuongToiDa = 2,
                            TenDt = "Chiết xuất polyphenol từ vỏ ca cao"
                        },
                        new
                        {
                            MaDt = "DT413",
                            HocKy = (byte)1,
                            KinhPhi = 20,
                            MaGv = 83,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Pasteur Street Brewing",
                            SoLuongToiDa = 4,
                            TenDt = "Tối ưu hóa quy trình sản xuất bia thủ công"
                        },
                        new
                        {
                            MaDt = "DT414",
                            HocKy = (byte)2,
                            KinhPhi = 14,
                            MaGv = 83,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Start-up FoodTech",
                            SoLuongToiDa = 2,
                            TenDt = "Ứng dụng vi sinh vật lên men kombucha"
                        },
                        new
                        {
                            MaDt = "DT415",
                            HocKy = (byte)1,
                            KinhPhi = 17,
                            MaGv = 83,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Nutifood",
                            SoLuongToiDa = 3,
                            TenDt = "Phát triển thực phẩm chức năng từ gạo lứt"
                        },
                        new
                        {
                            MaDt = "DT416",
                            HocKy = (byte)2,
                            KinhPhi = 15,
                            MaGv = 84,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Trung tâm CNSH TP.HCM",
                            SoLuongToiDa = 2,
                            TenDt = "Chiết xuất carotenoid từ gấc"
                        },
                        new
                        {
                            MaDt = "DT417",
                            HocKy = (byte)1,
                            KinhPhi = 18,
                            MaGv = 84,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Yakult VN",
                            SoLuongToiDa = 3,
                            TenDt = "Nghiên cứu vi sinh vật probiotic mới"
                        },
                        new
                        {
                            MaDt = "DT418",
                            HocKy = (byte)2,
                            KinhPhi = 20,
                            MaGv = 84,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Vinamit",
                            SoLuongToiDa = 4,
                            TenDt = "Tối ưu quy trình làm khô đông lạnh trái cây"
                        },
                        new
                        {
                            MaDt = "DT419",
                            HocKy = (byte)1,
                            KinhPhi = 16,
                            MaGv = 84,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Start-up FoodTech",
                            SoLuongToiDa = 2,
                            TenDt = "Phát triển sản phẩm kefir từ sữa hạt"
                        },
                        new
                        {
                            MaDt = "DT420",
                            HocKy = (byte)2,
                            KinhPhi = 19,
                            MaGv = 84,
                            NamHoc = "2023-2024",
                            NoiThucTap = "VinEco",
                            SoLuongToiDa = 3,
                            TenDt = "Ứng dụng công nghệ nano trong bảo quản rau quả"
                        },
                        new
                        {
                            MaDt = "DT421",
                            HocKy = (byte)1,
                            KinhPhi = 17,
                            MaGv = 85,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Viện CNSH",
                            SoLuongToiDa = 3,
                            TenDt = "Sản xuất enzyme amylase từ vi sinh vật"
                        },
                        new
                        {
                            MaDt = "DT422",
                            HocKy = (byte)2,
                            KinhPhi = 14,
                            MaGv = 85,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Tân Hiệp Phát",
                            SoLuongToiDa = 2,
                            TenDt = "Nghiên cứu quy trình sản xuất nước trái cây lên men"
                        },
                        new
                        {
                            MaDt = "DT423",
                            HocKy = (byte)1,
                            KinhPhi = 16,
                            MaGv = 85,
                            NamHoc = "2023-2024",
                            NoiThucTap = "TH True Milk",
                            SoLuongToiDa = 2,
                            TenDt = "Ứng dụng probiotic trong chế biến sữa chua"
                        },
                        new
                        {
                            MaDt = "DT424",
                            HocKy = (byte)2,
                            KinhPhi = 12,
                            MaGv = 85,
                            NamHoc = "2023-2024",
                            NoiThucTap = "ABC Bakery",
                            SoLuongToiDa = 2,
                            TenDt = "Tối ưu hóa quy trình sản xuất bánh mì"
                        },
                        new
                        {
                            MaDt = "DT425",
                            HocKy = (byte)1,
                            KinhPhi = 18,
                            MaGv = 85,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Nestlé VN",
                            SoLuongToiDa = 3,
                            TenDt = "Phát triển sản phẩm thức uống từ thảo mộc"
                        },
                        new
                        {
                            MaDt = "DT426",
                            HocKy = (byte)1,
                            KinhPhi = 14,
                            MaGv = 86,
                            NamHoc = "2024-2025",
                            NoiThucTap = "Trung tâm Quan trắc TNMT",
                            SoLuongToiDa = 2,
                            TenDt = "Đánh giá chất lượng không khí TP.HCM bằng mô hình AQI"
                        },
                        new
                        {
                            MaDt = "DT427",
                            HocKy = (byte)2,
                            KinhPhi = 18,
                            MaGv = 86,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Sở TNMT TP.HCM",
                            SoLuongToiDa = 3,
                            TenDt = "Ứng dụng IoT giám sát bụi mịn PM2.5"
                        },
                        new
                        {
                            MaDt = "DT428",
                            HocKy = (byte)1,
                            KinhPhi = 16,
                            MaGv = 86,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Viện Môi trường & Tài nguyên",
                            SoLuongToiDa = 4,
                            TenDt = "Mô phỏng lan truyền khí thải giao thông bằng CALINE"
                        },
                        new
                        {
                            MaDt = "DT429",
                            HocKy = (byte)2,
                            KinhPhi = 12,
                            MaGv = 86,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Sở GTVT TP.HCM",
                            SoLuongToiDa = 2,
                            TenDt = "Xây dựng bản đồ tiếng ồn đô thị bằng GIS"
                        },
                        new
                        {
                            MaDt = "DT430",
                            HocKy = (byte)1,
                            KinhPhi = 15,
                            MaGv = 86,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Sở Xây dựng TP.HCM",
                            SoLuongToiDa = 3,
                            TenDt = "Đánh giá hiệu quả cây xanh trong giảm UHI"
                        },
                        new
                        {
                            MaDt = "DT431",
                            HocKy = (byte)2,
                            KinhPhi = 19,
                            MaGv = 87,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Sawaco",
                            SoLuongToiDa = 4,
                            TenDt = "Thiết kế hệ thống xử lý nước thải sinh hoạt MBR"
                        },
                        new
                        {
                            MaDt = "DT432",
                            HocKy = (byte)1,
                            KinhPhi = 17,
                            MaGv = 87,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Khu đô thị Thủ Đức",
                            SoLuongToiDa = 3,
                            TenDt = "Khử N–P bằng quy trình A2/O quy mô pilot"
                        },
                        new
                        {
                            MaDt = "DT433",
                            HocKy = (byte)2,
                            KinhPhi = 14,
                            MaGv = 87,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Viện KHCNMT",
                            SoLuongToiDa = 2,
                            TenDt = "Hấp phụ kim loại nặng bằng biochar từ vỏ trấu"
                        },
                        new
                        {
                            MaDt = "DT434",
                            HocKy = (byte)1,
                            KinhPhi = 18,
                            MaGv = 87,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Quatest 3",
                            SoLuongToiDa = 3,
                            TenDt = "Ứng dụng UV/ozon phân hủy thuốc trừ sâu"
                        },
                        new
                        {
                            MaDt = "DT435",
                            HocKy = (byte)2,
                            KinhPhi = 16,
                            MaGv = 87,
                            NamHoc = "2023-2024",
                            NoiThucTap = "KCN Tân Bình",
                            SoLuongToiDa = 2,
                            TenDt = "Tối ưu bể UASB xử lý nước thải thực phẩm"
                        },
                        new
                        {
                            MaDt = "DT436",
                            HocKy = (byte)1,
                            KinhPhi = 20,
                            MaGv = 88,
                            NamHoc = "2024-2025",
                            NoiThucTap = "Doanh nghiệp Bao bì Xanh",
                            SoLuongToiDa = 4,
                            TenDt = "Đánh giá vòng đời (LCA) cho sản phẩm nhựa sinh học"
                        },
                        new
                        {
                            MaDt = "DT437",
                            HocKy = (byte)2,
                            KinhPhi = 15,
                            MaGv = 88,
                            NamHoc = "2023-2024",
                            NoiThucTap = "IUH – KHCNMT",
                            SoLuongToiDa = 2,
                            TenDt = "Tính toán dấu chân carbon cho trường đại học"
                        },
                        new
                        {
                            MaDt = "DT438",
                            HocKy = (byte)1,
                            KinhPhi = 18,
                            MaGv = 88,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Sở TNMT TP.HCM",
                            SoLuongToiDa = 3,
                            TenDt = "Giải pháp kinh tế tuần hoàn cho rác thải nhựa"
                        },
                        new
                        {
                            MaDt = "DT439",
                            HocKy = (byte)2,
                            KinhPhi = 17,
                            MaGv = 88,
                            NamHoc = "2023-2024",
                            NoiThucTap = "RECOF Vietnam",
                            SoLuongToiDa = 3,
                            TenDt = "Thiết kế mô hình thu hồi nhiệt trong nhà máy"
                        },
                        new
                        {
                            MaDt = "DT440",
                            HocKy = (byte)1,
                            KinhPhi = 14,
                            MaGv = 88,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Doanh nghiệp Công nghiệp",
                            SoLuongToiDa = 2,
                            TenDt = "Báo cáo phát thải KNK theo ISO 14064"
                        },
                        new
                        {
                            MaDt = "DT441",
                            HocKy = (byte)2,
                            KinhPhi = 13,
                            MaGv = 89,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Sở TNMT tỉnh Đồng Nai",
                            SoLuongToiDa = 2,
                            TenDt = "ĐTM cho dự án nhà máy dệt nhuộm"
                        },
                        new
                        {
                            MaDt = "DT442",
                            HocKy = (byte)1,
                            KinhPhi = 16,
                            MaGv = 89,
                            NamHoc = "2023-2024",
                            NoiThucTap = "KCN VSIP",
                            SoLuongToiDa = 3,
                            TenDt = "Quản lý môi trường khu công nghiệp theo ISO 14001"
                        },
                        new
                        {
                            MaDt = "DT443",
                            HocKy = (byte)2,
                            KinhPhi = 19,
                            MaGv = 89,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Sở TNMT TP.HCM",
                            SoLuongToiDa = 4,
                            TenDt = "Quan trắc tự động nước thải và liên thông dữ liệu"
                        },
                        new
                        {
                            MaDt = "DT444",
                            HocKy = (byte)1,
                            KinhPhi = 15,
                            MaGv = 89,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Viện Môi trường & Tài nguyên",
                            SoLuongToiDa = 2,
                            TenDt = "Đánh giá rủi ro môi trường theo phương pháp ERA"
                        },
                        new
                        {
                            MaDt = "DT445",
                            HocKy = (byte)2,
                            KinhPhi = 12,
                            MaGv = 89,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Ban QLDA Giao thông",
                            SoLuongToiDa = 2,
                            TenDt = "Kế hoạch quản lý môi trường EMP cho dự án giao thông"
                        },
                        new
                        {
                            MaDt = "DT446",
                            HocKy = (byte)1,
                            KinhPhi = 18,
                            MaGv = 90,
                            NamHoc = "2018-2019",
                            NoiThucTap = "Khu Dự trữ Sinh quyển Cần Giờ",
                            SoLuongToiDa = 3,
                            TenDt = "Phục hồi rừng ngập mặn Cần Giờ – đánh giá đa dạng sinh học"
                        },
                        new
                        {
                            MaDt = "DT447",
                            HocKy = (byte)2,
                            KinhPhi = 14,
                            MaGv = 90,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Khu bảo tồn thiên nhiên Bình Châu",
                            SoLuongToiDa = 2,
                            TenDt = "Giám sát đa dạng sinh học bằng bẫy ảnh"
                        },
                        new
                        {
                            MaDt = "DT448",
                            HocKy = (byte)1,
                            KinhPhi = 17,
                            MaGv = 90,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Viện Sinh thái & Tài nguyên",
                            SoLuongToiDa = 3,
                            TenDt = "Mô hình sinh cảnh phù hợp cho chim nước bằng MaxEnt"
                        },
                        new
                        {
                            MaDt = "DT449",
                            HocKy = (byte)2,
                            KinhPhi = 12,
                            MaGv = 90,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Sở NN&PTNT",
                            SoLuongToiDa = 2,
                            TenDt = "Đánh giá tác động xâm lấn của loài ngoại lai"
                        },
                        new
                        {
                            MaDt = "DT450",
                            HocKy = (byte)1,
                            KinhPhi = 20,
                            MaGv = 90,
                            NamHoc = "2023-2024",
                            NoiThucTap = "Trung tâm PTN Sinh học",
                            SoLuongToiDa = 4,
                            TenDt = "Ứng dụng DNA barcoding trong giám định loài"
                        },
                        new
                        {
                            MaDt = "DT451",
                            HocKy = (byte)1,
                            KinhPhi = 22,
                            MaGv = 1,
                            NamHoc = "2025-2026",
                            NoiThucTap = "FPT Software",
                            SoLuongToiDa = 3,
                            TenDt = "Nền tảng quản lý khóa học microservices (.NET + React)"
                        },
                        new
                        {
                            MaDt = "DT452",
                            HocKy = (byte)1,
                            KinhPhi = 24,
                            MaGv = 1,
                            NamHoc = "2025-2026",
                            NoiThucTap = "VNG Cloud",
                            SoLuongToiDa = 4,
                            TenDt = "Trợ lý học tập dùng LLM (RAG + Azure OpenAI)"
                        },
                        new
                        {
                            MaDt = "DT453",
                            HocKy = (byte)1,
                            KinhPhi = 18,
                            MaGv = 1,
                            NamHoc = "2025-2026",
                            NoiThucTap = "NashTech VN",
                            SoLuongToiDa = 3,
                            TenDt = "Hệ thống chấm bài lập trình tự động (Online Judge)"
                        },
                        new
                        {
                            MaDt = "DT454",
                            HocKy = (byte)1,
                            KinhPhi = 17,
                            MaGv = 1,
                            NamHoc = "2025-2026",
                            NoiThucTap = "Viettel Solutions",
                            SoLuongToiDa = 3,
                            TenDt = "Dashboard IoT giám sát phòng lab (MQTT + Timeseries DB)"
                        },
                        new
                        {
                            MaDt = "DT455",
                            HocKy = (byte)1,
                            KinhPhi = 20,
                            MaGv = 1,
                            NamHoc = "2025-2026",
                            NoiThucTap = "VNPT Data",
                            SoLuongToiDa = 4,
                            TenDt = "Phân tích dữ liệu sinh viên và dự báo rủi ro học tập (BI/ML)"
                        },
                        new
                        {
                            MaDt = "DT456",
                            HocKy = (byte)1,
                            KinhPhi = 19,
                            MaGv = 1,
                            NamHoc = "2025-2026",
                            NoiThucTap = "Axon Active",
                            SoLuongToiDa = 3,
                            TenDt = "Cổng tuyển sinh số đa kênh (Next.js + Keycloak SSO)"
                        },
                        new
                        {
                            MaDt = "DT457",
                            HocKy = (byte)1,
                            KinhPhi = 21,
                            MaGv = 1,
                            NamHoc = "2025-2026",
                            NoiThucTap = "Zalo AI",
                            SoLuongToiDa = 4,
                            TenDt = "Chatbot hỗ trợ sinh viên (RAG + Vector DB + LangChain)"
                        },
                        new
                        {
                            MaDt = "DT458",
                            HocKy = (byte)1,
                            KinhPhi = 18,
                            MaGv = 1,
                            NamHoc = "2025-2026",
                            NoiThucTap = "TopCV",
                            SoLuongToiDa = 3,
                            TenDt = "Nền tảng kết nối thực tập & việc làm (Matching + Recommender)"
                        },
                        new
                        {
                            MaDt = "DT459",
                            HocKy = (byte)1,
                            KinhPhi = 23,
                            MaGv = 1,
                            NamHoc = "2025-2026",
                            NoiThucTap = "CMC Global",
                            SoLuongToiDa = 4,
                            TenDt = "Đăng ký học phần chịu tải cao (CQRS + Event Sourcing)"
                        },
                        new
                        {
                            MaDt = "DT460",
                            HocKy = (byte)1,
                            KinhPhi = 16,
                            MaGv = 1,
                            NamHoc = "2025-2026",
                            NoiThucTap = "VinAI",
                            SoLuongToiDa = 3,
                            TenDt = "Điểm danh nhận diện khuôn mặt (Edge AI + ONNX)"
                        });
                });

            modelBuilder.Entity("InternshipManagement.Models.GiangVien", b =>
                {
                    b.Property<int>("MaGv")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("magv");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("MaGv"));

                    b.Property<string>("HoTenGv")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("hotengv");

                    b.Property<decimal?>("Luong")
                        .HasPrecision(5, 2)
                        .HasColumnType("decimal(5,2)")
                        .HasColumnName("luong");

                    b.Property<string>("MaKhoa")
                        .IsRequired()
                        .HasColumnType("char(10)")
                        .HasColumnName("makhoa");

                    b.HasKey("MaGv");

                    b.HasIndex("MaKhoa");

                    b.ToTable("GiangVien");

                    b.HasData(
                        new
                        {
                            MaGv = 1,
                            HoTenGv = "Nguyễn Thị Hoàng Khanh",
                            Luong = 22.00m,
                            MaKhoa = "CNTT"
                        },
                        new
                        {
                            MaGv = 2,
                            HoTenGv = "Trần Văn Minh",
                            Luong = 25.50m,
                            MaKhoa = "CNTT"
                        },
                        new
                        {
                            MaGv = 3,
                            HoTenGv = "Lê Thị Mai Phương",
                            Luong = 24.20m,
                            MaKhoa = "CNTT"
                        },
                        new
                        {
                            MaGv = 4,
                            HoTenGv = "Phạm Quốc Dũng",
                            Luong = 28.00m,
                            MaKhoa = "CNTT"
                        },
                        new
                        {
                            MaGv = 5,
                            HoTenGv = "Đỗ Hồng Ngọc",
                            Luong = 23.75m,
                            MaKhoa = "CNTT"
                        },
                        new
                        {
                            MaGv = 6,
                            HoTenGv = "Nguyễn Văn An",
                            Luong = 26.00m,
                            MaKhoa = "CNCK"
                        },
                        new
                        {
                            MaGv = 7,
                            HoTenGv = "Trần Thị Bích Ngọc",
                            Luong = 21.50m,
                            MaKhoa = "CNCK"
                        },
                        new
                        {
                            MaGv = 8,
                            HoTenGv = "Phan Văn Hùng",
                            Luong = 27.30m,
                            MaKhoa = "CNCK"
                        },
                        new
                        {
                            MaGv = 9,
                            HoTenGv = "Vũ Thị Lan",
                            Luong = 22.80m,
                            MaKhoa = "CNCK"
                        },
                        new
                        {
                            MaGv = 10,
                            HoTenGv = "Lê Quang Hiếu",
                            Luong = 29.00m,
                            MaKhoa = "CNCK"
                        },
                        new
                        {
                            MaGv = 11,
                            HoTenGv = "Nguyễn Thị Thu Hằng",
                            Luong = 20.50m,
                            MaKhoa = "CNDIEN"
                        },
                        new
                        {
                            MaGv = 12,
                            HoTenGv = "Trần Văn Hòa",
                            Luong = 24.90m,
                            MaKhoa = "CNDIEN"
                        },
                        new
                        {
                            MaGv = 13,
                            HoTenGv = "Đặng Thị Hương",
                            Luong = 23.60m,
                            MaKhoa = "CNDIEN"
                        },
                        new
                        {
                            MaGv = 14,
                            HoTenGv = "Hoàng Văn Phúc",
                            Luong = 30.00m,
                            MaKhoa = "CNDIEN"
                        },
                        new
                        {
                            MaGv = 15,
                            HoTenGv = "Phạm Thị Thanh Thủy",
                            Luong = 22.40m,
                            MaKhoa = "CNDIEN"
                        },
                        new
                        {
                            MaGv = 16,
                            HoTenGv = "Nguyễn Văn Thắng",
                            Luong = 24.50m,
                            MaKhoa = "CNDT"
                        },
                        new
                        {
                            MaGv = 17,
                            HoTenGv = "Trần Thị Mỹ Linh",
                            Luong = 23.80m,
                            MaKhoa = "CNDT"
                        },
                        new
                        {
                            MaGv = 18,
                            HoTenGv = "Phạm Văn Huy",
                            Luong = 26.00m,
                            MaKhoa = "CNDT"
                        },
                        new
                        {
                            MaGv = 19,
                            HoTenGv = "Đỗ Thị Cẩm Tú",
                            Luong = 22.70m,
                            MaKhoa = "CNDT"
                        },
                        new
                        {
                            MaGv = 20,
                            HoTenGv = "Lê Minh Tuấn",
                            Luong = 28.20m,
                            MaKhoa = "CNDT"
                        },
                        new
                        {
                            MaGv = 21,
                            HoTenGv = "Nguyễn Hoàng Anh",
                            Luong = 25.30m,
                            MaKhoa = "CNDL"
                        },
                        new
                        {
                            MaGv = 22,
                            HoTenGv = "Trần Thị Bảo Yến",
                            Luong = 21.90m,
                            MaKhoa = "CNDL"
                        },
                        new
                        {
                            MaGv = 23,
                            HoTenGv = "Phan Văn Lộc",
                            Luong = 27.40m,
                            MaKhoa = "CNDL"
                        },
                        new
                        {
                            MaGv = 24,
                            HoTenGv = "Đinh Thị Hồng Nhung",
                            Luong = 23.50m,
                            MaKhoa = "CNDL"
                        },
                        new
                        {
                            MaGv = 25,
                            HoTenGv = "Vũ Đức Long",
                            Luong = 29.10m,
                            MaKhoa = "CNDL"
                        },
                        new
                        {
                            MaGv = 26,
                            HoTenGv = "Nguyễn Thị Thu Trang",
                            Luong = 22.40m,
                            MaKhoa = "CNNL"
                        },
                        new
                        {
                            MaGv = 27,
                            HoTenGv = "Trần Văn Lâm",
                            Luong = 24.80m,
                            MaKhoa = "CNNL"
                        },
                        new
                        {
                            MaGv = 28,
                            HoTenGv = "Phạm Thị Bích Thảo",
                            Luong = 23.20m,
                            MaKhoa = "CNNL"
                        },
                        new
                        {
                            MaGv = 29,
                            HoTenGv = "Lê Văn Sơn",
                            Luong = 26.70m,
                            MaKhoa = "CNNL"
                        },
                        new
                        {
                            MaGv = 30,
                            HoTenGv = "Hoàng Thị Ngọc Mai",
                            Luong = 28.50m,
                            MaKhoa = "CNNL"
                        },
                        new
                        {
                            MaGv = 31,
                            HoTenGv = "Nguyễn Thị Thanh Hương",
                            Luong = 23.60m,
                            MaKhoa = "CNMT"
                        },
                        new
                        {
                            MaGv = 32,
                            HoTenGv = "Trần Văn Khánh",
                            Luong = 25.10m,
                            MaKhoa = "CNMT"
                        },
                        new
                        {
                            MaGv = 33,
                            HoTenGv = "Phạm Thị Mỹ Dung",
                            Luong = 21.80m,
                            MaKhoa = "CNMT"
                        },
                        new
                        {
                            MaGv = 34,
                            HoTenGv = "Đỗ Văn Bình",
                            Luong = 27.40m,
                            MaKhoa = "CNMT"
                        },
                        new
                        {
                            MaGv = 35,
                            HoTenGv = "Lê Thị Kim Ngân",
                            Luong = 22.90m,
                            MaKhoa = "CNMT"
                        },
                        new
                        {
                            MaGv = 36,
                            HoTenGv = "Nguyễn Văn Toàn",
                            Luong = 26.30m,
                            MaKhoa = "CNHH"
                        },
                        new
                        {
                            MaGv = 37,
                            HoTenGv = "Trần Thị Minh Châu",
                            Luong = 23.20m,
                            MaKhoa = "CNHH"
                        },
                        new
                        {
                            MaGv = 38,
                            HoTenGv = "Phan Văn Khôi",
                            Luong = 28.00m,
                            MaKhoa = "CNHH"
                        },
                        new
                        {
                            MaGv = 39,
                            HoTenGv = "Đinh Thị Hòa",
                            Luong = 22.40m,
                            MaKhoa = "CNHH"
                        },
                        new
                        {
                            MaGv = 40,
                            HoTenGv = "Hoàng Văn Cường",
                            Luong = 24.70m,
                            MaKhoa = "CNHH"
                        },
                        new
                        {
                            MaGv = 41,
                            HoTenGv = "Nguyễn Thị Mai Lan",
                            Luong = 21.50m,
                            MaKhoa = "KHCB"
                        },
                        new
                        {
                            MaGv = 42,
                            HoTenGv = "Trần Văn Hải",
                            Luong = 25.80m,
                            MaKhoa = "KHCB"
                        },
                        new
                        {
                            MaGv = 43,
                            HoTenGv = "Phạm Thị Như Quỳnh",
                            Luong = 23.40m,
                            MaKhoa = "KHCB"
                        },
                        new
                        {
                            MaGv = 44,
                            HoTenGv = "Vũ Văn Thái",
                            Luong = 27.10m,
                            MaKhoa = "KHCB"
                        },
                        new
                        {
                            MaGv = 45,
                            HoTenGv = "Lê Thị Thanh Trúc",
                            Luong = 22.80m,
                            MaKhoa = "KHCB"
                        },
                        new
                        {
                            MaGv = 46,
                            HoTenGv = "Nguyễn Văn Lợi",
                            Luong = 23.90m,
                            MaKhoa = "LUAT"
                        },
                        new
                        {
                            MaGv = 47,
                            HoTenGv = "Trần Thị Mai Hoa",
                            Luong = 22.70m,
                            MaKhoa = "LUAT"
                        },
                        new
                        {
                            MaGv = 48,
                            HoTenGv = "Phạm Văn Khánh",
                            Luong = 25.60m,
                            MaKhoa = "LUAT"
                        },
                        new
                        {
                            MaGv = 49,
                            HoTenGv = "Đỗ Thị Bích Phượng",
                            Luong = 24.20m,
                            MaKhoa = "LUAT"
                        },
                        new
                        {
                            MaGv = 50,
                            HoTenGv = "Hoàng Văn Dũng",
                            Luong = 27.00m,
                            MaKhoa = "LUAT"
                        },
                        new
                        {
                            MaGv = 51,
                            HoTenGv = "Nguyễn Thị Mỹ Linh",
                            Luong = 22.50m,
                            MaKhoa = "NN"
                        },
                        new
                        {
                            MaGv = 52,
                            HoTenGv = "Trần Văn Hùng",
                            Luong = 26.40m,
                            MaKhoa = "NN"
                        },
                        new
                        {
                            MaGv = 53,
                            HoTenGv = "Phạm Thị Hồng Nhung",
                            Luong = 23.80m,
                            MaKhoa = "NN"
                        },
                        new
                        {
                            MaGv = 54,
                            HoTenGv = "Đinh Văn Nam",
                            Luong = 25.20m,
                            MaKhoa = "NN"
                        },
                        new
                        {
                            MaGv = 55,
                            HoTenGv = "Lê Thị Thu Hà",
                            Luong = 27.10m,
                            MaKhoa = "NN"
                        },
                        new
                        {
                            MaGv = 56,
                            HoTenGv = "Nguyễn Văn Phát",
                            Luong = 24.30m,
                            MaKhoa = "QTKD"
                        },
                        new
                        {
                            MaGv = 57,
                            HoTenGv = "Trần Thị Thanh Tâm",
                            Luong = 22.80m,
                            MaKhoa = "QTKD"
                        },
                        new
                        {
                            MaGv = 58,
                            HoTenGv = "Phan Văn Quang",
                            Luong = 26.70m,
                            MaKhoa = "QTKD"
                        },
                        new
                        {
                            MaGv = 59,
                            HoTenGv = "Đỗ Thị Lan Anh",
                            Luong = 23.90m,
                            MaKhoa = "QTKD"
                        },
                        new
                        {
                            MaGv = 60,
                            HoTenGv = "Vũ Đức Thịnh",
                            Luong = 28.20m,
                            MaKhoa = "QTKD"
                        },
                        new
                        {
                            MaGv = 61,
                            HoTenGv = "Nguyễn Thị Kim Yến",
                            Luong = 22.40m,
                            MaKhoa = "TMDL"
                        },
                        new
                        {
                            MaGv = 62,
                            HoTenGv = "Trần Văn Quý",
                            Luong = 25.50m,
                            MaKhoa = "TMDL"
                        },
                        new
                        {
                            MaGv = 63,
                            HoTenGv = "Phạm Thị Bảo Trân",
                            Luong = 23.10m,
                            MaKhoa = "TMDL"
                        },
                        new
                        {
                            MaGv = 64,
                            HoTenGv = "Lê Văn Hòa",
                            Luong = 27.60m,
                            MaKhoa = "TMDL"
                        },
                        new
                        {
                            MaGv = 65,
                            HoTenGv = "Đặng Thị Minh Ngọc",
                            Luong = 24.80m,
                            MaKhoa = "TMDL"
                        },
                        new
                        {
                            MaGv = 66,
                            HoTenGv = "Nguyễn Văn Thọ",
                            Luong = 26.90m,
                            MaKhoa = "KTXD"
                        },
                        new
                        {
                            MaGv = 67,
                            HoTenGv = "Trần Thị Thảo",
                            Luong = 22.60m,
                            MaKhoa = "KTXD"
                        },
                        new
                        {
                            MaGv = 68,
                            HoTenGv = "Phạm Văn Thành",
                            Luong = 28.40m,
                            MaKhoa = "KTXD"
                        },
                        new
                        {
                            MaGv = 69,
                            HoTenGv = "Đỗ Thị Thanh Loan",
                            Luong = 23.50m,
                            MaKhoa = "KTXD"
                        },
                        new
                        {
                            MaGv = 70,
                            HoTenGv = "Hoàng Văn Lâm",
                            Luong = 25.70m,
                            MaKhoa = "KTXD"
                        },
                        new
                        {
                            MaGv = 71,
                            HoTenGv = "Nguyễn Thị Thanh Vân",
                            Luong = 23.40m,
                            MaKhoa = "TCKT"
                        },
                        new
                        {
                            MaGv = 72,
                            HoTenGv = "Trần Văn Hậu",
                            Luong = 25.60m,
                            MaKhoa = "TCKT"
                        },
                        new
                        {
                            MaGv = 73,
                            HoTenGv = "Phạm Thị Ngọc Bích",
                            Luong = 22.80m,
                            MaKhoa = "TCKT"
                        },
                        new
                        {
                            MaGv = 74,
                            HoTenGv = "Đỗ Văn Khải",
                            Luong = 27.10m,
                            MaKhoa = "TCKT"
                        },
                        new
                        {
                            MaGv = 75,
                            HoTenGv = "Lê Thị Mỹ Hạnh",
                            Luong = 24.50m,
                            MaKhoa = "TCKT"
                        },
                        new
                        {
                            MaGv = 76,
                            HoTenGv = "Nguyễn Văn Hưng",
                            Luong = 26.20m,
                            MaKhoa = "DTQT"
                        },
                        new
                        {
                            MaGv = 77,
                            HoTenGv = "Trần Thị Diễm My",
                            Luong = 22.90m,
                            MaKhoa = "DTQT"
                        },
                        new
                        {
                            MaGv = 78,
                            HoTenGv = "Phan Văn Quý",
                            Luong = 25.70m,
                            MaKhoa = "DTQT"
                        },
                        new
                        {
                            MaGv = 79,
                            HoTenGv = "Đinh Thị Thảo",
                            Luong = 23.60m,
                            MaKhoa = "DTQT"
                        },
                        new
                        {
                            MaGv = 80,
                            HoTenGv = "Hoàng Văn Tiến",
                            Luong = 28.30m,
                            MaKhoa = "DTQT"
                        },
                        new
                        {
                            MaGv = 81,
                            HoTenGv = "Nguyễn Thị Hồng Nhung",
                            Luong = 24.20m,
                            MaKhoa = "CNSH_TP"
                        },
                        new
                        {
                            MaGv = 82,
                            HoTenGv = "Trần Văn Duy",
                            Luong = 26.40m,
                            MaKhoa = "CNSH_TP"
                        },
                        new
                        {
                            MaGv = 83,
                            HoTenGv = "Phạm Thị Mai Anh",
                            Luong = 22.50m,
                            MaKhoa = "CNSH_TP"
                        },
                        new
                        {
                            MaGv = 84,
                            HoTenGv = "Đỗ Văn Hiếu",
                            Luong = 27.80m,
                            MaKhoa = "CNSH_TP"
                        },
                        new
                        {
                            MaGv = 85,
                            HoTenGv = "Lê Thị Bảo Trâm",
                            Luong = 23.90m,
                            MaKhoa = "CNSH_TP"
                        },
                        new
                        {
                            MaGv = 86,
                            HoTenGv = "Nguyễn Văn Long",
                            Luong = 25.10m,
                            MaKhoa = "KHCNMT"
                        },
                        new
                        {
                            MaGv = 87,
                            HoTenGv = "Trần Thị Ngọc Hân",
                            Luong = 23.70m,
                            MaKhoa = "KHCNMT"
                        },
                        new
                        {
                            MaGv = 88,
                            HoTenGv = "Phạm Văn Đức",
                            Luong = 26.80m,
                            MaKhoa = "KHCNMT"
                        },
                        new
                        {
                            MaGv = 89,
                            HoTenGv = "Đinh Thị Thanh Xuân",
                            Luong = 22.60m,
                            MaKhoa = "KHCNMT"
                        },
                        new
                        {
                            MaGv = 90,
                            HoTenGv = "Hoàng Văn Lợi",
                            Luong = 28.00m,
                            MaKhoa = "KHCNMT"
                        });
                });

            modelBuilder.Entity("InternshipManagement.Models.HuongDan", b =>
                {
                    b.Property<int>("MaSv")
                        .HasColumnType("int")
                        .HasColumnName("masv");

                    b.Property<string>("MaDt")
                        .HasColumnType("char(10)")
                        .HasColumnName("madt");

                    b.Property<DateTime?>("AcceptedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("ngaychapnhan");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("ngaydangky");

                    b.Property<string>("GhiChu")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("ghichu");

                    b.Property<decimal?>("KetQua")
                        .HasPrecision(5, 2)
                        .HasColumnType("decimal(5,2)")
                        .HasColumnName("ketqua");

                    b.Property<int>("MaGv")
                        .HasColumnType("int")
                        .HasColumnName("magv");

                    b.Property<byte>("TrangThai")
                        .HasColumnType("tinyint")
                        .HasColumnName("trangthai");

                    b.HasKey("MaSv", "MaDt");

                    b.HasIndex("CreatedAt");

                    b.HasIndex("MaGv");

                    b.HasIndex("TrangThai");

                    b.HasIndex("MaDt", "TrangThai");

                    b.ToTable("HuongDan");

                    b.HasData(
                        new
                        {
                            MaSv = 1001,
                            MaDt = "DT001",
                            AcceptedAt = new DateTime(2024, 9, 10, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            CreatedAt = new DateTime(2024, 9, 5, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            GhiChu = "Đã được chấp nhận tham gia",
                            MaGv = 1,
                            TrangThai = (byte)1
                        },
                        new
                        {
                            MaSv = 1001,
                            MaDt = "DT002",
                            CreatedAt = new DateTime(2024, 9, 15, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            GhiChu = "Đang chờ giảng viên duyệt",
                            MaGv = 1,
                            TrangThai = (byte)0
                        },
                        new
                        {
                            MaSv = 1002,
                            MaDt = "DT001",
                            AcceptedAt = new DateTime(2024, 9, 8, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            CreatedAt = new DateTime(2024, 9, 3, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            GhiChu = "Đã hoàn thành đề tài",
                            KetQua = 8.5m,
                            MaGv = 1,
                            TrangThai = (byte)3
                        },
                        new
                        {
                            MaSv = 1002,
                            MaDt = "DT003",
                            AcceptedAt = new DateTime(2024, 9, 18, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            CreatedAt = new DateTime(2024, 9, 12, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            GhiChu = "Đang thực hiện đề tài",
                            MaGv = 2,
                            TrangThai = (byte)2
                        },
                        new
                        {
                            MaSv = 1003,
                            MaDt = "DT004",
                            CreatedAt = new DateTime(2024, 9, 20, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            GhiChu = "Đề tài không phù hợp",
                            MaGv = 2,
                            TrangThai = (byte)4
                        },
                        new
                        {
                            MaSv = 1004,
                            MaDt = "DT005",
                            CreatedAt = new DateTime(2024, 9, 25, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            GhiChu = "Sinh viên xin rút đăng ký",
                            MaGv = 3,
                            TrangThai = (byte)5
                        });
                });

            modelBuilder.Entity("InternshipManagement.Models.Khoa", b =>
                {
                    b.Property<string>("MaKhoa")
                        .HasMaxLength(10)
                        .HasColumnType("char(10)")
                        .HasColumnName("makhoa");

                    b.Property<string>("DienThoai")
                        .HasMaxLength(20)
                        .HasColumnType("varchar(20)")
                        .HasColumnName("dienthoai");

                    b.Property<string>("TenKhoa")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("tenkhoa");

                    b.HasKey("MaKhoa");

                    b.HasIndex("MaKhoa")
                        .IsUnique();

                    b.ToTable("Khoa");

                    b.HasData(
                        new
                        {
                            MaKhoa = "CNCK",
                            DienThoai = "0901111111",
                            TenKhoa = "Khoa Công nghệ Cơ khí"
                        },
                        new
                        {
                            MaKhoa = "CNTT",
                            DienThoai = "0901111112",
                            TenKhoa = "Khoa Công nghệ Thông tin"
                        },
                        new
                        {
                            MaKhoa = "CNDIEN",
                            DienThoai = "0901111113",
                            TenKhoa = "Khoa Công nghệ Điện"
                        },
                        new
                        {
                            MaKhoa = "CNDT",
                            DienThoai = "0901111114",
                            TenKhoa = "Khoa Công nghệ Điện tử"
                        },
                        new
                        {
                            MaKhoa = "CNDL",
                            DienThoai = "0901111115",
                            TenKhoa = "Khoa Công nghệ Động lực"
                        },
                        new
                        {
                            MaKhoa = "CNNL",
                            DienThoai = "0901111116",
                            TenKhoa = "Khoa Công nghệ Nhiệt – Lạnh"
                        },
                        new
                        {
                            MaKhoa = "CNMT",
                            DienThoai = "0901111117",
                            TenKhoa = "Khoa Công nghệ May – Thời trang"
                        },
                        new
                        {
                            MaKhoa = "CNHH",
                            DienThoai = "0901111118",
                            TenKhoa = "Khoa Công nghệ Hóa học"
                        },
                        new
                        {
                            MaKhoa = "KHCB",
                            DienThoai = "0901111119",
                            TenKhoa = "Khoa Khoa học Cơ bản"
                        },
                        new
                        {
                            MaKhoa = "LUAT",
                            DienThoai = "0901111120",
                            TenKhoa = "Khoa Luật và Khoa học Chính trị"
                        },
                        new
                        {
                            MaKhoa = "NN",
                            DienThoai = "0901111121",
                            TenKhoa = "Khoa Ngoại ngữ"
                        },
                        new
                        {
                            MaKhoa = "QTKD",
                            DienThoai = "0901111122",
                            TenKhoa = "Khoa Quản trị Kinh doanh"
                        },
                        new
                        {
                            MaKhoa = "TMDL",
                            DienThoai = "0901111123",
                            TenKhoa = "Khoa Thương mại – Du lịch"
                        },
                        new
                        {
                            MaKhoa = "KTXD",
                            DienThoai = "0901111124",
                            TenKhoa = "Khoa Kỹ thuật Xây dựng"
                        },
                        new
                        {
                            MaKhoa = "TCKT",
                            DienThoai = "0901111125",
                            TenKhoa = "Viện Tài chính – Kế toán"
                        },
                        new
                        {
                            MaKhoa = "DTQT",
                            DienThoai = "0901111126",
                            TenKhoa = "Viện Đào tạo quốc tế và Sau đại học"
                        },
                        new
                        {
                            MaKhoa = "CNSH_TP",
                            DienThoai = "0901111127",
                            TenKhoa = "Viện Công nghệ Sinh học và Thực phẩm"
                        },
                        new
                        {
                            MaKhoa = "KHCNMT",
                            DienThoai = "0901111128",
                            TenKhoa = "Viện Khoa học Công nghệ và Quản lý Môi trường"
                        });
                });

            modelBuilder.Entity("InternshipManagement.Models.SinhVien", b =>
                {
                    b.Property<int>("MaSv")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("masv");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("MaSv"));

                    b.Property<string>("HoTenSv")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("hotensv");

                    b.Property<string>("MaKhoa")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("char(10)")
                        .HasColumnName("makhoa");

                    b.Property<int>("NamSinh")
                        .HasColumnType("int")
                        .HasColumnName("namsinh");

                    b.Property<string>("QueQuan")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("quequan");

                    b.HasKey("MaSv");

                    b.HasIndex("MaKhoa");

                    b.ToTable("SinhVien");

                    b.HasData(
                        new
                        {
                            MaSv = 1006,
                            HoTenSv = "Nguyễn Văn Hùng",
                            MaKhoa = "CNCK",
                            NamSinh = 2002,
                            QueQuan = "Hà Nội"
                        },
                        new
                        {
                            MaSv = 1007,
                            HoTenSv = "Trần Thị Mai",
                            MaKhoa = "CNCK",
                            NamSinh = 2001,
                            QueQuan = "Nam Định"
                        },
                        new
                        {
                            MaSv = 1008,
                            HoTenSv = "Phạm Văn Nam",
                            MaKhoa = "CNCK",
                            NamSinh = 2003,
                            QueQuan = "Hải Phòng"
                        },
                        new
                        {
                            MaSv = 1009,
                            HoTenSv = "Đỗ Thị Hoa",
                            MaKhoa = "CNCK",
                            NamSinh = 2002,
                            QueQuan = "Thái Bình"
                        },
                        new
                        {
                            MaSv = 1010,
                            HoTenSv = "Lê Quang Vinh",
                            MaKhoa = "CNCK",
                            NamSinh = 2001,
                            QueQuan = "Thanh Hóa"
                        },
                        new
                        {
                            MaSv = 1001,
                            HoTenSv = "Lê Hoàng Khang",
                            MaKhoa = "CNTT",
                            NamSinh = 2002,
                            QueQuan = "TP.HCM"
                        },
                        new
                        {
                            MaSv = 1002,
                            HoTenSv = "Trần Văn Minh",
                            MaKhoa = "CNTT",
                            NamSinh = 2001,
                            QueQuan = "Đà Nẵng"
                        },
                        new
                        {
                            MaSv = 1003,
                            HoTenSv = "Phạm Thị Hồng Nhung",
                            MaKhoa = "CNTT",
                            NamSinh = 2003,
                            QueQuan = "Nghệ An"
                        },
                        new
                        {
                            MaSv = 1004,
                            HoTenSv = "Đinh Văn Lâm",
                            MaKhoa = "CNTT",
                            NamSinh = 2002,
                            QueQuan = "Quảng Ninh"
                        },
                        new
                        {
                            MaSv = 1005,
                            HoTenSv = "Hoàng Thị Thu Hà",
                            MaKhoa = "CNTT",
                            NamSinh = 2001,
                            QueQuan = "Huế"
                        },
                        new
                        {
                            MaSv = 1011,
                            HoTenSv = "Nguyễn Văn Phúc",
                            MaKhoa = "CNDIEN",
                            NamSinh = 2002,
                            QueQuan = "Bắc Giang"
                        },
                        new
                        {
                            MaSv = 1012,
                            HoTenSv = "Trần Thị Bảo Trâm",
                            MaKhoa = "CNDIEN",
                            NamSinh = 2003,
                            QueQuan = "Phú Thọ"
                        },
                        new
                        {
                            MaSv = 1013,
                            HoTenSv = "Phạm Văn Dũng",
                            MaKhoa = "CNDIEN",
                            NamSinh = 2001,
                            QueQuan = "Vĩnh Phúc"
                        },
                        new
                        {
                            MaSv = 1014,
                            HoTenSv = "Đỗ Thị Hạnh",
                            MaKhoa = "CNDIEN",
                            NamSinh = 2002,
                            QueQuan = "Hưng Yên"
                        },
                        new
                        {
                            MaSv = 1015,
                            HoTenSv = "Lê Minh Tuấn",
                            MaKhoa = "CNDIEN",
                            NamSinh = 2001,
                            QueQuan = "Ninh Bình"
                        },
                        new
                        {
                            MaSv = 1016,
                            HoTenSv = "Nguyễn Thị Thanh Thảo",
                            MaKhoa = "CNDT",
                            NamSinh = 2003,
                            QueQuan = "Đắk Lắk"
                        },
                        new
                        {
                            MaSv = 1017,
                            HoTenSv = "Trần Văn Quân",
                            MaKhoa = "CNDT",
                            NamSinh = 2001,
                            QueQuan = "Khánh Hòa"
                        },
                        new
                        {
                            MaSv = 1018,
                            HoTenSv = "Phạm Thị Mỹ Duyên",
                            MaKhoa = "CNDT",
                            NamSinh = 2002,
                            QueQuan = "Cần Thơ"
                        },
                        new
                        {
                            MaSv = 1019,
                            HoTenSv = "Đỗ Văn Thành",
                            MaKhoa = "CNDT",
                            NamSinh = 2003,
                            QueQuan = "Bình Định"
                        },
                        new
                        {
                            MaSv = 1020,
                            HoTenSv = "Lê Thị Bích Ngọc",
                            MaKhoa = "CNDT",
                            NamSinh = 2002,
                            QueQuan = "Tiền Giang"
                        },
                        new
                        {
                            MaSv = 1021,
                            HoTenSv = "Nguyễn Văn Khải",
                            MaKhoa = "CNDL",
                            NamSinh = 2002,
                            QueQuan = "Hà Nội"
                        },
                        new
                        {
                            MaSv = 1022,
                            HoTenSv = "Trần Thị Minh Thư",
                            MaKhoa = "CNDL",
                            NamSinh = 2001,
                            QueQuan = "Hải Dương"
                        },
                        new
                        {
                            MaSv = 1023,
                            HoTenSv = "Phạm Văn Lâm",
                            MaKhoa = "CNDL",
                            NamSinh = 2003,
                            QueQuan = "Quảng Nam"
                        },
                        new
                        {
                            MaSv = 1024,
                            HoTenSv = "Đỗ Thị Kim Oanh",
                            MaKhoa = "CNDL",
                            NamSinh = 2002,
                            QueQuan = "Thanh Hóa"
                        },
                        new
                        {
                            MaSv = 1025,
                            HoTenSv = "Lê Quang Huy",
                            MaKhoa = "CNDL",
                            NamSinh = 2001,
                            QueQuan = "Nghệ An"
                        },
                        new
                        {
                            MaSv = 1026,
                            HoTenSv = "Nguyễn Thị Thu Hiền",
                            MaKhoa = "CNNL",
                            NamSinh = 2002,
                            QueQuan = "TP.HCM"
                        },
                        new
                        {
                            MaSv = 1027,
                            HoTenSv = "Trần Văn Toàn",
                            MaKhoa = "CNNL",
                            NamSinh = 2001,
                            QueQuan = "Đà Nẵng"
                        },
                        new
                        {
                            MaSv = 1028,
                            HoTenSv = "Phạm Thị Hồng Như",
                            MaKhoa = "CNNL",
                            NamSinh = 2003,
                            QueQuan = "Bình Định"
                        },
                        new
                        {
                            MaSv = 1029,
                            HoTenSv = "Đỗ Văn Tuấn",
                            MaKhoa = "CNNL",
                            NamSinh = 2002,
                            QueQuan = "Gia Lai"
                        },
                        new
                        {
                            MaSv = 1030,
                            HoTenSv = "Lê Thị Mỹ Linh",
                            MaKhoa = "CNNL",
                            NamSinh = 2001,
                            QueQuan = "Cà Mau"
                        },
                        new
                        {
                            MaSv = 1031,
                            HoTenSv = "Nguyễn Thị Hồng Hạnh",
                            MaKhoa = "CNMT",
                            NamSinh = 2003,
                            QueQuan = "Hải Phòng"
                        },
                        new
                        {
                            MaSv = 1032,
                            HoTenSv = "Trần Văn Đức",
                            MaKhoa = "CNMT",
                            NamSinh = 2002,
                            QueQuan = "Quảng Ninh"
                        },
                        new
                        {
                            MaSv = 1033,
                            HoTenSv = "Phạm Thị Bảo Ngọc",
                            MaKhoa = "CNMT",
                            NamSinh = 2001,
                            QueQuan = "Bình Thuận"
                        },
                        new
                        {
                            MaSv = 1034,
                            HoTenSv = "Đinh Văn Long",
                            MaKhoa = "CNMT",
                            NamSinh = 2002,
                            QueQuan = "Huế"
                        },
                        new
                        {
                            MaSv = 1035,
                            HoTenSv = "Hoàng Thị Ngọc Mai",
                            MaKhoa = "CNMT",
                            NamSinh = 2001,
                            QueQuan = "Sóc Trăng"
                        },
                        new
                        {
                            MaSv = 1036,
                            HoTenSv = "Nguyễn Thị Thu Trang",
                            MaKhoa = "CNHH",
                            NamSinh = 2002,
                            QueQuan = "Hà Nội"
                        },
                        new
                        {
                            MaSv = 1037,
                            HoTenSv = "Trần Văn Khánh",
                            MaKhoa = "CNHH",
                            NamSinh = 2001,
                            QueQuan = "Hải Phòng"
                        },
                        new
                        {
                            MaSv = 1038,
                            HoTenSv = "Phạm Thị Kim Oanh",
                            MaKhoa = "CNHH",
                            NamSinh = 2003,
                            QueQuan = "Nam Định"
                        },
                        new
                        {
                            MaSv = 1039,
                            HoTenSv = "Đỗ Văn Thắng",
                            MaKhoa = "CNHH",
                            NamSinh = 2002,
                            QueQuan = "Thanh Hóa"
                        },
                        new
                        {
                            MaSv = 1040,
                            HoTenSv = "Lê Thị Ngọc Hân",
                            MaKhoa = "CNHH",
                            NamSinh = 2001,
                            QueQuan = "Nghệ An"
                        },
                        new
                        {
                            MaSv = 1041,
                            HoTenSv = "Nguyễn Văn Toàn",
                            MaKhoa = "KHCB",
                            NamSinh = 2002,
                            QueQuan = "Thái Bình"
                        },
                        new
                        {
                            MaSv = 1042,
                            HoTenSv = "Trần Thị Bích Ngọc",
                            MaKhoa = "KHCB",
                            NamSinh = 2001,
                            QueQuan = "Bắc Ninh"
                        },
                        new
                        {
                            MaSv = 1043,
                            HoTenSv = "Phạm Văn Khôi",
                            MaKhoa = "KHCB",
                            NamSinh = 2003,
                            QueQuan = "Phú Thọ"
                        },
                        new
                        {
                            MaSv = 1044,
                            HoTenSv = "Đinh Thị Hồng Nhung",
                            MaKhoa = "KHCB",
                            NamSinh = 2002,
                            QueQuan = "Hà Tĩnh"
                        },
                        new
                        {
                            MaSv = 1045,
                            HoTenSv = "Hoàng Văn Trường",
                            MaKhoa = "KHCB",
                            NamSinh = 2001,
                            QueQuan = "Quảng Bình"
                        },
                        new
                        {
                            MaSv = 1046,
                            HoTenSv = "Nguyễn Thị Mỹ Hạnh",
                            MaKhoa = "LUAT",
                            NamSinh = 2002,
                            QueQuan = "TP.HCM"
                        },
                        new
                        {
                            MaSv = 1047,
                            HoTenSv = "Trần Văn Lực",
                            MaKhoa = "LUAT",
                            NamSinh = 2003,
                            QueQuan = "Đồng Nai"
                        },
                        new
                        {
                            MaSv = 1048,
                            HoTenSv = "Phạm Thị Ngọc Ánh",
                            MaKhoa = "LUAT",
                            NamSinh = 2001,
                            QueQuan = "Bình Dương"
                        },
                        new
                        {
                            MaSv = 1049,
                            HoTenSv = "Đỗ Văn Thành",
                            MaKhoa = "LUAT",
                            NamSinh = 2002,
                            QueQuan = "Long An"
                        },
                        new
                        {
                            MaSv = 1050,
                            HoTenSv = "Lê Thị Thanh Thủy",
                            MaKhoa = "LUAT",
                            NamSinh = 2001,
                            QueQuan = "Tây Ninh"
                        },
                        new
                        {
                            MaSv = 1051,
                            HoTenSv = "Nguyễn Văn Hải",
                            MaKhoa = "NN",
                            NamSinh = 2002,
                            QueQuan = "Hải Phòng"
                        },
                        new
                        {
                            MaSv = 1052,
                            HoTenSv = "Trần Thị Ngọc Mai",
                            MaKhoa = "NN",
                            NamSinh = 2001,
                            QueQuan = "Quảng Ninh"
                        },
                        new
                        {
                            MaSv = 1053,
                            HoTenSv = "Phạm Văn Hòa",
                            MaKhoa = "NN",
                            NamSinh = 2003,
                            QueQuan = "Nghệ An"
                        },
                        new
                        {
                            MaSv = 1054,
                            HoTenSv = "Đỗ Thị Kim Cúc",
                            MaKhoa = "NN",
                            NamSinh = 2002,
                            QueQuan = "Hà Nội"
                        },
                        new
                        {
                            MaSv = 1055,
                            HoTenSv = "Lê Quang Bình",
                            MaKhoa = "NN",
                            NamSinh = 2001,
                            QueQuan = "Nam Định"
                        },
                        new
                        {
                            MaSv = 1056,
                            HoTenSv = "Nguyễn Thị Bích Ngọc",
                            MaKhoa = "QTKD",
                            NamSinh = 2002,
                            QueQuan = "TP.HCM"
                        },
                        new
                        {
                            MaSv = 1057,
                            HoTenSv = "Trần Văn Hoàng",
                            MaKhoa = "QTKD",
                            NamSinh = 2001,
                            QueQuan = "Đà Nẵng"
                        },
                        new
                        {
                            MaSv = 1058,
                            HoTenSv = "Phạm Thị Yến Nhi",
                            MaKhoa = "QTKD",
                            NamSinh = 2003,
                            QueQuan = "Bình Định"
                        },
                        new
                        {
                            MaSv = 1059,
                            HoTenSv = "Đinh Văn Quý",
                            MaKhoa = "QTKD",
                            NamSinh = 2002,
                            QueQuan = "Quảng Nam"
                        },
                        new
                        {
                            MaSv = 1060,
                            HoTenSv = "Hoàng Thị Diễm My",
                            MaKhoa = "QTKD",
                            NamSinh = 2001,
                            QueQuan = "Huế"
                        },
                        new
                        {
                            MaSv = 1061,
                            HoTenSv = "Nguyễn Văn Hưng",
                            MaKhoa = "TMDL",
                            NamSinh = 2002,
                            QueQuan = "Khánh Hòa"
                        },
                        new
                        {
                            MaSv = 1062,
                            HoTenSv = "Trần Thị Mỹ Linh",
                            MaKhoa = "TMDL",
                            NamSinh = 2001,
                            QueQuan = "Cần Thơ"
                        },
                        new
                        {
                            MaSv = 1063,
                            HoTenSv = "Phạm Văn Phước",
                            MaKhoa = "TMDL",
                            NamSinh = 2003,
                            QueQuan = "Tiền Giang"
                        },
                        new
                        {
                            MaSv = 1064,
                            HoTenSv = "Đỗ Thị Như Ý",
                            MaKhoa = "TMDL",
                            NamSinh = 2002,
                            QueQuan = "An Giang"
                        },
                        new
                        {
                            MaSv = 1065,
                            HoTenSv = "Lê Văn Dũng",
                            MaKhoa = "TMDL",
                            NamSinh = 2001,
                            QueQuan = "Đồng Tháp"
                        },
                        new
                        {
                            MaSv = 1066,
                            HoTenSv = "Nguyễn Văn Phát",
                            MaKhoa = "KTXD",
                            NamSinh = 2002,
                            QueQuan = "Hà Nội"
                        },
                        new
                        {
                            MaSv = 1067,
                            HoTenSv = "Trần Thị Thu Hằng",
                            MaKhoa = "KTXD",
                            NamSinh = 2001,
                            QueQuan = "Hải Phòng"
                        },
                        new
                        {
                            MaSv = 1068,
                            HoTenSv = "Phạm Văn Thịnh",
                            MaKhoa = "KTXD",
                            NamSinh = 2003,
                            QueQuan = "Thanh Hóa"
                        },
                        new
                        {
                            MaSv = 1069,
                            HoTenSv = "Đỗ Thị Mỹ Linh",
                            MaKhoa = "KTXD",
                            NamSinh = 2002,
                            QueQuan = "Nghệ An"
                        },
                        new
                        {
                            MaSv = 1070,
                            HoTenSv = "Lê Quang Trọng",
                            MaKhoa = "KTXD",
                            NamSinh = 2001,
                            QueQuan = "Bắc Giang"
                        },
                        new
                        {
                            MaSv = 1071,
                            HoTenSv = "Nguyễn Thị Thu Hà",
                            MaKhoa = "TCKT",
                            NamSinh = 2002,
                            QueQuan = "TP.HCM"
                        },
                        new
                        {
                            MaSv = 1072,
                            HoTenSv = "Trần Văn Sơn",
                            MaKhoa = "TCKT",
                            NamSinh = 2001,
                            QueQuan = "Đồng Nai"
                        },
                        new
                        {
                            MaSv = 1073,
                            HoTenSv = "Phạm Thị Ngọc Trâm",
                            MaKhoa = "TCKT",
                            NamSinh = 2003,
                            QueQuan = "Bình Dương"
                        },
                        new
                        {
                            MaSv = 1074,
                            HoTenSv = "Đỗ Văn Hòa",
                            MaKhoa = "TCKT",
                            NamSinh = 2002,
                            QueQuan = "Long An"
                        },
                        new
                        {
                            MaSv = 1075,
                            HoTenSv = "Lê Thị Mỹ Dung",
                            MaKhoa = "TCKT",
                            NamSinh = 2001,
                            QueQuan = "Tây Ninh"
                        },
                        new
                        {
                            MaSv = 1076,
                            HoTenSv = "Nguyễn Văn Hậu",
                            MaKhoa = "DTQT",
                            NamSinh = 2003,
                            QueQuan = "Khánh Hòa"
                        },
                        new
                        {
                            MaSv = 1077,
                            HoTenSv = "Trần Thị Minh Ngọc",
                            MaKhoa = "DTQT",
                            NamSinh = 2002,
                            QueQuan = "Cần Thơ"
                        },
                        new
                        {
                            MaSv = 1078,
                            HoTenSv = "Phạm Văn Đạt",
                            MaKhoa = "DTQT",
                            NamSinh = 2001,
                            QueQuan = "Bình Định"
                        },
                        new
                        {
                            MaSv = 1079,
                            HoTenSv = "Đỗ Thị Bích Thủy",
                            MaKhoa = "DTQT",
                            NamSinh = 2002,
                            QueQuan = "An Giang"
                        },
                        new
                        {
                            MaSv = 1080,
                            HoTenSv = "Lê Văn Hồng",
                            MaKhoa = "DTQT",
                            NamSinh = 2001,
                            QueQuan = "Tiền Giang"
                        },
                        new
                        {
                            MaSv = 1081,
                            HoTenSv = "Nguyễn Thị Hồng Nhung",
                            MaKhoa = "CNSH_TP",
                            NamSinh = 2002,
                            QueQuan = "Hà Nội"
                        },
                        new
                        {
                            MaSv = 1082,
                            HoTenSv = "Trần Văn Thịnh",
                            MaKhoa = "CNSH_TP",
                            NamSinh = 2001,
                            QueQuan = "Hải Phòng"
                        },
                        new
                        {
                            MaSv = 1083,
                            HoTenSv = "Phạm Thị Minh Châu",
                            MaKhoa = "CNSH_TP",
                            NamSinh = 2003,
                            QueQuan = "Nghệ An"
                        },
                        new
                        {
                            MaSv = 1084,
                            HoTenSv = "Đỗ Văn Hùng",
                            MaKhoa = "CNSH_TP",
                            NamSinh = 2002,
                            QueQuan = "Thanh Hóa"
                        },
                        new
                        {
                            MaSv = 1085,
                            HoTenSv = "Lê Thị Bảo Ngọc",
                            MaKhoa = "CNSH_TP",
                            NamSinh = 2001,
                            QueQuan = "Huế"
                        },
                        new
                        {
                            MaSv = 1086,
                            HoTenSv = "Nguyễn Văn Dũng",
                            MaKhoa = "KHCNMT",
                            NamSinh = 2002,
                            QueQuan = "TP.HCM"
                        },
                        new
                        {
                            MaSv = 1087,
                            HoTenSv = "Trần Thị Cẩm Tú",
                            MaKhoa = "KHCNMT",
                            NamSinh = 2003,
                            QueQuan = "Đà Nẵng"
                        },
                        new
                        {
                            MaSv = 1088,
                            HoTenSv = "Phạm Văn Lộc",
                            MaKhoa = "KHCNMT",
                            NamSinh = 2001,
                            QueQuan = "Bình Định"
                        },
                        new
                        {
                            MaSv = 1089,
                            HoTenSv = "Đinh Thị Thu Hằng",
                            MaKhoa = "KHCNMT",
                            NamSinh = 2002,
                            QueQuan = "Khánh Hòa"
                        },
                        new
                        {
                            MaSv = 1090,
                            HoTenSv = "Hoàng Văn Tùng",
                            MaKhoa = "KHCNMT",
                            NamSinh = 2001,
                            QueQuan = "Cần Thơ"
                        });
                });

            modelBuilder.Entity("InternshipManagement.Models.DeTai", b =>
                {
                    b.HasOne("InternshipManagement.Models.GiangVien", "GiangVien")
                        .WithMany("DeTais")
                        .HasForeignKey("MaGv")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("GiangVien");
                });

            modelBuilder.Entity("InternshipManagement.Models.GiangVien", b =>
                {
                    b.HasOne("InternshipManagement.Models.Khoa", "Khoa")
                        .WithMany("GiangViens")
                        .HasForeignKey("MaKhoa")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Khoa");
                });

            modelBuilder.Entity("InternshipManagement.Models.HuongDan", b =>
                {
                    b.HasOne("InternshipManagement.Models.DeTai", "DeTai")
                        .WithMany("HuongDans")
                        .HasForeignKey("MaDt")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("InternshipManagement.Models.GiangVien", "GiangVien")
                        .WithMany("HuongDans")
                        .HasForeignKey("MaGv")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("InternshipManagement.Models.SinhVien", "SinhVien")
                        .WithMany("HuongDans")
                        .HasForeignKey("MaSv")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("DeTai");

                    b.Navigation("GiangVien");

                    b.Navigation("SinhVien");
                });

            modelBuilder.Entity("InternshipManagement.Models.SinhVien", b =>
                {
                    b.HasOne("InternshipManagement.Models.Khoa", "Khoa")
                        .WithMany("SinhViens")
                        .HasForeignKey("MaKhoa")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Khoa");
                });

            modelBuilder.Entity("InternshipManagement.Models.DeTai", b =>
                {
                    b.Navigation("HuongDans");
                });

            modelBuilder.Entity("InternshipManagement.Models.GiangVien", b =>
                {
                    b.Navigation("DeTais");

                    b.Navigation("HuongDans");
                });

            modelBuilder.Entity("InternshipManagement.Models.Khoa", b =>
                {
                    b.Navigation("GiangViens");

                    b.Navigation("SinhViens");
                });

            modelBuilder.Entity("InternshipManagement.Models.SinhVien", b =>
                {
                    b.Navigation("HuongDans");
                });
#pragma warning restore 612, 618
        }
    }
}
