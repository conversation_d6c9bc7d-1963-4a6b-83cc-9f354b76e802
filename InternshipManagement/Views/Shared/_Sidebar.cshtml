﻿@* Views/Shared/_Sidebar.cshtml *@
@using System.Security.Claims

@{
    var isAuth = User?.Identity?.IsAuthenticated ?? false;
    var role = User.FindFirst(ClaimTypes.Role)?.Value; // "Admin" | "GiangVien" | "SinhVien"
}

<div class="card shadow-sm">
    <div class="card-header bg-primary text-white fw-semibold">
        Điều hướng nhanh
    </div>

    <ul class="list-group list-group-flush">
        @if (!isAuth)
        {
            <li class="list-group-item">
                <a class="text-primary fw-semibold d-flex align-items-center" href="/DeTai">
                    <i class="bi bi-journal-text me-2"></i> Danh sách Đề tài
                </a>
            </li>
            <li class="list-group-item">
                <a class="text-primary fw-semibold d-flex align-items-center" asp-controller="Auth" asp-action="Login">
                    <i class="bi bi-box-arrow-in-right me-2"></i> <PERSON><PERSON><PERSON> nhập
                </a>
            </li>
        }
        else if (role == "Admin")
        {
            <li class="list-group-item">
                <a class="text-primary fw-semibold d-flex align-items-center" href="/SinhVien">
                    <i class="bi bi-people me-2"></i> Quản lý Sinh viên
                </a>
            </li>
            <li class="list-group-item">
                <a class="text-primary fw-semibold d-flex align-items-center" href="/GiangVien">
                    <i class="bi bi-person-badge me-2"></i> Quản lý Giảng viên
                </a>
            </li>
            <li class="list-group-item">
                <a class="text-primary fw-semibold d-flex align-items-center" href="/DeTai">
                    <i class="bi bi-journal-text me-2"></i> Quản lý Đề tài
                </a>
            </li>
            <li class="list-group-item">
                <a class="text-primary fw-semibold d-flex align-items-center" href="/ThongKe">
                    <i class="bi bi-graph-up me-2"></i> Thống kê
                </a>
            </li>
        }
        else if (role == "GiangVien")
        {
            <li class="list-group-item">
                <a class="text-primary fw-semibold d-flex align-items-center" href="/DeTai/Registrations">
                    <i class="bi bi-people me-2"></i> Danh sách Sinh viên đăng ký đề tài
                </a>
            </li>
            <li class="list-group-item">
                @* Gợi ý: /DeTai?mine=1 để lọc "đề tài của tôi" *@
                <a class="text-primary fw-semibold d-flex align-items-center" href="/DeTai/Manage">
                    <i class="bi bi-journal-check me-2"></i> Đề tài của tôi
                </a>
            </li>
            <li class="list-group-item">
                <a class="text-primary fw-semibold d-flex align-items-center" href="/ThongKe">
                    <i class="bi bi-graph-up me-2"></i> Thống kê
                </a>
            </li>
        }
        else if (role == "SinhVien")
        {
            <li class="list-group-item">
                <a class="text-primary fw-semibold d-flex align-items-center" href="/DeTai">
                    <i class="bi bi-journal-text me-2"></i> Danh sách Đề tài
                </a>
            </li>
            <li class="list-group-item">
                @* Gợi ý: /HuongDan?mine=1 để xem đề tài đã đăng ký (map theo SV hiện tại) *@
                <a class="text-primary fw-semibold d-flex align-items-center" href="/DeTai/MyTopics">
                    <i class="bi bi-bookmark-check me-2"></i> Đề tài đã đăng ký
                </a>
            </li>
        }
    </ul>
</div>
