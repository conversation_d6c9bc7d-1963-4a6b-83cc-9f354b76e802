﻿@* Views/Shared/_Layout.cshtml *@
<!DOCTYPE html>
<html lang="vi">

<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>@ViewData["Title"] - InternshipManagement</title>
    <link rel="stylesheet" href="~/lib/bootstrap/dist/css/bootstrap.min.css" />
    <link rel="stylesheet" href="~/css/site.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/lib/bootstrap-icons/font/bootstrap-icons.css" />
    <link href="https://cdn.syncfusion.com/ej2/29.1.33/bootstrap.css" rel="stylesheet" />
</head>

<body class="bg-light">
    <partial name="_Header" />

    <div class="container-xxl my-4">
        <div class="row">
            <aside class="col-12 col-lg-3 mb-4">
                <partial name="_Sidebar" />
            </aside>
            <main class="col-12 col-lg-9">
                @RenderBody()
            </main>
        </div>
    </div>

    <partial name="_Footer" />
    <script src="~/lib/jquery/dist/jquery.min.js"></script>
    <script src="~/lib/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <script src="~/js/site.js" asp-append-version="true"></script>
    <script src="https://cdn.syncfusion.com/ej2/29.1.33/dist/ej2.min.js"></script>
    <script src="~/lib/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
    <script src="~/lib/echarts/dist/echarts.min.js"></script>
    <ejs-scripts></ejs-scripts>
    @await RenderSectionAsync("Scripts", required: false)
</body>

</html>
