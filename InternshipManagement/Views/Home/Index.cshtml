﻿@using System.Collections.Generic;
@using System.Linq;
@using InternshipManagement.Models;
@using Syncfusion.EJ2.Diagrams;
@using Syncfusion.EJ2.Navigations;
@using Syncfusion.EJ2.Inputs;

@{
    ViewData["Title"] = "Home Page";
}

<section class="hero bg-primary-main text-white rounded-3 p-4 p-lg-5 shadow-sm">
    <div class="d-flex flex-column flex-lg-row align-items-lg-center">
        <div class="flex-grow-1">
            <h1 class="display-5 fw-bold mb-3">Chào mừng bạn đến với hệ thống quản lý sinh viên thực tập</h1>
            <p class="lead mb-4">
                Quản lý Khoa, Giảng viên, Sinh viên, Đ<PERSON> tài; tra cứu và xuất báo cáo nhanh chóng.
            </p>
            <div class="d-flex gap-2">
                <a class="btn btn-light btn-lg fw-semibold" href="/SinhVien">Bắt đầu quản lý</a>
                <a class="btn btn-outline-light btn-lg" target="_blank" href="https://learn.microsoft.com/aspnet/core">
                    Tài liệu ASP.NET Core
                </a>
                <a class="btn btn-outline-light btn-lg" target="_blank" href="https://ej2.syncfusion.com/home/<USER>">
                    Tài liệu SyncFusion ASP MVC
                </a>
            </div>
        </div>
    </div>
</section>



