﻿@model InternshipManagement.Models.ViewModels.DeTaiIndexVm
@using Microsoft.AspNetCore.Routing
@{
    ViewData["Title"] = "Danh sách Đề tài";
}


<div class="card shadow-sm">
    <div class="card-header bg-white d-flex align-items-center justify-content-between">
        <h5 class="mb-0"><PERSON>h sách Đề tài</h5>
        <div class="small text-muted">Tổng: <strong>@Model.Items.Count</strong> bản ghi</div>
    </div>

    <div class="card shadow-sm mb-3">

        <div class="card-body">
            <form method="get" id="filterForm" class="row g-3">

                 <div class="col-md-4">
                    <label class="form-label">Từ khóa</label>
                    <input class="form-control" asp-for="Filter.Keyword" placeholder="Tên đề tài..." />
                </div>
               
                <div class="col-md-2">
                    <label class="form-label"><PERSON><PERSON> phí từ</label>
                    <input class="form-control" asp-for="Filter.MinKinhPhi" type="number" />
                </div>
                <div class="col-md-2">
                    <label class="form-label">đến</label>
                    <input class="form-control" asp-for="Filter.MaxKinhPhi" type="number" />
                </div>

                <div class="col-md-4">
                    <label class="form-label">Tình trạng</label>
                    <div class="searchable-dropdown" id="statusDropdownIndex">
                        <input type="hidden" asp-for="Filter.TinhTrang" />
                        <input type="text" class="form-control" autocomplete="off">
                        <i class="bi bi-chevron-down dropdown-arrow"></i>
                        <div class="dropdown-menu">
                            <div class="dropdown-options">
                                <a class="dropdown-item" href="#" data-value="All">-- Tất cả --</a>
                                <a class="dropdown-item" href="#" data-value="OnlyNoStudent">Chưa có sinh viên đăng ký</a>
                                <a class="dropdown-item" href="#" data-value="Full">Đã đủ số lượng</a>
                                <a class="dropdown-item" href="#" data-value="OnlyNotEnough">Còn trống</a>
                            </div>
                        </div>
                    </div>
                </div>


                <!-- Hàng 1 -->
                @if (!(User?.IsInRole("SinhVien") ?? false))
                {
                    <div class="col-md-4">
                        <label class="form-label">Khoa</label>
                        <div class="searchable-dropdown" id="khoaDropdown">
                            <input type="hidden" asp-for="Filter.MaKhoa" />
                            <input type="text" class="form-control" placeholder="Tìm kiếm khoa..." autocomplete="off">
                            <i class="bi bi-chevron-down dropdown-arrow"></i>
                            <div class="dropdown-menu">
                                <div class="dropdown-options">
                                    <a class="dropdown-item" href="#" data-value="">-- Tất cả --</a>
                                </div>
                            </div>
                        </div>
                    </div>
                }
                else
                {
                    <input type="hidden" asp-for="Filter.MaKhoa" value="@User?.FindFirst("MaKhoa")?.Value" />
                }
                <div class="col-md-4">
                    <label class="form-label">Giảng viên</label>
                    <div class="searchable-dropdown" id="giangVienDropdown">
                        <input type="hidden" asp-for="Filter.MaGv" />
                        <input type="text" class="form-control" placeholder="Tìm kiếm giảng viên..." autocomplete="off">
                        <i class="bi bi-chevron-down dropdown-arrow"></i>
                        <div class="dropdown-menu">
                            <div class="dropdown-options">
                                <a class="dropdown-item" href="#" data-value="">-- Tất cả --</a>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <label class="form-label">Học kỳ/Năm học</label>
                    <div class="searchable-dropdown" id="termDropdownIndex">
                        <input type="hidden" value="@(Model.Filter.HocKy.HasValue && !string.IsNullOrEmpty(Model.Filter.NamHoc) ? ($"{Model.Filter.HocKy}|{Model.Filter.NamHoc}") : "")" />
                        <input type="text" class="form-control" autocomplete="off">
                        <i class="bi bi-chevron-down dropdown-arrow"></i>
                        <div class="dropdown-menu">
                            <div class="dropdown-options">
                                <a class="dropdown-item" href="#" data-value="">-- Tất cả --</a>
                            </div>
                        </div>
                    </div>
                    <input type="hidden" asp-for="Filter.HocKy" />
                    <input type="hidden" asp-for="Filter.NamHoc" />
                </div>

                <div class="col-md-12 d-flex justify-content-end gap-2">
                      @if (User.Identity?.IsAuthenticated == true
                    && (User.IsInRole("GiangVien") || User.IsInRole("Admin")))
                {
                    <div class="col-auto d-flex align-items-end">
                        <button type="button" class="btn btn-success d-flex align-items-center px-3 py-2" data-bs-toggle="modal" data-bs-target="#exportModal">
                            <i class="bi bi-table me-1"></i> Export
                        </button>
                    </div>
                }

                        <div class="col-auto d-flex align-items-end">
                    <button type="submit" form="filterForm" class="btn btn-primary d-flex align-items-center px-3 py-2">
                        <i class="bi bi-funnel me-1"></i> Lọc
                    </button>
                </div>
                <div class="col-auto d-flex align-items-end">
                    <a asp-action="Index" class="btn btn-outline-secondary d-flex align-items-center px-3  py-2">
                        <i class="bi-arrow-clockwise me-1"></i> Hủy
                    </a>
                </div>
                </div>
            </form>
        </div>
    </div>


    <!-- Responsive Grid Container -->
    <div class="grid-responsive">
        <div class="grid-container">
            <ejs-grid id="DeTaiGrid" dataSource="@Model.Items" allowPaging="true" allowSorting="true" allowMultiSorting="true" allowFiltering="false" allowResizing="true" gridLines="Both" height="600" width="100%" grid-lines="Both" enable-hover="false" enable-alt-row="true">
                <e-grid-pagesettings pageSize="20" pageSizes="new List<int>{10,20,50,100,500}"></e-grid-pagesettings> 
                <e-grid-columns>
                <e-grid-column field="MaDt" headerText="Mã đề tài" width="120" template="#maDtTemplate"></e-grid-column>
                <e-grid-column field="TenDt" headerText="Tên đề tài" width="300"></e-grid-column>
                <e-grid-column headerText="Khoa" width="150" template="#khoaTemplate"></e-grid-column>
                <e-grid-column headerText="Số lượng" width="120" textAlign="Center" template="#soLuongTemplate"></e-grid-column>
                <e-grid-column headerText="Học kỳ" width="120" template="#hocKyTemplate"></e-grid-column>
                <e-grid-column headerText="Kinh phí" width="120" textAlign="Right" template="#kinhPhiTemplate"></e-grid-column>
                <e-grid-column headerText="Hành động" width="120" textAlign="Center" template="#actionTemplate"></e-grid-column>
            </e-grid-columns>
        </ejs-grid>
    </div>


    @* Templates for custom column rendering *@
    <script id="maDtTemplate" type="text/x-template">
        <span class="ma-dt-code">${MaDt}</span>
    </script>

    <script id="khoaTemplate" type="text/x-template">
        ${KhoaOptionVm.TenKhoa}
    </script>

    <script id="soLuongTemplate" type="text/x-template">
        <span class="so-luong-badge">${SoChapNhan}/${SoLuongToiDa}</span>
    </script>

    <script id="hocKyTemplate" type="text/x-template">
        HK${HocKy} / ${NamHoc}
    </script>

    <script id="kinhPhiTemplate" type="text/x-template">
        <span class="kinh-phi-text">${KinhPhi} triệu</span>
    </script>

    <script id="actionTemplate" type="text/x-template">
        <a class="btn btn-sm btn-primary text-white" href="/DeTai/Details/${MaDt}" title="Xem chi tiết">
                                    <i class="bi bi-eye"></i> Xem
                                </a>
    </script>
    @* EJ2 Grid component *@
</div>

@* Export Modal *@
<div class="modal fade" id="exportModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-lg">
        <div class="modal-content border-0">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-file-earmark-spreadsheet me-2"></i>
                    Export danh sách đề tài
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Đóng"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-12">
                        <div class="alert alert-info">
                            <i class="bi bi-info-circle me-1"></i>
                            <strong>Thông tin export:</strong><br/>
                            File Excel sẽ được tạo với các điều kiện lọc hiện tại và các trường được chọn bên dưới.
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-12">
                        <label class="form-label fw-bold">Chọn và sắp xếp thứ tự các cột:</label>
                        <div class="small text-muted mb-2">
                            <i class="bi bi-info-circle me-1"></i>
                            Kéo thả để sắp xếp thứ tự hiển thị. Bỏ chọn để ẩn cột.
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-12">
                        <div id="sortableColumnsDt" class="sortable-columns">
                            <div class="column-item" data-column="includeMaDt">
                                <div class="column-handle">
                                    <i class="bi bi-grip-vertical"></i>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="includeMaDt" value="true" id="chkMaDt" checked>
                                    <input type="hidden" name="includeMaDt" value="false">
                                    <label class="form-check-label" for="chkMaDt">
                                        <i class="bi bi-tag me-1"></i> Mã đề tài
                                    </label>
                                </div>
                            </div>
                            
                            <div class="column-item" data-column="includeTenDt">
                                <div class="column-handle">
                                    <i class="bi bi-grip-vertical"></i>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="includeTenDt" value="true" id="chkTenDt" checked>
                                    <input type="hidden" name="includeTenDt" value="false">
                                    <label class="form-check-label" for="chkTenDt">
                                        <i class="bi bi-file-text me-1"></i> Tên đề tài
                                    </label>
                                </div>
                            </div>
                            
                            <div class="column-item" data-column="includeGiangVien">
                                <div class="column-handle">
                                    <i class="bi bi-grip-vertical"></i>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="includeGiangVien" value="true" id="chkGiangVien" checked>
                                    <input type="hidden" name="includeGiangVien" value="false">
                                    <label class="form-check-label" for="chkGiangVien">
                                        <i class="bi bi-person me-1"></i> Giảng viên
                                    </label>
                                </div>
                            </div>
                            
                            <div class="column-item" data-column="includeKhoa">
                                <div class="column-handle">
                                    <i class="bi bi-grip-vertical"></i>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="includeKhoa" value="true" id="chkKhoa" checked>
                                    <input type="hidden" name="includeKhoa" value="false">
                                    <label class="form-check-label" for="chkKhoa">
                                        <i class="bi bi-building me-1"></i> Khoa
                                    </label>
                                </div>
                            </div>
                            
                            <div class="column-item" data-column="includeHocKy">
                                <div class="column-handle">
                                    <i class="bi bi-grip-vertical"></i>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="includeHocKy" value="true" id="chkHocKy" checked>
                                    <input type="hidden" name="includeHocKy" value="false">
                                    <label class="form-check-label" for="chkHocKy">
                                        <i class="bi bi-calendar me-1"></i> Học kỳ/Năm học
                                    </label>
                                </div>
                            </div>
                            
                            <div class="column-item" data-column="includeSoLuong">
                                <div class="column-handle">
                                    <i class="bi bi-grip-vertical"></i>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="includeSoLuong" value="true" id="chkSoLuong" checked>
                                    <input type="hidden" name="includeSoLuong" value="false">
                                    <label class="form-check-label" for="chkSoLuong">
                                        <i class="bi bi-hash me-1"></i> Số lượng
                                    </label>
                                </div>
                            </div>
                            
                            <div class="column-item" data-column="includeKinhPhi">
                                <div class="column-handle">
                                    <i class="bi bi-grip-vertical"></i>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="includeKinhPhi" value="true" id="chkKinhPhi" checked>
                                    <input type="hidden" name="includeKinhPhi" value="false">
                                    <label class="form-check-label" for="chkKinhPhi">
                                        <i class="bi bi-currency-dollar me-1"></i> Kinh phí
                                    </label>
                                </div>
                            </div>
                            
                            <div class="column-item" data-column="includeNoiThucTap">
                                <div class="column-handle">
                                    <i class="bi bi-grip-vertical"></i>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="includeNoiThucTap" value="true" id="chkNoiThucTap" checked>
                                    <input type="hidden" name="includeNoiThucTap" value="false">
                                    <label class="form-check-label" for="chkNoiThucTap">
                                        <i class="bi bi-geo-alt me-1"></i> Nơi thực tập
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="mb-3">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" name="includeSinhVien" value="true" id="chkSinhVien">
                        <input type="hidden" name="includeSinhVien" value="false">
                        <label class="form-check-label fw-bold" for="chkSinhVien">
                            <i class="bi bi-people me-1"></i> Kèm thông tin sinh viên
                        </label>
                    </div>
                    <div class="ps-4 mt-2" id="sinhVienOptions" style="display: none;">
                        <div id="sortableColumnsSv" class="sortable-columns">
                            <div class="column-item" data-column="includeSvHoTen">
                                <div class="column-handle">
                                    <i class="bi bi-grip-vertical"></i>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="includeSvHoTen" value="true" id="chkSvHoTen" checked>
                                    <input type="hidden" name="includeSvHoTen" value="false">
                                    <label class="form-check-label" for="chkSvHoTen">
                                        <i class="bi bi-person me-1"></i> Họ tên sinh viên
                                    </label>
                                </div>
                            </div>

                            <div class="column-item" data-column="includeSvMaSv">
                                <div class="column-handle">
                                    <i class="bi bi-grip-vertical"></i>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="includeSvMaSv" value="true" id="chkSvMaSv" checked>
                                    <input type="hidden" name="includeSvMaSv" value="false">
                                    <label class="form-check-label" for="chkSvMaSv">
                                        <i class="bi bi-person-badge me-1"></i> Mã sinh viên
                                    </label>
                                </div>
                            </div>

                            <div class="column-item" data-column="includeSvTrangThai">
                                <div class="column-handle">
                                    <i class="bi bi-grip-vertical"></i>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="includeSvTrangThai" value="true" id="chkSvTrangThai" checked>
                                    <input type="hidden" name="includeSvTrangThai" value="false">
                                    <label class="form-check-label" for="chkSvTrangThai">
                                        <i class="bi bi-info-circle me-1"></i> Trạng thái
                                    </label>
                                </div>
                            </div>

                            <div class="column-item" data-column="includeSvNgayDK">
                                <div class="column-handle">
                                    <i class="bi bi-grip-vertical"></i>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="includeSvNgayDK" value="true" id="chkSvNgayDK" checked>
                                    <input type="hidden" name="includeSvNgayDK" value="false">
                                    <label class="form-check-label" for="chkSvNgayDK">
                                        <i class="bi bi-calendar-check me-1"></i> Ngày đăng ký
                                    </label>
                                </div>
                            </div>

                            <div class="column-item" data-column="includeSvKetQua">
                                <div class="column-handle">
                                    <i class="bi bi-grip-vertical"></i>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="includeSvKetQua" value="true" id="chkSvKetQua" checked>
                                    <input type="hidden" name="includeSvKetQua" value="false">
                                    <label class="form-check-label" for="chkSvKetQua">
                                        <i class="bi bi-trophy me-1"></i> Kết quả
                                    </label>
                                </div>
                            </div>

                            <div class="column-item" data-column="includeSvGhiChu">
                                <div class="column-handle">
                                    <i class="bi bi-grip-vertical"></i>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="includeSvGhiChu" value="true" id="chkSvGhiChu" checked>
                                    <input type="hidden" name="includeSvGhiChu" value="false">
                                    <label class="form-check-label" for="chkSvGhiChu">
                                        <i class="bi bi-chat-text me-1"></i> Ghi chú
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row mt-3">
                    <div class="col-12">
                        <div class="d-flex justify-content-between">
                            <div>
                                <button type="button" class="btn btn-outline-primary btn-sm" id="selectAllFields">
                                    <i class="bi bi-check-all me-1"></i> Chọn tất cả
                                </button>
                                <button type="button" class="btn btn-outline-secondary btn-sm ms-2" id="clearAllFields">
                                    <i class="bi bi-x-square me-1"></i> Bỏ chọn tất cả
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Huỷ</button>
                <button type="button" id="btnExport" class="btn btn-success text-white">
                    <i class="bi bi-file-earmark-spreadsheet me-1"></i> Export Excel
                </button>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <link href="~/css/searchable-dropdown.css" rel="stylesheet" />
    <script src="~/js/searchable-dropdown.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>
    <style>
        .sortable-columns {
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            padding: 0.5rem;
            background-color: #f8f9fa;
        }
        
        .column-item {
            display: flex;
            align-items: center;
            padding: 0.5rem;
            margin-bottom: 0.25rem;
            background-color: white;
            border: 1px solid #e9ecef;
            border-radius: 0.25rem;
            cursor: move;
            transition: all 0.2s ease;
        }
        
        .column-item:hover {
            background-color: #f8f9fa;
            border-color: #007bff;
        }
        
        .column-item.sortable-ghost {
            opacity: 0.4;
            background-color: #e3f2fd;
        }
        
        .column-item.sortable-chosen {
            background-color: #e3f2fd;
            border-color: #007bff;
            box-shadow: 0 2px 4px rgba(0,123,255,0.2);
        }
        
        .column-handle {
            margin-right: 0.75rem;
            color: #6c757d;
            cursor: grab;
        }
        
        .column-handle:active {
            cursor: grabbing;
        }
        
        .column-item .form-check {
            flex: 1;
            margin: 0;
        }
        
        .column-item .form-check-label {
            cursor: pointer;
            margin: 0;
        }
    </style>
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            // Handle filter form submission
            const filterForm = document.getElementById('filterForm');
            filterForm.addEventListener('submit', function(e) {
                e.preventDefault();

                // Build query string with filter parameters (no paging)
                var formData = new FormData(this);
                var queryParams = new URLSearchParams();

                for (var pair of formData.entries()) {
                    queryParams.append(pair[0], pair[1]);
                }

                window.location.href = '@Url.Action("Index", "DeTai")?' + queryParams.toString();
            });

            // Initialize SearchableDropdown for khoa (skip for students)
            const khoaEl = document.getElementById('khoaDropdown');
            let khoaDropdown = null;
            if (khoaEl) {
                khoaDropdown = new SearchableDropdown('khoaDropdown', {
                    placeholder: 'Tìm kiếm khoa...',
                    searchUrl: '@Url.Action("Search", "Khoa")',
                    searchParam: 'q',
                    debounceDelay: 300,
                    allowClear: true
                });
            }

            // Initialize SearchableDropdown for giảng viên
            const isStudent = !document.getElementById('khoaDropdown');
            const giangVienDropdown = new SearchableDropdown('giangVienDropdown', {
                placeholder: isStudent ? '' : 'Tìm kiếm giảng viên...',
                searchUrl: isStudent ? '@Url.Action("GetLecturersForCurrentStudent", "DeTai")' : '@Url.Action("GetLecturersByDepartment", "DeTai")',
                searchParam: 'q',
                filterParam: 'maKhoa',
                debounceDelay: 300,
                allowClear: true,
                labelFormatter: function(item) {
                    if (item) {
                        const mgv = item.MaGv ?? item.maGv;
                        const hten = item.HoTenGv ?? item.hoTenGv;
                        if (mgv !== undefined && hten !== undefined) {
                            return { value: String(mgv), text: `${mgv} - ${hten}` };
                        }
                    }
                    return null;
                }
            });
            

            // Handle khoa change to update lecturers
            if (khoaEl) {
                document.getElementById('khoaDropdown').addEventListener('selectionChanged', function(e) {
                    const selectedMaKhoa = e.detail.value;
                    giangVienDropdown.setFilterValue(selectedMaKhoa);
                });
            } else {
                // Student: use dedicated API; ignore filterParam
                giangVienDropdown.setFilterValue('');
                giangVienDropdown.searchData('');
                // show '-- Tất cả --' like combobox behavior
                const gvInput = document.querySelector('#giangVienDropdown input.form-control');
                const gvHidden = document.querySelector('#giangVienDropdown input[type="hidden"][name="Filter.MaGv"]');
                if (gvInput && (!gvInput.value || gvInput.value.trim() === '') && (!gvHidden || !gvHidden.value)) {
                    gvInput.value = '-- Tất cả --';
                }
            }
            // Term dropdown (combine HK/Năm học)
            const termEl = document.getElementById('termDropdownIndex');
            const termDd = new SearchableDropdown('termDropdownIndex', { placeholder: '', searchUrl: null, allowClear: true });
            function generateTerms(baseYear) {
                const out = [];
                for (let y = baseYear - 5; y <= baseYear; y++) {
                    const nh = `${y}-${y + 1}`;
                    for (let hk = 1; hk <= 3; hk++) out.push({ value: `${hk}|${nh}`, text: `HK${hk} (${nh})`, hk, nh });
                }
                return out;
            }
            function filterTerms(all, query) {
                if (!query) return all;
                const q = String(query).toLowerCase().replace(/\s+/g, '');
                const m = q.match(/^(\d{4})$/);
                if (m) {
                    const Y = parseInt(m[1], 10);
                    const expanded = [];
                    for (let hk = 1; hk <= 3; hk++) expanded.push({ value: `${hk}|${Y}-${Y + 1}`, text: `HK${hk} (${Y}-${Y + 1})`, hk, nh: `${Y}-${Y + 1}` });
                    for (let hk = 1; hk <= 3; hk++) expanded.push({ value: `${hk}|${Y - 1}-${Y}`, text: `HK${hk} (${Y - 1}-${Y})`, hk, nh: `${Y - 1}-${Y}` });
                    return expanded;
                }
                return all.filter(x => x.text.toLowerCase().includes(q));
            }
            const nowY = new Date().getFullYear();
            let allTerms = generateTerms(nowY).sort((a, b) => (a.nh < b.nh ? 1 : a.nh > b.nh ? -1 : b.hk - a.hk));
            termDd.searchData = async function (query) {
                const options = filterTerms(allTerms, query);
                await termDd.updateOptions(options.map(o => ({ value: o.value, text: o.text })));
            };
            termDd.searchData('');
            termEl.addEventListener('selectionChanged', function (e) {
                const val = e?.detail?.value ?? '';
                const [hk, nh] = val.includes('|') ? val.split('|') : ['', ''];
                const hkInput = document.querySelector('input[name="Filter.HocKy"]');
                const nhInput = document.querySelector('input[name="Filter.NamHoc"]');
                if (hkInput) hkInput.value = hk;
                if (nhInput) nhInput.value = nh;
            });
            // Prevent empty state: if user clears, revert to All
            const termTextInput = termEl?.querySelector('input.form-control');
            termTextInput?.addEventListener('input', function(){
                if (this.value.trim() === '') {
                    const hkInput = document.querySelector('input[name="Filter.HocKy"]');
                    const nhInput = document.querySelector('input[name="Filter.NamHoc"]');
                    if (hkInput) hkInput.value = '';
                    if (nhInput) nhInput.value = '';
                    this.value = '-- Tất cả --';
                }
            });

            // Status dropdown custom on Index
            const statusEl = document.getElementById('statusDropdownIndex');
            const statusDd = new SearchableDropdown('statusDropdownIndex', { placeholder: '', searchUrl: null, allowClear: false });
            // Bind static options from DOM to enable selection and search without remote URL
            function getStatusOptionsFromDom() {
                const opts = [];
                statusEl.querySelectorAll('.dropdown-options a.dropdown-item').forEach(a => {
                    const v = a.getAttribute('data-value') ?? '';
                    const t = a.textContent ?? '';
                    opts.push({ value: v, text: t });
                });
                return opts;
            }
            statusDd.searchData = async function(query){
                const all = getStatusOptionsFromDom();
                const q = (query || '').toLowerCase();
                const filtered = all.filter(o => !q || o.text.toLowerCase().includes(q) || o.value === q);
                await statusDd.updateOptions(filtered);
            };
            // Initialize option click handlers
            statusDd.searchData('');
            // Initialize display only
            // Initialize display and hidden
            (function initStatusDisplay(){
                const hidden = document.querySelector('input[name="Filter.TinhTrang"]');
                const input = statusEl?.querySelector('input.form-control');
                if (!hidden || !input) return;
                // Keep enum string values (All, IsFull, OnlyNoStudent, OnlyFull, OnlyNotEnough)
                const currentVal = hidden.value || 'All';
                const anchor = statusEl.querySelector(`.dropdown-options a.dropdown-item[data-value="${currentVal}"]`);
                input.value = anchor ? anchor.textContent : '-- Tất cả --';
            })();
            statusEl.addEventListener('selectionChanged', function (e) {
                const val = e?.detail?.value ?? 'All';
                const input = this.querySelector('input.form-control');
                const hidden = document.querySelector('input[name="Filter.TinhTrang"]');
                if (hidden) hidden.value = val;
                const anchor = this.querySelector(`.dropdown-options a.dropdown-item[data-value="${val}"]`);
                if (input) input.value = anchor ? anchor.textContent : '-- Tất cả --';
            });
            // Prevent empty state: if user clears text, revert to All
            const statusTextInput = statusEl?.querySelector('input.form-control');
            statusTextInput?.addEventListener('input', function(){
                if (this.value.trim() === '') {
                    const hidden = document.querySelector('input[name=\"Filter.TinhTrang\"]');
                    if (hidden) hidden.value = 'All';
                    this.value = '-- Tất cả --';
                }
            });

            // Helpers to default to '-- Tất cả --' and behave like comboboxes
            function setAllIfEmpty(container, hiddenSelector) {
                const input = container?.querySelector('input.form-control');
                const hidden = hiddenSelector ? document.querySelector(hiddenSelector) : container?.querySelector('input[type="hidden"]');
                if (input && (!input.value || input.value.trim() === '') && (!hidden || !hidden.value || hidden.value === '0')) {
                    input.value = '-- Tất cả --';
                }
            }
            function installComboBehavior(container, hiddenSelector) {
                const input = container?.querySelector('input.form-control');
                const hidden = hiddenSelector ? document.querySelector(hiddenSelector) : container?.querySelector('input[type="hidden"]');
                if (!input) return;
                input.addEventListener('focus', function () {
                    if (this.value.trim() === '-- Tất cả --') this.value = '';
                });
                input.addEventListener('blur', function () {
                    const hv = hidden ? hidden.value : '';
                    if (this.value.trim() === '' && (!hv || hv === '0')) {
                        this.value = '-- Tất cả --';
                    }
                });
            }

            // Initialize display values (normalize enum string 'All' -> '0')
            // Term
            const termInput = termEl?.querySelector('input.form-control');
            const hkHidden = document.querySelector('input[name="Filter.HocKy"]');
            const nhHidden = document.querySelector('input[name="Filter.NamHoc"]');
            if (termInput) {
                if (hkHidden?.value && nhHidden?.value) {
                    termInput.value = `HK${hkHidden.value} (${nhHidden.value})`;
                } else {
                    termInput.value = '-- Tất cả --';
                }
            }
            // Status
            const statusInput = statusEl?.querySelector('input.form-control');
            const stHidden = document.querySelector('input[name="Filter.TinhTrang"]');
            if (statusInput) {
                const stVal = stHidden?.value || 'All';
                const anchor = statusEl.querySelector(`.dropdown-options a.dropdown-item[data-value="${stVal}"]`);
                statusInput.value = anchor ? anchor.textContent : '-- Tất cả --';
            }

            // Apply combobox behaviors
            setAllIfEmpty(termEl, 'input[name="Filter.HocKy"]');
            setAllIfEmpty(statusEl, 'input[name="Filter.TinhTrang"]');
            installComboBehavior(termEl, 'input[name="Filter.HocKy"]');
            installComboBehavior(statusEl, 'input[name="Filter.TinhTrang"]');

            // Listen for selection changes
            document.getElementById('giangVienDropdown').addEventListener('selectionChanged', function(e) {
                console.log('Giảng viên selected:', e.detail);
            });

            // Initialize sortable columns
            const sortableColumnsDt = document.getElementById('sortableColumnsDt');
            const sortableDt = new Sortable(sortableColumnsDt, {
                handle: '.column-handle',
                animation: 150,
                ghostClass: 'sortable-ghost',
                chosenClass: 'sortable-chosen',
                onEnd: function(evt) {
                    // Update column order when drag ends
                    updateColumnOrder();
                }
            });

            // Sortable for student columns
            const sortableColumnsSv = document.getElementById('sortableColumnsSv');
            if (sortableColumnsSv) {
                const sortableSv = new Sortable(sortableColumnsSv, {
                    handle: '.column-handle',
                    animation: 150,
                    ghostClass: 'sortable-ghost',
                    chosenClass: 'sortable-chosen',
                    onEnd: function() { updateColumnOrder(); }
                });
            }

            // Export modal logic with separate actions for student details
            const exportModalEl = document.getElementById('exportModal');
            const modal = new bootstrap.Modal(exportModalEl);
            const urlExport = '@Url.Action("Export","DeTai")';
            const urlExportChiTiet = '@Url.Action("ExportChiTiet","DeTai")';

            // Handle "Kèm thông tin sinh viên" checkbox
            document.getElementById('chkSinhVien')?.addEventListener('change', function() {
                const sinhVienOptions = document.getElementById('sinhVienOptions');
                const svCheckboxes = sinhVienOptions.querySelectorAll('input[type="checkbox"]');

                if (this.checked) {
                    sinhVienOptions.style.display = 'block';
                    svCheckboxes.forEach(cb => {
                        cb.disabled = false;
                        cb.checked = true;
                    });
                } else {
                    sinhVienOptions.style.display = 'none';
                    svCheckboxes.forEach(cb => {
                        cb.disabled = true;
                        cb.checked = false;
                    });
                }
            });

            // Function to update column order
            function updateColumnOrder() {
                // DeTai columns order
                const dtItems = document.querySelectorAll('#sortableColumnsDt .column-item');
                const dtOrder = Array.from(dtItems).map(item => item.getAttribute('data-column'));
                // SinhVien columns order
                const svItems = document.querySelectorAll('#sortableColumnsSv .column-item');
                const svOrder = Array.from(svItems).map(item => item.getAttribute('data-column'));

                // Store orders in hidden inputs
                let orderDt = document.getElementById('columnOrder');
                if (!orderDt) {
                    orderDt = document.createElement('input');
                    orderDt.type = 'hidden';
                    orderDt.name = 'columnOrder';
                    orderDt.id = 'columnOrder';
                    document.getElementById('exportModal').appendChild(orderDt);
                }
                orderDt.value = dtOrder.join(',');

                let orderSv = document.getElementById('studentColumnOrder');
                if (!orderSv) {
                    orderSv = document.createElement('input');
                    orderSv.type = 'hidden';
                    orderSv.name = 'studentColumnOrder';
                    orderSv.id = 'studentColumnOrder';
                    document.getElementById('exportModal').appendChild(orderSv);
                }
                orderSv.value = svOrder.join(',');
            }

            // Select all fields button
            document.getElementById('selectAllFields')?.addEventListener('click', function() {
                const checkboxes = document.querySelectorAll('#exportModal input[type="checkbox"]:not([disabled])');
                checkboxes.forEach(cb => cb.checked = true);
            });

            // Clear all fields button
            document.getElementById('clearAllFields')?.addEventListener('click', function() {
                const checkboxes = document.querySelectorAll('#exportModal input[type="checkbox"]:not([disabled])');
                checkboxes.forEach(cb => cb.checked = false);
                // Also uncheck the main sinh vien checkbox
                document.getElementById('chkSinhVien').checked = false;
                document.getElementById('sinhVienOptions').style.display = 'none';
            });

            // Export button with conditional action selection
            document.getElementById('btnExport')?.addEventListener('click', function(e) {
                // Validate at least one column is selected (excluding hidden inputs)
                const checkboxes = document.querySelectorAll('#exportModal input[type="checkbox"]:checked:not([type="hidden"]):not([disabled])');
                if (checkboxes.length === 0) {
                    alert('Vui lòng chọn ít nhất một cột để export!');
                    return;
                }

                // Update column order before export
                updateColumnOrder();

                // Get current filter parameters
                var form = document.getElementById('filterForm');
                var formData = new FormData(form);
                var queryParams = new URLSearchParams();

                // Add filter parameters
                for (var pair of formData.entries()) {
                    queryParams.append(pair[0], pair[1]);
                }

                // Add column selection parameters from modal
                const modalCheckboxes = document.querySelectorAll('#exportModal input[type="checkbox"]:not([type="hidden"])');
                modalCheckboxes.forEach(cb => {
                    if (!cb.disabled) {
                        queryParams.append(cb.name, cb.checked ? 'true' : 'false');
                    }
                });

                // Add column order parameters (DeTai + SinhVien)
                const columnOrder = document.getElementById('columnOrder')?.value;
                if (columnOrder) queryParams.append('columnOrder', columnOrder);
                const studentColumnOrder = document.getElementById('studentColumnOrder')?.value;
                if (studentColumnOrder) queryParams.append('studentColumnOrder', studentColumnOrder);

                // Đóng modal và export
                modal.hide();
                document.querySelector('.modal-backdrop')?.remove();
                document.body.classList.remove('modal-open');
                document.body.style.removeProperty('padding-right');

                // Choose export action based on whether student info is included
                const includeSinhVien = document.getElementById('chkSinhVien').checked;
                const exportUrl = includeSinhVien ? urlExportChiTiet : urlExport;

                window.location.href = exportUrl + '?' + queryParams.toString();
            });
  });
    </script>
}
