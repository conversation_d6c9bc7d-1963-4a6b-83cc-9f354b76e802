﻿@model InternshipManagement.Models.ViewModels.GvManageVm
@{
    ViewData["Title"] = "Quản lý đề tài";
}

@if (TempData["Toast"] != null)
{
    <div class="alert alert-info alert-dismissible fade show position-fixed bottom-0 end-0 m-3 shadow"
         role="alert" style="z-index: 2000;">
        @TempData["Toast"]
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
}


<form method="get" class="card mb-3">
    <div class="card-header bg-white">B<PERSON> lọc</div>
    <div class="card-body">
        <div class="row g-2 align-items-end">
            <div class="col-12 col-sm-2">
                <label class="form-label">H<PERSON><PERSON> kỳ</label>
                <select class="form-select" asp-items="Model.HocKyOptions" name="hocKy">
                    <option value="">Chọ<PERSON> họ<PERSON></option>
                </select>
            </div>
            <div class="col-12 col-sm-2">
                <label class="form-label">Năm học</label>
                <select class="form-select" asp-items="Model.NamHocOptions" name="namHoc">
                    <option value="">Chọn năm học</option>
                </select>
            </div>
            <div class="col-12 col-sm-3">
                <label class="form-label">Đề tài</label>
                <select class="form-select" asp-items="Model.DeTaiOptions" name="maDt">
                    <option value="">Chọn đề tài</option>
                </select>
            </div>
            <div class="col-12 col-sm-2">
                <label class="form-label">Trạng thái</label>
                <select class="form-select" asp-items="@(new SelectList(Model.TrangThaiOptions.Where(x => new[] { "1", "2", "3" }.Contains(x.Value)), "Value", "Text"))" name="trangThai">
                    <option value="">Chọn trạng thái</option>
                </select>
            </div>
            <div class="col-12 col-sm-3 d-flex gap-2">
                <button class="btn btn-primary d-flex align-items-center justify-content-center py-2" type="submit"><i class="bi bi-funnel me-1"></i>Lọc</button>
                <a class="btn btn-outline-secondary d-flex align-items-center justify-content-center py-2" href="@Url.Action("Manage","DeTai")"><i class="bi bi-trash me-1"></i>Xóa lọc</a>
            </div>
        </div>
    </div>
</form>

<div class="col-12">
    <div class="card mb-3">
        <div class="card-header bg-white d-flex justify-content-between align-items-center">
            <span>Đề tài của tôi</span>
            <a class="btn btn-sm btn-primary" asp-action="CreateDeTai">
                <i class="bi bi-plus-circle me-1"></i> Thêm đề tài
            </a>
        </div>

        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-striped table-bordered table-hover table-sm align-middle mb-0 bg-white">
                    <thead class="table-primary">
                        <tr>
                            <th class="text-nowrap fw-bold">Mã</th>
                            <th class="text-nowrap fw-bold">Tên</th>
                            <th class="text-nowrap fw-bold">HK/NH</th>
                            <th class="text-nowrap fw-bold">Tối đa</th>
                            <th class="text-nowrap fw-bold">Tham gia</th>
                            <th class="text-nowrap fw-bold">Còn</th>
                            <th class="text-nowrap fw-bold text-center">Hành động</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var t in Model.Topics)
                        {
                            <tr>
                                <td class="text-nowrap text-monospace">@t.MaDt</td>
                                <td class="text-truncate" style="max-width: 360px;">@t.TenDt</td>
                                <td>@($"HK{t.HocKy} / {t.NamHoc}")</td>
                                <td class="text-center">@t.SoLuongToiDa</td>
                                <td class="text-center">@t.ThamGia</td>
                                <td class="text-center @(t.ConLai > 0 ? "text-success" : "text-danger")">@t.ConLai</td>
                                <td class="text-center text-nowrap">
                                    <!-- Sửa -->
                                    <a class="btn btn-primary btn-sm me-1 text-white"
                                       asp-action="EditDeTai" asp-route-id="@t.MaDt" title="Sửa">
                                        <i class="bi bi-pencil-square"></i>
                                    </a>
                                    <!-- Xóa (mở modal) -->
                                    <button type="button" class="btn btn-danger btn-sm"
                                            data-bs-toggle="modal" data-bs-target="#deleteTopicModal"
                                            data-madt="@t.MaDt" data-tendt="@t.TenDt" title="Xóa">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </td>
                            </tr>
                        }
                        @if (!Model.Topics.Any())
                        {
                            <tr><td colspan="7" class="text-center text-muted py-5 fs-5"><i class="bi bi-info-circle me-2"></i>Không có đề tài</td></tr>
                        }
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <div class="card">
        <div class="card-header bg-white">Sinh viên đang hướng dẫn</div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-striped table-bordered table-hover table-sm align-middle mb-0 bg-white">
                    <thead class="table-primary">
                        <tr>
                            <th class="text-nowrap fw-bold">Mã SV</th>
                            <th class="text-nowrap fw-bold">Họ tên</th>
                            <th class="text-nowrap fw-bold">Khoa</th>
                            <th class="text-nowrap fw-bold">Đề tài</th>
                            <th class="text-nowrap fw-bold">HK/NH</th>
                            <th class="text-nowrap fw-bold">Trạng thái</th>
                            <th class="text-nowrap fw-bold">ĐK</th>
                            <th class="text-nowrap fw-bold">CN</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var s in Model.Students)
                        {
                            <tr>
                                <td class="text-monospace">@s.Masv</td>
                                <td>@s.HotenSv</td>
                                <td>@s.Sv_TenKhoa</td>
                                <td class="text-monospace">@s.MaDt</td>
                                <td>@s.HocKy / @s.NamHoc</td>
                                <td>
                                    @{
                                        var (label, badgeClass) = s.TrangThai switch
                                        {
                                            0 => ("Chờ duyệt", "bg-warning text-dark"),
                                            1 => ("Chấp nhận", "bg-success text-white"),
                                            2 => ("Đang thực hiện", "bg-info text-dark"),
                                            3 => ("Hoàn thành", "bg-success text-white"),
                                            4 => ("Từ chối", "bg-danger text-white"),
                                            5 => ("Rút đăng ký", "bg-secondary text-white"),
                                            _ => ("?", "bg-secondary text-white")
                                        };
                                    }
                                    <span class="badge @badgeClass">@label</span>
                                </td>
                                <td>@(s.NgayDangKy?.ToString("dd/MM/yyyy") ?? "—")</td>
                                <td>@(s.NgayChapNhan?.ToString("dd/MM/yyyy") ?? "—")</td>
                            </tr>
                        }
                        @if (!Model.Students.Any())
                        {
                            <tr><td colspan="8" class="text-center text-muted py-5 fs-5"><i class="bi bi-info-circle me-2"></i>Không có sinh viên</td></tr>
                        }
                    </tbody>
                </table>
            </div>
        </div>
    </div>


    <!-- Modal xác nhận xóa: dùng chung -->
    <div class="modal fade" id="deleteTopicModal" tabindex="-1" aria-labelledby="deleteTopicLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <form asp-action="DeleteDeTai" method="post">
                    @Html.AntiForgeryToken()
                    <div class="modal-header">
                        <h5 class="modal-title" id="deleteTopicLabel">
                            <i class="bi bi-exclamation-triangle me-2 text-danger"></i> Xác nhận xóa đề tài
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Đóng"></button>
                    </div>

                    <div class="modal-body">
                        <p>Bạn chắc chắn muốn xóa đề tài:</p>
                        <ul class="mb-0">
                            <li><strong>Mã:</strong> <span id="delMaDt"></span></li>
                            <li><strong>Tên:</strong> <span id="delTenDt"></span></li>
                        </ul>
                        <small class="text-muted d-block mt-2">
                            Lưu ý: Nếu đề tài có sinh viên ở trạng thái 1/2/3, hệ thống sẽ không cho xóa.
                        </small>
                        <!-- Truyền id (MaDt) về controller -->
                        <input type="hidden" name="id" id="delMaDtInput" />
                    </div>

                    <div class="modal-footer">
                        <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">
                            <i class="bi bi-x-circle me-1"></i> Hủy
                        </button>
                        <button type="submit" class="btn btn-danger">
                            <i class="bi bi-trash me-1"></i> Xóa
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        // Khi mở modal, lấy data-* từ nút bấm và set vào nội dung/hidden input
        const delModal = document.getElementById('deleteTopicModal');
        if (delModal) {
            delModal.addEventListener('show.bs.modal', function (event) {
                const btn = event.relatedTarget;
                const ma = btn?.getAttribute('data-madt') || '';
                const ten = btn?.getAttribute('data-tendt') || '';

                delModal.querySelector('#delMaDt').textContent = ma;
                delModal.querySelector('#delTenDt').textContent = ten;
                delModal.querySelector('#delMaDtInput').value = ma;
            });
        }
    </script>
}