@model InternshipManagement.Models.ViewModels.GvManageVm
@{
    ViewData["Title"] = "Quản lý đề tài";
}

@* Syncfusion Toast *@
<ejs-toast id="toast_type" close="onclose" beforeOpen="onBeforeOpen" created="created" timeOut="10000">
    <e-toast-position X="Right" Y="Top"></e-toast-position>
</ejs-toast>

@* Server messages bridge for toasts *@
<div id="serverMessages"
     data-success="@TempData["Toast"]"
     data-warning=""
     data-error=""
     style="display:none;"></div>


<form method="get" class="card mb-3" id="filterForm">
    <div class="card-header bg-white">Bộ lọc</div>
    <div class="card-body">
        <div class="row g-2 align-items-end">
            <div class="col-12 col-sm-3">
                <label class="form-label"><PERSON><PERSON><PERSON>ỳ/<PERSON>ăm học</label>
                <div class="searchable-dropdown" id="termDropdown">
                    <input type="hidden" value="@(Model.Filter.HocKy.HasValue && !string.IsNullOrEmpty(Model.Filter.NamHoc) ? ($"{Model.Filter.HocKy}|{Model.Filter.NamHoc}") : "")" />
                    <input type="text" class="form-control" autocomplete="off">
                    <i class="bi bi-chevron-down dropdown-arrow"></i>
                    <div class="dropdown-menu">
                        <div class="dropdown-options">
                        </div>
                    </div>
                </div>
                <input type="hidden" name="hocKy" value="@Model.Filter.HocKy" />
                <input type="hidden" name="namHoc" value="@Model.Filter.NamHoc" />
            </div>
            <div class="col-12 col-sm-3">
                <label class="form-label">Đề tài</label>
                <div class="searchable-dropdown" id="deTaiDropdown">
                    <input type="hidden" name="maDt" value="@Model.Filter.MaDt" />
                    <input type="text" class="form-control" placeholder="Tìm kiếm đề tài..." autocomplete="off">
                    <i class="bi bi-chevron-down dropdown-arrow"></i>
                    <div class="dropdown-menu">
                        <div class="dropdown-options">
                            <a class="dropdown-item" href="#" data-value="">-- Tất cả --</a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-12 col-sm-2">
                <label class="form-label">Trạng thái</label>
                <div class="searchable-dropdown" id="trangThaiDropdown">
                    <input type="hidden" name="trangThai" value="@Model.Filter.TrangThai" />
                    <input type="text" class="form-control" autocomplete="off">
                    <i class="bi bi-chevron-down dropdown-arrow"></i>
                    <div class="dropdown-menu">
                        <div class="dropdown-options">
                            <a class="dropdown-item" href="#" data-value="1">Chấp nhận</a>
                            <a class="dropdown-item" href="#" data-value="2">Đang thực hiện</a>
                            <a class="dropdown-item" href="#" data-value="3">Hoàn thành</a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-12 col-sm-4 d-flex gap-2">
                <button class="btn btn-primary d-flex align-items-center justify-content-center py-2" type="submit"><i class="bi bi-funnel me-1"></i>Lọc</button>
                <a class="btn btn-outline-secondary d-flex align-items-center justify-content-center py-2" href="@Url.Action("Manage","DeTai")"><i class="bi bi-trash me-1"></i>Xóa lọc</a>
            </div>
        </div>
    </div>
</form>

<div class="col-12">
    <div class="card mb-3">
        <div class="card-header bg-white d-flex justify-content-between align-items-center">
            <span>Đề tài của tôi</span>
            <a class="btn btn-sm btn-primary" asp-action="CreateDeTai">
                <i class="bi bi-plus-circle me-1"></i> Thêm đề tài
            </a>
        </div>

        <div class="card-body p-0">
            <!-- Responsive Grid Container -->
            <div class="grid-responsive">
                <div class="grid-container">
                    <ejs-grid id="TopicsGrid" dataSource="@Model.Topics" allowPaging="true" allowSorting="true"
                              allowMultiSorting="true" allowFiltering="false" allowResizing="true" gridLines="Both"
                              height="400" width="100%" grid-lines="Both" enable-hover="false" enable-alt-row="true">
                        <e-grid-pagesettings pageSize="10" pageSizes="new List<int>{5,10,20,50}"></e-grid-pagesettings>
                        <e-grid-columns>
                            <e-grid-column field="MaDt" headerText="Mã" width="120" template="#maDtTemplate"></e-grid-column>
                            <e-grid-column field="TenDt" headerText="Tên đề tài" width="300"></e-grid-column>
                            <e-grid-column headerText="HK/NH" width="120" template="#hocKyTemplate"></e-grid-column>
                            <e-grid-column field="SoLuongToiDa" headerText="Tối đa" width="80" textAlign="Center"></e-grid-column>
                            <e-grid-column field="ThamGia" headerText="Tham gia" width="80" textAlign="Center"></e-grid-column>
                            <e-grid-column field="ConLai" headerText="Còn" width="80" textAlign="Center" template="#conLaiTemplate"></e-grid-column>
                            <e-grid-column headerText="Hành động" width="150" textAlign="Center" template="#actionTemplate"></e-grid-column>
                        </e-grid-columns>
                    </ejs-grid>
                </div>
            </div>
        </div>
    </div>

    <div class="card">
        <div class="card-header bg-white">Sinh viên đang hướng dẫn</div>
        <div class="card-body p-0">
            <!-- Responsive Grid Container -->
            <div class="grid-responsive">
                <div class="grid-container">
                    <ejs-grid id="StudentsGrid" dataSource="@Model.Students" allowPaging="true" allowSorting="true"
                              allowMultiSorting="true" allowFiltering="false" allowResizing="true" gridLines="Both"
                              height="400" width="100%" grid-lines="Both" enable-hover="false" enable-alt-row="true"
                              queryCellInfo="onStudentCellRender">
                        <e-grid-pagesettings pageSize="10" pageSizes="new List<int>{5,10,20,50}"></e-grid-pagesettings>
                        <e-grid-columns>
                            <e-grid-column field="Masv" headerText="Mã SV" width="100" template="#maSvTemplate"></e-grid-column>
                            <e-grid-column field="HotenSv" headerText="Họ tên" width="200"></e-grid-column>
                            <e-grid-column field="Sv_TenKhoa" headerText="Khoa" width="150"></e-grid-column>
                            <e-grid-column field="MaDt" headerText="Đề tài" width="120" template="#maDtStudentTemplate"></e-grid-column>
                            <e-grid-column headerText="HK/NH" width="120" template="#hocKyStudentTemplate"></e-grid-column>
                            <e-grid-column field="TrangThai" headerText="Trạng thái" width="120" textAlign="Center" template="#trangThaiTemplate"></e-grid-column>
                            <e-grid-column field="NgayDangKy" headerText="ĐK" width="100" textAlign="Center" template="#ngayDkTemplate"></e-grid-column>
                            <e-grid-column field="NgayChapNhan" headerText="CN" width="100" textAlign="Center" template="#ngayChapNhanTemplate"></e-grid-column>
                        </e-grid-columns>
                    </ejs-grid>
                </div>
            </div>
        </div>
    </div>


    <!-- Modal xác nhận xóa: dùng chung -->
    <div class="modal fade" id="deleteTopicModal" tabindex="-1" aria-labelledby="deleteTopicLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <form asp-action="DeleteDeTai" method="post">
                    @Html.AntiForgeryToken()
                    <div class="modal-header">
                        <h5 class="modal-title" id="deleteTopicLabel">
                            <i class="bi bi-exclamation-triangle me-2 text-danger"></i> Xác nhận xóa đề tài
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Đóng"></button>
                    </div>

                    <div class="modal-body">
                        <p>Bạn chắc chắn muốn xóa đề tài:</p>
                        <ul class="mb-0">
                            <li><strong>Mã:</strong> <span id="delMaDt"></span></li>
                            <li><strong>Tên:</strong> <span id="delTenDt"></span></li>
                        </ul>
                        <small class="text-muted d-block mt-2">
                            Lưu ý: Nếu đề tài có sinh viên ở trạng thái 1/2/3, hệ thống sẽ không cho xóa.
                        </small>
                        <!-- Truyền id (MaDt) về controller -->
                        <input type="hidden" name="id" id="delMaDtInput" />
                    </div>

                    <div class="modal-footer">
                        <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">
                            <i class="bi bi-x-circle me-1"></i> Hủy
                        </button>
                        <button type="submit" class="btn btn-danger">
                            <i class="bi bi-trash me-1"></i> Xóa
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

    @* Templates for Topics Grid *@
    <script id="maDtTemplate" type="text/x-template">
        <span class="ma-dt-code">${MaDt}</span>
    </script>

    <script id="hocKyTemplate" type="text/x-template">
        HK${HocKy} / ${NamHoc}
    </script>

    <script id="conLaiTemplate" type="text/x-template">
        <span class="${ConLai > 0 ? 'text-success' : 'text-danger'}">${ConLai}</span>
    </script>

    <script id="actionTemplate" type="text/x-template">
        <div class="d-flex gap-1 justify-content-center">
            <a class="btn btn-primary btn-sm" href="@Url.Action("EditDeTai", "DeTai")/${MaDt}" title="Sửa">
                <i class="bi bi-pencil-square"></i>
            </a>
            <button type="button" class="btn btn-danger btn-sm" onclick="showDeleteModal('${MaDt}', '${TenDt}')" title="Xóa">
                <i class="bi bi-trash"></i>
            </button>
        </div>
    </script>

    @* Templates for Students Grid *@
    <script id="maSvTemplate" type="text/x-template">
        <span class="ma-sv-code">${Masv}</span>
    </script>

    <script id="maDtStudentTemplate" type="text/x-template">
        <span class="ma-dt-code">${MaDt}</span>
    </script>

    <script id="hocKyStudentTemplate" type="text/x-template">
        ${HocKy} / ${NamHoc}
    </script>

    <script id="trangThaiTemplate" type="text/x-template">
        # var badgeInfo = getBadgeInfo(data.TrangThai); #
        <span class="badge ${badgeInfo.class}">${badgeInfo.label}</span>
    </script>

    <script id="ngayDkTemplate" type="text/x-template">
        # var dateStr = formatDate(data.NgayDangKy); #
        ${dateStr}
    </script>

    <script id="ngayChapNhanTemplate" type="text/x-template">
        # var dateStr = formatDate(data.NgayChapNhan); #
        ${dateStr}
    </script>

@section Scripts {
    <link href="~/css/searchable-dropdown.css" rel="stylesheet" />
    <script src="~/js/searchable-dropdown.js"></script>
    <script>
        // Helper functions for templates
        function getBadgeInfo(trangThai) {
            switch (trangThai) {
                case 0: return { label: "Chờ duyệt", class: "bg-warning text-dark" };
                case 1: return { label: "Chấp nhận", class: "bg-success text-white" };
                case 2: return { label: "Đang thực hiện", class: "bg-info text-dark" };
                case 3: return { label: "Hoàn thành", class: "bg-success text-white" };
                case 4: return { label: "Từ chối", class: "bg-danger text-white" };
                case 5: return { label: "Rút đăng ký", class: "bg-secondary text-white" };
                default: return { label: "?", class: "bg-secondary text-white" };
            }
        }

        function formatDate(dateStr) {
            if (!dateStr) return "—";
            var date = new Date(dateStr);
            if (isNaN(date.getTime())) return "—";
            return date.toLocaleDateString('vi-VN');
        }

        function showDeleteModal(maDt, tenDt) {
            const modal = document.getElementById('deleteTopicModal');
            modal.querySelector('#delMaDt').textContent = maDt;
            modal.querySelector('#delTenDt').textContent = tenDt;
            modal.querySelector('#delMaDtInput').value = maDt;
            new bootstrap.Modal(modal).show();
        }

        // Syncfusion Toast functions
        function onclose(e) {
            // Toast closed
        }

        function onBeforeOpen(e) {
            // Before toast opens
        }

        function created() {
            // Show server messages as toasts
            const serverMessages = document.getElementById('serverMessages');
            if (serverMessages) {
                const success = serverMessages.dataset.success;
                const warning = serverMessages.dataset.warning;
                const error = serverMessages.dataset.error;

                const toastObj = document.getElementById('toast_type').ej2_instances[0];

                if (success) {
                    toastObj.show({
                        title: 'Thành công',
                        content: success,
                        cssClass: 'e-toast-success',
                        icon: 'e-success toast-icons'
                    });
                }
                if (warning) {
                    toastObj.show({
                        title: 'Cảnh báo',
                        content: warning,
                        cssClass: 'e-toast-warning',
                        icon: 'e-warning toast-icons'
                    });
                }
                if (error) {
                    toastObj.show({
                        title: 'Lỗi',
                        content: error,
                        cssClass: 'e-toast-danger',
                        icon: 'e-error toast-icons'
                    });
                }
            }
        }

        // Cell rendering for students grid
        function onStudentCellRender(args) {
            // Custom cell rendering if needed
        }

        document.addEventListener('DOMContentLoaded', function () {
            // Handle filter form submission
            const filterForm = document.getElementById('filterForm');
            filterForm.addEventListener('submit', function(e) {
                e.preventDefault();

                // Build query string with filter parameters
                var formData = new FormData(this);
                var queryParams = new URLSearchParams();

                for (var pair of formData.entries()) {
                    queryParams.append(pair[0], pair[1]);
                }

                window.location.href = '@Url.Action("Manage", "DeTai")?' + queryParams.toString();
            });

            // Term dropdown (combine HK/Năm học)
            const termEl = document.getElementById('termDropdown');
            const termDd = new SearchableDropdown('termDropdown', { placeholder: '', searchUrl: null, allowClear: true });

            function generateTerms(baseYear) {
                const out = [];
                for (let y = baseYear - 5; y <= baseYear; y++) {
                    const nh = `${y}-${y + 1}`;
                    for (let hk = 1; hk <= 3; hk++) out.push({ value: `${hk}|${nh}`, text: `HK${hk} (${nh})`, hk, nh });
                }
                return out;
            }

            function filterTerms(all, query) {
                if (!query) return all;
                const q = String(query).toLowerCase().replace(/\s+/g, '');
                const m = q.match(/^(\d{4})$/);
                if (m) {
                    const Y = parseInt(m[1], 10);
                    const expanded = [];
                    for (let hk = 1; hk <= 3; hk++) expanded.push({ value: `${hk}|${Y}-${Y + 1}`, text: `HK${hk} (${Y}-${Y + 1})`, hk, nh: `${Y}-${Y + 1}` });
                    for (let hk = 1; hk <= 3; hk++) expanded.push({ value: `${hk}|${Y - 1}-${Y}`, text: `HK${hk} (${Y - 1}-${Y})`, hk, nh: `${Y - 1}-${Y}` });
                    return expanded;
                }
                return all.filter(x => x.text.toLowerCase().includes(q));
            }

            const nowY = new Date().getFullYear();
            let allTerms = generateTerms(nowY).sort((a, b) => (a.nh < b.nh ? 1 : a.nh > b.nh ? -1 : b.hk - a.hk));
            termDd.searchData = async function (query) {
                const options = filterTerms(allTerms, query);
                await termDd.updateOptions(options.map(o => ({ value: o.value, text: o.text })));
            };
            termDd.searchData('');

            termEl.addEventListener('selectionChanged', function (e) {
                const val = e?.detail?.value ?? '';
                const [hk, nh] = val.includes('|') ? val.split('|') : ['', ''];
                const hkInput = document.querySelector('input[name="hocKy"]');
                const nhInput = document.querySelector('input[name="namHoc"]');
                if (hkInput) hkInput.value = hk;
                if (nhInput) nhInput.value = nh;

                // Refresh đề tài options when term changes
                deTaiDropdown.clear();
                deTaiDropdown.loadInitialData();
            });

            // Đề tài dropdown (dynamic options based on học kỳ and năm học)
            const deTaiDropdown = new SearchableDropdown('deTaiDropdown', {
                placeholder: 'Tìm kiếm đề tài...',
                searchUrl: '@Url.Action("GetTopicsByTerm", "DeTai")',
                searchParam: 'q',
                debounceDelay: 300,
                allowClear: true,
                labelFormatter: function(item) {
                    if (item && item.MaDt && item.TenDt) {
                        return { value: item.MaDt, text: `${item.MaDt} - ${item.TenDt}` };
                    }
                    return null;
                },
                getFilterValue: function() {
                    const hocKy = document.querySelector('input[name="hocKy"]').value;
                    const namHoc = document.querySelector('input[name="namHoc"]').value;
                    return { hocKy: hocKy, namHoc: namHoc };
                }
            });

            // Trạng thái dropdown (static options)
            const trangThaiEl = document.getElementById('trangThaiDropdown');
            const trangThaiDd = new SearchableDropdown('trangThaiDropdown', { placeholder: '', searchUrl: null, allowClear: true });

            function getTrangThaiOptionsFromDom() {
                const opts = [];
                trangThaiEl.querySelectorAll('.dropdown-options a.dropdown-item').forEach(a => {
                    const v = a.getAttribute('data-value') ?? '';
                    const t = a.textContent ?? '';
                    opts.push({ value: v, text: t });
                });
                return opts;
            }

            trangThaiDd.searchData = async function(query){
                const all = getTrangThaiOptionsFromDom();
                const q = (query || '').toLowerCase();
                const filtered = all.filter(o => !q || o.text.toLowerCase().includes(q) || o.value === q);
                await trangThaiDd.updateOptions(filtered);
            };
            trangThaiDd.searchData('');

            // Helper functions for combobox behavior
            function setAllIfEmpty(container, hiddenSelector) {
                const input = container?.querySelector('input.form-control');
                const hidden = hiddenSelector ? document.querySelector(hiddenSelector) : container?.querySelector('input[type="hidden"]');
                if (input && (!input.value || input.value.trim() === '') && (!hidden || !hidden.value)) {
                    input.value = '-- Tất cả --';
                }
            }

            function installComboBehavior(container, hiddenSelector) {
                const input = container?.querySelector('input.form-control');
                const hidden = hiddenSelector ? document.querySelector(hiddenSelector) : container?.querySelector('input[type="hidden"]');
                if (!input) return;
                input.addEventListener('focus', function () {
                    if (this.value.trim() === '-- Tất cả --') this.value = '';
                });
                input.addEventListener('blur', function () {
                    const hv = hidden ? hidden.value : '';
                    if (this.value.trim() === '' && !hv) {
                        this.value = '-- Tất cả --';
                    }
                });
            }

            // Initialize display values
            const termInput = termEl?.querySelector('input.form-control');
            const hkHidden = document.querySelector('input[name="hocKy"]');
            const nhHidden = document.querySelector('input[name="namHoc"]');
            if (termInput) {
                if (hkHidden?.value && nhHidden?.value) {
                    termInput.value = `HK${hkHidden.value} (${nhHidden.value})`;
                } else {
                    termInput.value = '-- Tất cả --';
                }
            }

            // Apply combobox behavior
            setAllIfEmpty(termEl, null);
            setAllIfEmpty(trangThaiEl, null);
            installComboBehavior(termEl, null);
            installComboBehavior(trangThaiEl, null);

            // Prevent empty state for term dropdown
            const termTextInput = termEl?.querySelector('input.form-control');
            termTextInput?.addEventListener('input', function(){
                if (this.value.trim() === '') {
                    const hkInput = document.querySelector('input[name="hocKy"]');
                    const nhInput = document.querySelector('input[name="namHoc"]');
                    if (hkInput) hkInput.value = '';
                    if (nhInput) nhInput.value = '';
                    this.value = '-- Tất cả --';
                }
            });

            // Prevent empty state for trạng thái dropdown
            const trangThaiTextInput = trangThaiEl?.querySelector('input.form-control');
            trangThaiTextInput?.addEventListener('input', function(){
                if (this.value.trim() === '') {
                    const hidden = document.querySelector('input[name="trangThai"]');
                    if (hidden) hidden.value = '';
                    this.value = '-- Tất cả --';
                }
            });
        });
    </script>
}