﻿@model InternshipManagement.Models.ViewModels.DeTaiDetailVm
@{
    ViewData["Title"] = "Chi tiết Đề tài";
}

<div class="card shadow-sm mb-3">
    <div class="card-header bg-white d-flex justify-content-between align-items-center">
        <h5 class="mb-0">Chi tiết Đề tài: <strong>@Model.TenDt</strong> <small class="text-muted">(@Model.MaDt)</small></h5>
        <a asp-controller="DeTai" asp-action="Index" class="btn btn-sm btn-outline-secondary">← Quay lại</a>
    </div>
    <div class="card-body">
        <div class="row g-3">
            <div class="col-md-6">
                <div><strong>Giảng viên:</strong> @Model.Gv_HoTenGv (@Model.Gv_MaGv)</div>
                <div><strong>Khoa:</strong> @Model.Khoa_TenKhoa (@Model.Khoa_MaKhoa) — ĐT: @Model.Khoa_DienThoai</div>
                <div><strong><PERSON><PERSON><PERSON> thực tập:</strong> @Model.NoiThucTap</div>
                <div><strong>Kinh phí:</strong> @(Model.KinhPhi.HasValue ? String.Format("{0:#,##0} triệu", Model.KinhPhi) : "—")</div>
            </div>
            <div class="col-md-6">
                <div><strong>Học kỳ:</strong> @Model.HocKy/@Model.NamHoc</div>
                <div><strong>Số lượng tối đa:</strong> @Model.SoLuongToiDa</div>
                <div>
                    <span class="badge rounded-pill bg-primary me-2">Đã tham gia: @Model.SoThamGia</span>
                    <span class="badge rounded-pill @(Model.SoChoConLai > 0 ? "bg-success" : "bg-danger")">Còn lại: @Model.SoChoConLai</span>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="card shadow-sm">
    <div class="card-header bg-white d-flex align-items-center justify-content-between">
        <h6 class="mb-0">Sinh viên đã tham gia</h6>
        <div class="small text-muted">Tổng: <strong>@Model.SoThamGia</strong></div>
    </div>
    <div class="card-body p-3">
        <ejs-grid id="studentsGrid" dataSource="@Model.Students"
                  allowPaging="true" height="400" gridLines="Both"
                  pageSettings="@(new Syncfusion.EJ2.Grids.GridPageSettings { PageSize = 10 })"
                  queryCellInfo="onCellRender">
            <e-grid-columns>
                <e-grid-column field="MaSv" headerText="Mã SV" width="90" textAlign="@Syncfusion.EJ2.Grids.TextAlign.Center"></e-grid-column>
                <e-grid-column field="HoTenSv" headerText="Họ tên" width="150"></e-grid-column>
                <e-grid-column field="NamSinh" headerText="Năm sinh" width="110" textAlign="@Syncfusion.EJ2.Grids.TextAlign.Center"></e-grid-column>
                <e-grid-column field="QueQuan" headerText="Quê quán" width="120"></e-grid-column>
                <e-grid-column field="TrangThai" headerText="Trạng thái" width="140" textAlign="@Syncfusion.EJ2.Grids.TextAlign.Center"></e-grid-column>
                <e-grid-column field="NgayDangKy" headerText="Ngày đăng ký" width="130" textAlign="@Syncfusion.EJ2.Grids.TextAlign.Center"></e-grid-column>
                <e-grid-column field="NgayChapNhan" headerText="Ngày chấp nhận" width="140" textAlign="@Syncfusion.EJ2.Grids.TextAlign.Center"></e-grid-column>
                <e-grid-column field="KetQua" headerText="Kết quả" width="90" textAlign="@Syncfusion.EJ2.Grids.TextAlign.Center"></e-grid-column>
                <e-grid-column field="GhiChu" headerText="Ghi chú" width="150"></e-grid-column>
            </e-grid-columns>
        </ejs-grid>

        <script>
            // Format dd/MM/yyyy
            function toViDate(raw) {
                if (!raw) return '—';
                var d = new Date(raw);
                if (isNaN(d.getTime())) return '—';
                var dd = String(d.getDate()).padStart(2, '0');
                var mm = String(d.getMonth() + 1).padStart(2, '0');
                var yyyy = d.getFullYear();
                return dd + '/' + mm + '/' + yyyy;
            }

            // Cell render hook to inject HTML and formatted text safely
            window.onCellRender = function(args) {
                if (!args || !args.data || !args.column) return;
                var f = args.column.field;
                if (f === 'TrangThai') {
                    var map = {
                        0: { text: 'Chờ duyệt', cls: '' },
                        1: { text: 'Chấp nhận', cls: 'text-success' },
                        2: { text: 'Đang thực hiện', cls: 'text-warning' },
                        3: { text: 'Hoàn thành', cls: 'text-primary' },
                        4: { text: 'Từ chối', cls: 'text-danger' },
                        5: { text: 'Rút', cls: 'text-muted' }
                    };
                    var s = map[args.data.TrangThai] || { text: 'Khác', cls: '' };
                    args.cell.innerHTML = '<span class="' + s.cls + '">' + s.text + '</span>';
                } else if (f === 'NgayDangKy' || f === 'NgayChapNhan') {
                    args.cell.innerText = toViDate(args.data[f]);
                } else if (f === 'KetQua') {
                    var v = args.data.KetQua;
                    if (v === null || v === undefined || v === '') {
                        args.cell.innerText = '—';
                        return;
                    }
                    var num = Number(v);
                    if (isNaN(num)) { args.cell.innerText = '—'; return; }
                    var txt = num.toFixed(2);
                    if (num < 5) {
                        args.cell.innerHTML = '<span class="text-danger">' + txt + '</span>';
                    } else {
                        args.cell.innerText = txt;
                    }
                } else if (f === 'GhiChu') {
                    args.cell.innerText = args.data.GhiChu || '—';
                }
            }
        </script>

    </div>
</div>


@{
    var reg = (DeTaiRegistrationStatusVm?)ViewBag.Reg;
    bool isAuth = (bool)(ViewBag.IsAuthenticated ?? false);
    bool isStudent = (bool)(ViewBag.IsStudent ?? false);
    var returnUrl = Context.Request.Path + Context.Request.QueryString;
    var maDt = Model.MaDt;

    DateTime today = DateTime.Today;
    DateTime regStart, regEnd;

    // Parse năm học từ string "YYYY-YYYY" để lấy năm đầu
    int baseYear = DateTime.Now.Year; // fallback
    if (!string.IsNullOrEmpty(Model.NamHoc) && Model.NamHoc.Contains("-"))
    {
        var parts = Model.NamHoc.Split('-');
        if (parts.Length == 2 && int.TryParse(parts[0], out var year))
        {
            baseYear = year;
        }
    }

    // Tính theo quy ước năm học
    switch (Model.HocKy)
    {
        case 1:
            // HK1: 5/9 -> 31/12, nằm trong năm đầu của năm học
            regStart = new DateTime(baseYear, 9, 5);
            break;
        case 2:
            // HK2: 1/1 -> 25/5, nhưng của NĂM SAU
            regStart = new DateTime(baseYear + 1, 1, 1);
            break;
        case 3:
            // HK3: 26/5 -> 4/9, nhưng của NĂM SAU
            regStart = new DateTime(baseYear + 1, 5, 26);
            break;
        default:
            regStart = regEnd = today.AddDays(-1); // đóng luôn nếu dữ liệu kỳ lạ
            break;
    }
    regEnd = regStart.AddDays(14);

    bool registrationClosed = today < regStart || today > regEnd;
}


<div class="d-flex align-items-center gap-2 flex-wrap mt-2">
    @if (!isAuth)
    {
        <div class="d-inline-flex align-items-center mt-1 text-muted gap-2 lh-1">
            <i class="bi bi-info-circle align-middle p-1"></i>
            <span class="align-middle p-1">Đăng nhập để đăng ký</span>
        </div>
    }
    else if (registrationClosed && (reg is null || reg.ThisTrangThai is null || reg.ThisTrangThai == 4 || reg.ThisTrangThai == 5))
    {
        <div class="d-inline-flex align-items-center mt-1 text-muted gap-2 lh-1">
            <i class="bi bi-info-circle align-middle p-1"></i>
            <span class="align-middle p-1">Đã qua lượt đăng ký</span>
        </div>
    }
    else if (!isStudent)
    {
        @* GV/Admin: không hiển thị gì thêm *@
    }
    else if (Model.SoChoConLai <= 0 && (reg is null || reg.ThisTrangThai is null))
    {
        <div class="d-inline-flex align-items-center mt-1 text-muted gap-2 lh-1">
            <i class="bi bi-info-circle align-middle p-1"></i>
            <span class="align-middle p-1">Đề tài này đã đủ số lượng</span>
        </div>
    }
    else
    {
        // Quy ước: ThisTrangThai == -1 => đã có đề tài khác trong kỳ
        if (reg is null || reg.ThisTrangThai is null)
        {
            // Chưa từng đăng ký đề tài này => cho phép đăng ký
            <form asp-controller="DeTai" asp-action="DangKy" method="post" class="d-inline">
                @Html.AntiForgeryToken()
                <input type="hidden" name="maDt" value="@maDt" />
                <input type="hidden" name="returnUrl" value="@(Context.Request.Path + Context.Request.QueryString)" />
                <button type="submit" class="btn btn-sm btn-primary mt-1">Đăng ký đề tài</button>
            </form>
        }
        else if (reg.ThisTrangThai == -1)
        {
            <div class="d-inline-flex align-items-center mt-1 text-muted gap-2 lh-1">
                <i class="bi bi-info-circle align-middle p-1"></i>
                <span class="align-middle p-1">Bạn đã đăng ký đề tài khác trong học kỳ này</span>
            </div>
        }
        else if (reg.ThisTrangThai == 1 || reg.ThisTrangThai == 2 || reg.ThisTrangThai == 3)
        {
            <div class="d-inline-flex align-items-center mt-1 text-muted gap-2 lh-1">
                <i class="bi bi-info-circle align-middle p-1"></i>
                <span class="align-middle p-1">Đã đăng ký thành công</span>
            </div>
        }
        else if (reg.ThisTrangThai == 4) // Rejected
        {
            <button class="btn btn-sm btn-danger mt-1" disabled>Bạn đã bị từ chối</button>
            <form asp-controller="DeTai" asp-action="DangKy" method="post" class="d-inline">
                @Html.AntiForgeryToken()
                <input type="hidden" name="maDt" value="@maDt" />
                <input type="hidden" name="returnUrl" value="@(Context.Request.Path + Context.Request.QueryString)" />
                <button type="submit" class="btn btn-sm btn-outline-primary mt-1">Đăng ký lại</button>
            </form>
        }
        else if (reg.ThisTrangThai == 5) // Withdrawn / Hủy
        {
            <div class="d-inline-flex align-items-center mt-1 text-muted gap-2 lh-1">
                <i class="bi bi-info-circle align-middle p-1"></i>
                <span class="align-middle p-1">Bạn đã hủy đăng ký trước đó</span>
            </div>
        }
    }
</div>
