@model GvRegistrationsPageVm
@{
    ViewData["Title"] = "Sinh viên đăng ký đề tài";
    byte? selectedHk = Model.Filter.HocKy;
    string? selectedYear = Model.Filter.NamHoc;

    // Function kiểm tra quá hạn
    bool IsOverdue(byte hk, string namHoc)
    {
        var now = DateTime.Now;
        var parts = namHoc?.Split('-');
        if (parts == null || parts.Length != 2) return false;

        if (!int.TryParse(parts[0], out var startYear)) return false;
        if (!int.TryParse(parts[1], out var endYear)) return false;

        DateTime hkStart, hkEnd;

        switch (hk)
        {
            case 1: // HK1: 9/startYear đến 12/startYear
                hkStart = new DateTime(startYear, 9, 1);
                hkEnd = new DateTime(startYear, 12, 31);
                break;
            case 2: // HK2: 1/endYear đến 4/endYear
                hkStart = new DateTime(endYear, 1, 1);
                hkEnd = new DateTime(endYear, 4, 30);
                break;
            case 3: // HK3: 5/endYear đến 8/endYear
                hkStart = new DateTime(endYear, 5, 1);
                hkEnd = new DateTime(endYear, 8, 31);
                break;
            default:
                return false;
        }

        // Quá hạn nếu đã qua ngày kết thúc học kỳ
        return now > hkEnd;
    }
}

@* Syncfusion Toast *@
<ejs-toast id="toast_type" close="onclose" beforeOpen="onBeforeOpen" created="created" timeOut="10000">
    <e-toast-position X="Right" Y="Top"></e-toast-position>
</ejs-toast>

@* Server messages bridge for toasts *@
<div id="serverMessages"
     data-success="@TempData["Toast"]"
     data-warning=""
     data-error=""
     style="display:none;"></div>

<style>
    .action-cell {
        min-height: 80px;
        vertical-align: middle;
        width: 140px;
    }

    .action-cell .badge {
        font-size: 0.75rem;
        padding: 0.375rem 0.5rem;
    }
</style>
<div class="card shadow-sm">
    <div class="card-header bg-white d-flex justify-content-between align-items-center">
        <h5 class="mb-0">Sinh viên đăng ký đề tài</h5>
        <div class="small text-muted">Tổng: <strong>@Model.Items.Count</strong></div>
    </div>

    <div class="card-body">

        <form method="get" class="row g-3 align-items-end mb-3" id="filterForm">
            <div class="col-md-3">
                <label class="form-label">Học kỳ - Năm học</label>
                <div id="hkNamDropdown" class="searchable-dropdown">
                    <input type="text" class="form-control" readonly />
                    <i class="bi bi-chevron-down dropdown-arrow"></i>
                    <div class="dropdown-menu">
                        <div class="dropdown-options">
                            <a class="dropdown-item" href="#" data-value="">-- Tất cả --</a>
                            @{
                                var nowY = DateTime.Now.Year;
                                var pairs = Enumerable.Range(nowY - 5, 8)
                                            .SelectMany(y => new[] { (hk: (byte)1, yearStr: $"{y}-{y+1}"), (hk: (byte)2, yearStr: $"{y}-{y+1}"), (hk: (byte)3, yearStr: $"{y}-{y+1}") });
                                var selectedValue = selectedHk.HasValue && !string.IsNullOrEmpty(selectedYear)
                                                    ? $"{selectedHk}-{selectedYear}"
                                                    : "";
                                foreach (var p in pairs)
                                {
                                    var val = $"{p.hk}-{p.yearStr}";
                                    var text = $"HK{p.hk} - {p.yearStr}";
                                    <a class="dropdown-item" href="#" data-value="@val">@text</a>
                                }
                            }
                        </div>
                    </div>
                </div>
                <input type="hidden" name="hocKy" id="hocKyHidden" value="@(selectedHk?.ToString() ?? "")" />
                <input type="hidden" name="namHoc" id="namHocHidden" value="@(selectedYear ?? "")" />
            </div>
            <div class="col-md-3">
                <label class="form-label">Đề tài</label>
                <div id="deTaiDropdown" class="searchable-dropdown">
                    <input type="hidden" name="maDt" value="@Model.Filter.MaDt" />
                    <input type="text" class="form-control" placeholder="Tìm kiếm đề tài..." />
                    <i class="bi bi-chevron-down dropdown-arrow"></i>
                    <div class="dropdown-menu">
                        <div class="dropdown-options">
                            <!-- Options will be loaded dynamically -->
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <label class="form-label">Trạng thái</label>
                <div id="trangThaiDropdown" class="searchable-dropdown">
                    <input type="hidden" name="trangThai" value="@Model.Filter.TrangThai" />
                    <input type="text" class="form-control" readonly />
                    <i class="bi bi-chevron-down dropdown-arrow"></i>
                    <div class="dropdown-menu">
                        <div class="dropdown-options">
                            <a class="dropdown-item" href="#" data-value="">-- Tất cả --</a>
                            <a class="dropdown-item" href="#" data-value="0">Đang chờ duyệt</a>
                            <a class="dropdown-item" href="#" data-value="1">Chấp nhận</a>
                            <a class="dropdown-item" href="#" data-value="2">Đang thực hiện</a>
                            <a class="dropdown-item" href="#" data-value="3">Hoàn thành</a>
                            <a class="dropdown-item" href="#" data-value="4">Đã từ chối</a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="d-flex gap-2">
                    <button class="btn btn-primary py-2" type="submit" id="btnFilter">
                        <i class="bi bi-funnel me-1"></i>Lọc
                    </button>
                    <a class="btn btn-outline-secondary py-2" asp-action="Registrations">
                        <i class="bi bi-trash me-1"></i>Xóa lọc
                    </a>
                    <a class="btn btn-success py-2"
                       asp-action="ExportRegistrationsExcel"
                       asp-route-hocKy="@(Model.Filter.HocKy)"
                       asp-route-namHoc="@(Model.Filter.NamHoc)"
                       asp-route-trangThai="@(Model.Filter.TrangThai)"
                       asp-route-maDt="@(Model.Filter.MaDt)">
                        <i class="bi bi-file-earmark-excel me-1"></i>Excel
                    </a>
                </div>
            </div>
        </form>

        <div class="table-responsive">
            <table class="table table-striped table-bordered table-hover table-sm align-middle mb-0 bg-white">
                <thead class="table-primary text-center">
                    <tr>
                        <th class="text-nowrap fw-bold text-center" style="width: 60px;">STT</th>
                        <th class="text-nowrap fw-bold">Ngày đăng ký</th>
                        <th class="text-nowrap fw-bold">Sinh viên</th>
                        <th class="text-nowrap fw-bold">Đề tài</th>
                        <th class="text-nowrap fw-bold">Học kỳ</th>
                        <th class="text-nowrap fw-bold">Trạng thái</th>
                        <th class="text-nowrap fw-bold">Kết quả</th>
                        <th class="text-nowrap fw-bold">Thao tác</th>
                    </tr>
                </thead>
                <tbody>
                    @if (!Model.Items.Any())
                    {
                        <tr>
                            <td colspan="8" class="text-center text-muted py-5 fs-5">
                                <i class="bi bi-info-circle me-2"></i>Không có đăng ký nào
                            </td>
                        </tr>
                    }
                    else
                    {
                        var stt = 1;
                        foreach (var x in Model.Items)
                        {
                            var modalId = $"completeModal_{x.Masv}_{x.MaDt}".Replace(" ", "").Replace(".", "").Replace("/", "").Replace("\\", "");
                            var isOverdue = IsOverdue(x.HocKy, x.NamHoc);
                            <tr>
                                <td class="text-center">@stt</td>
                                <td>@(x.NgayDangKy?.ToString("dd/MM/yyyy") ?? "")</td>
                                <td>
                                    <div class="fw-semibold">@x.HotenSv (#@x.Masv)</div>
                                    <div class="text-muted small">@x.Sv_TenKhoa (@x.Sv_MaKhoa)</div>
                                    @if (!string.IsNullOrWhiteSpace(x.QueQuan))
                                    {
                                        <div class="text-muted small">QQ: @x.QueQuan</div>
                                    }
                                </td>
                                <td>
                                    <div class="fw-semibold">@x.TenDt</div>
                                    <div class="text-muted small">Mã: <span class="text-monospace">@x.MaDt</span></div>
                                </td>
                                <td>@($"{x.HocKy} - {x.NamHoc}")</td>
                                <td>
                                    @{
                                        string statusText = x.TrangThai switch
                                        {
                                            0 => "Đang chờ duyệt",
                                            1 => "Chấp nhận",
                                            2 => "Đang thực hiện",
                                            3 => "Hoàn thành",
                                            4 => "Đã từ chối",
                                            _ => "Khác"
                                        };
                                        string badgeClass = x.TrangThai switch
                                        {
                                            0 => "bg-warning text-dark",
                                            1 => "bg-success text-white",
                                            2 => "bg-info text-dark",
                                            3 => "bg-success text-white",
                                            4 => "bg-danger text-white",
                                            _ => "bg-secondary text-white"
                                        };
                                    }
                                    <span class="badge @badgeClass">@statusText</span>
                                </td>

                                <td class="text-center">
                                    @if (x.KetQua.HasValue)
                                    {
                                        <span class="fw-semibold">@x.KetQua.Value</span>
                                    }
                                    else
                                    {
                                        <span class="text-muted">-</span>
                                    }
                                </td>

                                <td class="text-nowrap action-cell">
                                    @* 0 = Pending: giữ nguyên Duyệt/Từ chối *@
                                    @if (x.TrangThai == 0)
                                    {
                                        @if (isOverdue)
                                        {
                                            <div class="text-center">
                                                <span class="badge bg-warning text-dark">
                                                    <i class="bi bi-clock-history me-1"></i>Quá hạn
                                                </span>
                                            </div>
                                        }
                                        else
                                        {
                                            <div class="d-grid gap-2">
                                                <form method="post" asp-action="ApproveRegistration">
                                                    @Html.AntiForgeryToken()
                                                    <input type="hidden" name="maSv" value="@x.Masv" />
                                                    <input type="hidden" name="maDt" value="@x.MaDt" />
                                                    <input type="hidden" name="hocKy" value="@Model.Filter.HocKy" />
                                                    <input type="hidden" name="namHoc" value="@Model.Filter.NamHoc" />
                                                    <input type="hidden" name="trangThai" value="@Model.Filter.TrangThai" />
                                                    <input type="hidden" name="filterMaDt" value="@Model.Filter.MaDt" />
                                                    <button type="submit" class="btn btn-primary btn-sm w-100">
                                                        <i class="bi bi-check-circle me-1"></i>Duyệt
                                                    </button>
                                                </form>
                                                <form method="post" asp-action="RejectRegistration">
                                                    @Html.AntiForgeryToken()
                                                    <input type="hidden" name="maSv" value="@x.Masv" />
                                                    <input type="hidden" name="maDt" value="@x.MaDt" />
                                                    <input type="hidden" name="hocKy" value="@Model.Filter.HocKy" />
                                                    <input type="hidden" name="namHoc" value="@Model.Filter.NamHoc" />
                                                    <input type="hidden" name="trangThai" value="@Model.Filter.TrangThai" />
                                                    <input type="hidden" name="filterMaDt" value="@Model.Filter.MaDt" />
                                                    <button type="submit" class="btn btn-danger btn-sm w-100">
                                                        <i class="bi bi-x-circle me-1"></i>Từ chối
                                                    </button>
                                                </form>
                                            </div>
                                        }
                                    }
                                    @* 1 = Accepted: thêm nút Bắt đầu -> InProgress (2) *@
                                    else if (x.TrangThai == 1)
                                    {
                                        <div class="text-center">
                                            <form method="post" asp-action="SetHuongDanInProgress" class="d-inline">
                                                @Html.AntiForgeryToken()
                                                <input type="hidden" name="maSv" value="@x.Masv" />
                                                <input type="hidden" name="maDt" value="@x.MaDt" />
                                                <input type="hidden" name="ghiChu" value="Bắt đầu thực hiện" />
                                                <input type="hidden" name="hocKy" value="@Model.Filter.HocKy" />
                                                <input type="hidden" name="namHoc" value="@Model.Filter.NamHoc" />
                                                <input type="hidden" name="trangThai" value="@Model.Filter.TrangThai" />
                                                <input type="hidden" name="filterMaDt" value="@Model.Filter.MaDt" />
                                                <button type="submit" class="btn btn-outline-primary btn-sm">
                                                    <i class="bi bi-play-circle me-1"></i>Bắt đầu
                                                </button>
                                            </form>
                                        </div>
                                    }
                                    @* 2 = InProgress: thêm nút Hoàn thành (mở modal nhập điểm) *@
                                    else if (x.TrangThai == 2)
                                    {
                                        <div class="text-center">
                                            <button type="button"
                                                    class="btn btn-success btn-sm"
                                                    data-bs-toggle="modal"
                                                    data-bs-target="#@modalId">
                                                <i class="bi bi-flag-checkered me-1"></i>Hoàn thành
                                            </button>
                                        </div>

                                        @* Modal nhập điểm *@
                                        <div class="modal fade" id="@modalId" tabindex="-1" aria-hidden="true">
                                            <div class="modal-dialog modal-dialog-centered">
                                                <div class="modal-content">
                                                    <div class="modal-header">
                                                        <h5 class="modal-title">Nhập điểm hoàn thành</h5>
                                                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                    </div>
                                                    <form method="post" asp-action="SetHuongDanCompleted">
                                                        @Html.AntiForgeryToken()
                                                        <div class="modal-body">
                                                            <div class="mb-3">
                                                                <label class="form-label">Sinh viên</label>
                                                                <div class="form-control-plaintext fw-semibold">@x.HotenSv (#@x.Masv)</div>
                                                            </div>
                                                            <div class="mb-3">
                                                                <label class="form-label">Đề tài</label>
                                                                <div class="form-control-plaintext">@x.TenDt (<span class="text-monospace">@x.MaDt</span>)</div>
                                                            </div>
                                                            <div class="mb-3">
                                                                <label class="form-label" for="ketQua_@modalId">Điểm (0–10)</label>
                                                                <input type="number" step="0.01" min="0" max="10"
                                                                       class="form-control" id="ketQua_@modalId" name="ketQua" required />
                                                            </div>
                                                            <div class="mb-2">
                                                                <label class="form-label" for="ghiChu_@modalId">Ghi chú</label>
                                                                <input type="text" class="form-control" id="ghiChu_@modalId" name="ghiChu" />
                                                            </div>

                                                            @* Hidden giữ ngữ cảnh *@
                                                            <input type="hidden" name="maSv" value="@x.Masv" />
                                                            <input type="hidden" name="maDt" value="@x.MaDt" />
                                                            <input type="hidden" name="hocKy" value="@Model.Filter.HocKy" />
                                                            <input type="hidden" name="namHoc" value="@Model.Filter.NamHoc" />
                                                            <input type="hidden" name="trangThai" value="@Model.Filter.TrangThai" />
                                                            <input type="hidden" name="filterMaDt" value="@Model.Filter.MaDt" />
                                                        </div>
                                                        <div class="modal-footer">
                                                            <button type="button" class="btn btn-light" data-bs-dismiss="modal">Hủy</button>
                                                            <button type="submit" class="btn btn-success">
                                                                <i class="bi bi-check2-circle me-1"></i>Xác nhận hoàn thành
                                                            </button>
                                                        </div>
                                                    </form>
                                                </div>
                                            </div>
                                        </div>
                                    }
                                    else
                                    {
                                        <div class="text-center">
                                            <span class="text-muted">—</span>
                                        </div>
                                    }
                                </td>
                            </tr>
                            stt++;
                        }
                    }
                </tbody>
            </table>
        </div>

    </div>
</div>

@if (TempData["Toast"] is string toast && !string.IsNullOrWhiteSpace(toast))
{
    <div class="alert alert-info mt-3">@toast</div>
}

@section Scripts {
    <script>
        // Tự động đồng bộ dropdown HK-Năm -> hidden inputs hocKy/namHoc
        (function () {
            const sel = document.getElementById('hkNamSelect');
            const hkH = document.getElementById('hocKyHidden');
            const nhH = document.getElementById('namHocHidden');

            function syncHkNam() {
                const v = sel.value; // "1-2025" hoặc ""
                if (!v) {
                    hkH.value = ""; nhH.value = "";
                    return;
                }
                const parts = v.split("-");
                hkH.value = parts[0] || "";
                nhH.value = parts[1] || "";
            }

            sel.addEventListener('change', syncHkNam);
            // Đồng bộ ngay khi load nếu có sẵn giá trị
            syncHkNam();
        })();
    </script>
}
