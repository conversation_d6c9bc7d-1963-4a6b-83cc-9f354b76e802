@model GvRegistrationsPageVm
@{
    ViewData["Title"] = "Sinh viên đăng ký đề tài";
    byte? selectedHk = Model.Filter.HocKy;
    string? selectedYear = Model.Filter.NamHoc;


}

@* Syncfusion Toast *@
<ejs-toast id="toast_type" close="onclose" beforeOpen="onBeforeOpen" created="created" timeOut="10000">
    <e-toast-position X="Right" Y="Top"></e-toast-position>
</ejs-toast>

@* Server messages bridge for toasts *@
<div id="serverMessages"
     data-success="@TempData["Toast"]"
     data-warning=""
     data-error=""
     style="display:none;"></div>

<style>
    .action-cell {
        min-height: 80px;
        vertical-align: middle;
        width: 140px;
    }

    .action-cell .badge {
        font-size: 0.75rem;
        padding: 0.375rem 0.5rem;
    }
</style>
<div class="card shadow-sm">
    <div class="card-header bg-white d-flex justify-content-between align-items-center">
        <h5 class="mb-0"><PERSON>h viên đăng ký đề tài</h5>
        <div class="small text-muted">Tổng: <strong>@Model.Items.Count</strong></div>
    </div>

    <div class="card-body">

        <form method="get" class="row g-3 align-items-end mb-3" id="filterForm">
            <div class="col-md-3">
                <label class="form-label">Học kỳ - Năm học</label>
                <div class="searchable-dropdown" id="termDropdown">
                    <input type="hidden" value="@(selectedHk.HasValue && !string.IsNullOrEmpty(selectedYear) ? ($"{selectedHk}|{selectedYear}") : "")" />
                    <input type="text" class="form-control" autocomplete="off">
                    <i class="bi bi-chevron-down dropdown-arrow"></i>
                    <div class="dropdown-menu">
                        <div class="dropdown-options">
                        </div>
                    </div>
                </div>
                <input type="hidden" name="hocKy" value="@(selectedHk?.ToString() ?? "")" />
                <input type="hidden" name="namHoc" value="@(selectedYear ?? "")" />
            </div>
            <div class="col-md-3">
                <label class="form-label">Đề tài</label>
                <div class="searchable-dropdown" id="deTaiDropdown">
                    <input type="hidden" name="maDt" value="@Model.Filter.MaDt" />
                    <input type="text" class="form-control" placeholder="Tìm kiếm đề tài..." autocomplete="off">
                    <i class="bi bi-chevron-down dropdown-arrow"></i>
                    <div class="dropdown-menu">
                        <div class="dropdown-options">
                            <a class="dropdown-item" href="#" data-value="">-- Tất cả --</a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <label class="form-label">Trạng thái</label>
                <div class="searchable-dropdown" id="trangThaiDropdown">
                    <input type="hidden" name="trangThai" value="@Model.Filter.TrangThai" />
                    <input type="text" class="form-control" autocomplete="off">
                    <i class="bi bi-chevron-down dropdown-arrow"></i>
                    <div class="dropdown-menu">
                        <div class="dropdown-options">
                            <a class="dropdown-item" href="#" data-value="0">Đang chờ duyệt</a>
                            <a class="dropdown-item" href="#" data-value="1">Chấp nhận</a>
                            <a class="dropdown-item" href="#" data-value="2">Đang thực hiện</a>
                            <a class="dropdown-item" href="#" data-value="3">Hoàn thành</a>
                            <a class="dropdown-item" href="#" data-value="4">Đã từ chối</a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="d-flex gap-2">
                    <button class="btn btn-primary py-2" type="submit" id="btnFilter">
                        <i class="bi bi-funnel me-1"></i>Lọc
                    </button>
                    <a class="btn btn-outline-secondary py-2" asp-action="Registrations">
                        <i class="bi bi-trash me-1"></i>Xóa lọc
                    </a>
                    <a class="btn btn-success py-2"
                       asp-action="ExportRegistrationsExcel"
                       asp-route-hocKy="@(Model.Filter.HocKy)"
                       asp-route-namHoc="@(Model.Filter.NamHoc)"
                       asp-route-trangThai="@(Model.Filter.TrangThai)"
                       asp-route-maDt="@(Model.Filter.MaDt)">
                        <i class="bi bi-file-earmark-excel me-1"></i>Excel
                    </a>
                </div>
            </div>
        </form>

        <!-- Responsive Grid Container -->
        <div class="grid-responsive">
            <div class="grid-container">
                <ejs-grid id="RegistrationsGrid" dataSource="@Model.Items" allowPaging="true" allowSorting="true"
                          allowMultiSorting="true" allowFiltering="false" allowResizing="true" gridLines="Both"
                          height="600" width="100%" grid-lines="Both" enable-hover="false" enable-alt-row="true">
                    <e-grid-pagesettings pageSize="20" pageSizes="new List<int>{10,20,50,100}"></e-grid-pagesettings>
                    <e-grid-columns>
                        <e-grid-column headerText="STT" width="60" textAlign="Center" template="#sttTemplate"></e-grid-column>
                        <e-grid-column field="NgayDangKy" headerText="Ngày đăng ký" width="120" textAlign="Center" template="#ngayDkTemplate"></e-grid-column>
                        <e-grid-column headerText="Sinh viên" width="250" template="#sinhVienTemplate"></e-grid-column>
                        <e-grid-column headerText="Đề tài" width="300" template="#deTaiTemplate"></e-grid-column>
                        <e-grid-column headerText="Học kỳ" width="120" template="#hocKyTemplate"></e-grid-column>
                        <e-grid-column field="TrangThai" headerText="Trạng thái" width="120" textAlign="Center" template="#trangThaiTemplate"></e-grid-column>
                        <e-grid-column field="KetQua" headerText="Kết quả" width="100" textAlign="Center" template="#ketQuaTemplate"></e-grid-column>
                        <e-grid-column headerText="Thao tác" width="150" textAlign="Center" template="#actionTemplate"></e-grid-column>
                    </e-grid-columns>
                </ejs-grid>
            </div>
        </div>


    </div>
</div>

@* Templates for DataGrid *@
<script id="sttTemplate" type="text/x-template">
    # var index = data.index || 0; #
    ${index + 1}
</script>

<script id="ngayDkTemplate" type="text/x-template">
    # var dateStr = formatDate(data.NgayDangKy); #
    ${dateStr}
</script>

<script id="sinhVienTemplate" type="text/x-template">
    <div class="fw-semibold">${HotenSv} (#${Masv})</div>
    <div class="text-muted small">${Sv_TenKhoa} (${Sv_MaKhoa})</div>
    # if (data.QueQuan) { #
        <div class="text-muted small">QQ: ${QueQuan}</div>
    # } #
</script>

<script id="deTaiTemplate" type="text/x-template">
    <div class="fw-semibold">${TenDt}</div>
    <div class="text-muted small">Mã: <span class="text-monospace">${MaDt}</span></div>
</script>

<script id="hocKyTemplate" type="text/x-template">
    ${HocKy} - ${NamHoc}
</script>

<script id="trangThaiTemplate" type="text/x-template">
    # var badgeInfo = getBadgeInfo(data.TrangThai); #
    <span class="badge ${badgeInfo.class}">${badgeInfo.label}</span>
</script>

<script id="ketQuaTemplate" type="text/x-template">
    # if (data.KetQua !== null && data.KetQua !== undefined) { #
        <span class="fw-semibold">${KetQua}</span>
    # } else { #
        <span class="text-muted">-</span>
    # } #
</script>

<script id="actionTemplate" type="text/x-template">
    # var isOverdue = checkOverdue(data.HocKy, data.NamHoc); #
    # if (data.TrangThai === 0) { #
        # if (isOverdue) { #
            <div class="text-center">
                <span class="badge bg-warning text-dark">
                    <i class="bi bi-clock-history me-1"></i>Quá hạn
                </span>
            </div>
        # } else { #
            <div class="d-grid gap-2">
                <button type="button" class="btn btn-primary btn-sm w-100" onclick="approveRegistration('${Masv}', '${MaDt}')">
                    <i class="bi bi-check-circle me-1"></i>Duyệt
                </button>
                <button type="button" class="btn btn-danger btn-sm w-100" onclick="rejectRegistration('${Masv}', '${MaDt}')">
                    <i class="bi bi-x-circle me-1"></i>Từ chối
                </button>
            </div>
        # } #
    # } else if (data.TrangThai === 1) { #
        <div class="text-center">
            <button type="button" class="btn btn-outline-primary btn-sm" onclick="startRegistration('${Masv}', '${MaDt}')">
                <i class="bi bi-play-circle me-1"></i>Bắt đầu
            </button>
        </div>
    # } else if (data.TrangThai === 2) { #
        <div class="text-center">
            <button type="button" class="btn btn-success btn-sm" onclick="showCompleteModal('${Masv}', '${MaDt}', '${HotenSv}', '${TenDt}')">
                <i class="bi bi-flag-checkered me-1"></i>Hoàn thành
            </button>
        </div>
    # } else { #
        <div class="text-center">
            <span class="text-muted">—</span>
        </div>
    # } #
</script>

@* Modal for completing registration *@
<div class="modal fade" id="completeModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Nhập điểm hoàn thành</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="post" asp-action="SetHuongDanCompleted" id="completeForm">
                @Html.AntiForgeryToken()
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">Sinh viên</label>
                        <div class="form-control-plaintext fw-semibold" id="modalSinhVien"></div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Đề tài</label>
                        <div class="form-control-plaintext" id="modalDeTai"></div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label" for="ketQua">Điểm (0–10)</label>
                        <input type="number" step="0.01" min="0" max="10" class="form-control" id="ketQua" name="ketQua" required />
                    </div>
                    <div class="mb-2">
                        <label class="form-label" for="ghiChu">Ghi chú</label>
                        <input type="text" class="form-control" id="ghiChu" name="ghiChu" />
                    </div>
                    <input type="hidden" name="maSv" id="modalMaSv" />
                    <input type="hidden" name="maDt" id="modalMaDt" />
                    <input type="hidden" name="hocKy" value="@Model.Filter.HocKy" />
                    <input type="hidden" name="namHoc" value="@Model.Filter.NamHoc" />
                    <input type="hidden" name="trangThai" value="@Model.Filter.TrangThai" />
                    <input type="hidden" name="filterMaDt" value="@Model.Filter.MaDt" />
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-light" data-bs-dismiss="modal">Hủy</button>
                    <button type="submit" class="btn btn-success">
                        <i class="bi bi-check2-circle me-1"></i>Xác nhận hoàn thành
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

@section Scripts {
    <link href="~/css/searchable-dropdown.css" rel="stylesheet" />
    <script src="~/js/searchable-dropdown.js"></script>
    <script>
        // Helper functions for templates
        function getBadgeInfo(trangThai) {
            switch (trangThai) {
                case 0: return { label: "Đang chờ duyệt", class: "bg-warning text-dark" };
                case 1: return { label: "Chấp nhận", class: "bg-success text-white" };
                case 2: return { label: "Đang thực hiện", class: "bg-info text-dark" };
                case 3: return { label: "Hoàn thành", class: "bg-success text-white" };
                case 4: return { label: "Đã từ chối", class: "bg-danger text-white" };
                default: return { label: "Khác", class: "bg-secondary text-white" };
            }
        }

        function formatDate(dateStr) {
            if (!dateStr) return "";
            var date = new Date(dateStr);
            if (isNaN(date.getTime())) return "";
            return date.toLocaleDateString('vi-VN');
        }

        function checkOverdue(hk, namHoc) {
            if (!hk || !namHoc) return false;
            var now = new Date();
            var parts = namHoc.split('-');
            if (parts.length !== 2) return false;

            var startYear = parseInt(parts[0]);
            var endYear = parseInt(parts[1]);
            if (isNaN(startYear) || isNaN(endYear)) return false;

            var hkStart, hkEnd;
            switch (hk) {
                case 1:
                    hkStart = new Date(startYear, 8, 1); // September
                    hkEnd = new Date(startYear, 11, 31); // December
                    break;
                case 2:
                    hkStart = new Date(endYear, 0, 1); // January
                    hkEnd = new Date(endYear, 3, 30); // April
                    break;
                case 3:
                    hkStart = new Date(endYear, 4, 1); // May
                    hkEnd = new Date(endYear, 7, 31); // August
                    break;
                default:
                    return false;
            }

            return now > hkEnd;
        }

        // Action functions
        function approveRegistration(maSv, maDt) {
            if (confirm('Bạn có chắc chắn muốn duyệt đăng ký này?')) {
                submitAction('ApproveRegistration', maSv, maDt);
            }
        }

        function rejectRegistration(maSv, maDt) {
            if (confirm('Bạn có chắc chắn muốn từ chối đăng ký này?')) {
                submitAction('RejectRegistration', maSv, maDt);
            }
        }

        function startRegistration(maSv, maDt) {
            if (confirm('Bắt đầu thực hiện đề tài này?')) {
                submitActionWithGhiChu('SetHuongDanInProgress', maSv, maDt, 'Bắt đầu thực hiện');
            }
        }

        function showCompleteModal(maSv, maDt, hotenSv, tenDt) {
            document.getElementById('modalSinhVien').textContent = hotenSv + ' (#' + maSv + ')';
            document.getElementById('modalDeTai').innerHTML = tenDt + ' (<span class="text-monospace">' + maDt + '</span>)';
            document.getElementById('modalMaSv').value = maSv;
            document.getElementById('modalMaDt').value = maDt;
            document.getElementById('ketQua').value = '';
            document.getElementById('ghiChu').value = '';
            new bootstrap.Modal(document.getElementById('completeModal')).show();
        }

        function submitAction(action, maSv, maDt) {
            var form = document.createElement('form');
            form.method = 'post';
            form.action = '@Url.Action("", "DeTai")'.replace('""', '"' + action + '"');

            var token = document.querySelector('input[name="__RequestVerificationToken"]').value;
            form.innerHTML = `
                <input type="hidden" name="__RequestVerificationToken" value="${token}" />
                <input type="hidden" name="maSv" value="${maSv}" />
                <input type="hidden" name="maDt" value="${maDt}" />
                <input type="hidden" name="hocKy" value="@Model.Filter.HocKy" />
                <input type="hidden" name="namHoc" value="@Model.Filter.NamHoc" />
                <input type="hidden" name="trangThai" value="@Model.Filter.TrangThai" />
                <input type="hidden" name="filterMaDt" value="@Model.Filter.MaDt" />
            `;

            document.body.appendChild(form);
            form.submit();
        }

        function submitActionWithGhiChu(action, maSv, maDt, ghiChu) {
            var form = document.createElement('form');
            form.method = 'post';
            form.action = '@Url.Action("", "DeTai")'.replace('""', '"' + action + '"');

            var token = document.querySelector('input[name="__RequestVerificationToken"]').value;
            form.innerHTML = `
                <input type="hidden" name="__RequestVerificationToken" value="${token}" />
                <input type="hidden" name="maSv" value="${maSv}" />
                <input type="hidden" name="maDt" value="${maDt}" />
                <input type="hidden" name="ghiChu" value="${ghiChu}" />
                <input type="hidden" name="hocKy" value="@Model.Filter.HocKy" />
                <input type="hidden" name="namHoc" value="@Model.Filter.NamHoc" />
                <input type="hidden" name="trangThai" value="@Model.Filter.TrangThai" />
                <input type="hidden" name="filterMaDt" value="@Model.Filter.MaDt" />
            `;

            document.body.appendChild(form);
            form.submit();
        }

        // Syncfusion Toast functions
        function onclose(e) {
            // Toast closed
        }

        function onBeforeOpen(e) {
            // Before toast opens
        }

        function created() {
            // Show server messages as toasts
            const serverMessages = document.getElementById('serverMessages');
            if (serverMessages) {
                const success = serverMessages.dataset.success;
                const warning = serverMessages.dataset.warning;
                const error = serverMessages.dataset.error;

                const toastObj = document.getElementById('toast_type').ej2_instances[0];

                if (success) {
                    toastObj.show({
                        title: 'Thành công',
                        content: success,
                        cssClass: 'e-toast-success',
                        icon: 'e-success toast-icons'
                    });
                }
                if (warning) {
                    toastObj.show({
                        title: 'Cảnh báo',
                        content: warning,
                        cssClass: 'e-toast-warning',
                        icon: 'e-warning toast-icons'
                    });
                }
                if (error) {
                    toastObj.show({
                        title: 'Lỗi',
                        content: error,
                        cssClass: 'e-toast-danger',
                        icon: 'e-error toast-icons'
                    });
                }
            }
        }

        document.addEventListener('DOMContentLoaded', function () {
            // Handle filter form submission
            const filterForm = document.getElementById('filterForm');
            filterForm.addEventListener('submit', function(e) {
                e.preventDefault();

                // Build query string with filter parameters
                var formData = new FormData(this);
                var queryParams = new URLSearchParams();

                for (var pair of formData.entries()) {
                    queryParams.append(pair[0], pair[1]);
                }

                window.location.href = '@Url.Action("Registrations", "DeTai")?' + queryParams.toString();
            });

            // Term dropdown (combine HK/Năm học)
            const termEl = document.getElementById('termDropdown');
            const termDd = new SearchableDropdown('termDropdown', { placeholder: '', searchUrl: null, allowClear: true });

            function generateTerms(baseYear) {
                const out = [];
                for (let y = baseYear - 5; y <= baseYear; y++) {
                    const nh = `${y}-${y + 1}`;
                    for (let hk = 1; hk <= 3; hk++) out.push({ value: `${hk}|${nh}`, text: `HK${hk} (${nh})`, hk, nh });
                }
                return out;
            }

            function filterTerms(all, query) {
                if (!query) return all;
                const q = String(query).toLowerCase().replace(/\s+/g, '');
                const m = q.match(/^(\d{4})$/);
                if (m) {
                    const Y = parseInt(m[1], 10);
                    const expanded = [];
                    for (let hk = 1; hk <= 3; hk++) expanded.push({ value: `${hk}|${Y}-${Y + 1}`, text: `HK${hk} (${Y}-${Y + 1})`, hk, nh: `${Y}-${Y + 1}` });
                    for (let hk = 1; hk <= 3; hk++) expanded.push({ value: `${hk}|${Y - 1}-${Y}`, text: `HK${hk} (${Y - 1}-${Y})`, hk, nh: `${Y - 1}-${Y}` });
                    return expanded;
                }
                return all.filter(x => x.text.toLowerCase().includes(q));
            }

            const nowY = new Date().getFullYear();
            let allTerms = generateTerms(nowY).sort((a, b) => (a.nh < b.nh ? 1 : a.nh > b.nh ? -1 : b.hk - a.hk));
            termDd.searchData = async function (query) {
                const options = filterTerms(allTerms, query);
                await termDd.updateOptions(options.map(o => ({ value: o.value, text: o.text })));
            };
            termDd.searchData('');

            termEl.addEventListener('selectionChanged', function (e) {
                const val = e?.detail?.value ?? '';
                const [hk, nh] = val.includes('|') ? val.split('|') : ['', ''];
                const hkInput = document.querySelector('input[name="hocKy"]');
                const nhInput = document.querySelector('input[name="namHoc"]');
                if (hkInput) hkInput.value = hk;
                if (nhInput) nhInput.value = nh;

                // Refresh đề tài options when term changes
                deTaiDropdown.clear();
                deTaiDropdown.loadInitialData();
            });

            // Đề tài dropdown (dynamic options based on học kỳ and năm học)
            const deTaiDropdown = new SearchableDropdown('deTaiDropdown', {
                placeholder: 'Tìm kiếm đề tài...',
                searchUrl: '@Url.Action("GetTopicsByTerm", "DeTai")',
                searchParam: 'q',
                debounceDelay: 300,
                allowClear: true,
                labelFormatter: function(item) {
                    if (item && item.MaDt && item.TenDt) {
                        return { value: item.MaDt, text: `${item.MaDt} - ${item.TenDt}` };
                    }
                    return null;
                },
                getFilterValue: function() {
                    const hocKy = document.querySelector('input[name="hocKy"]').value;
                    const namHoc = document.querySelector('input[name="namHoc"]').value;
                    return { hocKy: hocKy, namHoc: namHoc };
                }
            });

            // Trạng thái dropdown (static options)
            const trangThaiEl = document.getElementById('trangThaiDropdown');
            const trangThaiDd = new SearchableDropdown('trangThaiDropdown', { placeholder: '', searchUrl: null, allowClear: true });

            function getTrangThaiOptionsFromDom() {
                const opts = [];
                trangThaiEl.querySelectorAll('.dropdown-options a.dropdown-item').forEach(a => {
                    const v = a.getAttribute('data-value') ?? '';
                    const t = a.textContent ?? '';
                    opts.push({ value: v, text: t });
                });
                return opts;
            }

            trangThaiDd.searchData = async function(query){
                const all = getTrangThaiOptionsFromDom();
                const q = (query || '').toLowerCase();
                const filtered = all.filter(o => !q || o.text.toLowerCase().includes(q) || o.value === q);
                await trangThaiDd.updateOptions(filtered);
            };
            trangThaiDd.searchData('');

            // Helper functions for combobox behavior
            function setAllIfEmpty(container, hiddenSelector) {
                const input = container?.querySelector('input.form-control');
                const hidden = hiddenSelector ? document.querySelector(hiddenSelector) : container?.querySelector('input[type="hidden"]');
                if (input && (!input.value || input.value.trim() === '') && (!hidden || !hidden.value)) {
                    input.value = '-- Tất cả --';
                }
            }

            function installComboBehavior(container, hiddenSelector) {
                const input = container?.querySelector('input.form-control');
                const hidden = hiddenSelector ? document.querySelector(hiddenSelector) : container?.querySelector('input[type="hidden"]');
                if (!input) return;
                input.addEventListener('focus', function () {
                    if (this.value.trim() === '-- Tất cả --') this.value = '';
                });
                input.addEventListener('blur', function () {
                    const hv = hidden ? hidden.value : '';
                    if (this.value.trim() === '' && !hv) {
                        this.value = '-- Tất cả --';
                    }
                });
            }

            // Initialize display values
            const termInput = termEl?.querySelector('input.form-control');
            const hkHidden = document.querySelector('input[name="hocKy"]');
            const nhHidden = document.querySelector('input[name="namHoc"]');
            if (termInput) {
                if (hkHidden?.value && nhHidden?.value) {
                    termInput.value = `HK${hkHidden.value} (${nhHidden.value})`;
                } else {
                    termInput.value = '-- Tất cả --';
                }
            }

            // Apply combobox behavior
            setAllIfEmpty(termEl, null);
            setAllIfEmpty(trangThaiEl, null);
            installComboBehavior(termEl, null);
            installComboBehavior(trangThaiEl, null);

            // Prevent empty state for term dropdown
            const termTextInput = termEl?.querySelector('input.form-control');
            termTextInput?.addEventListener('input', function(){
                if (this.value.trim() === '') {
                    const hkInput = document.querySelector('input[name="hocKy"]');
                    const nhInput = document.querySelector('input[name="namHoc"]');
                    if (hkInput) hkInput.value = '';
                    if (nhInput) nhInput.value = '';
                    this.value = '-- Tất cả --';
                }
            });

            // Prevent empty state for trạng thái dropdown
            const trangThaiTextInput = trangThaiEl?.querySelector('input.form-control');
            trangThaiTextInput?.addEventListener('input', function(){
                if (this.value.trim() === '') {
                    const hidden = document.querySelector('input[name="trangThai"]');
                    if (hidden) hidden.value = '';
                    this.value = '-- Tất cả --';
                }
            });
        });
    </script>
}
