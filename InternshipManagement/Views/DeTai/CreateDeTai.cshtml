﻿@model InternshipManagement.Models.DTOs.DeTaiCreateDto
@{
    ViewData["Title"] = "Thêm đề tài";
    // năm hiện tại ± 5 năm
    var nowY = DateTime.Now.Year;
    var years = Enumerable.Range(nowY - 5, 11).Select(y => $"{y}-{y+1}").ToList();
}

<div class="card shadow-sm">
    <div class="card-header bg-white d-flex justify-content-between align-items-center">
        <h5 class="mb-0"><i class="bi bi-plus-circle me-2"></i>Thêm đề tài</h5>
    </div>
    <div class="card-body">
        <form asp-action="CreateDeTai" method="post" novalidate>
            @Html.AntiForgeryToken()
            <div asp-validation-summary="ModelOnly" class="alert alert-danger" role="alert"></div>

            <div class="row g-3">
                <div class="col-md-12">
                    <label asp-for="TenDt" class="form-label">Tên đề tài</label>
                    <input asp-for="TenDt" class="form-control" />
                    <span asp-validation-for="TenDt" class="text-danger"></span>
                </div>


                <div class="col-md-8">
                    <label asp-for="NoiThucTap" class="form-label">Nơi thực tập</label>
                    <input asp-for="NoiThucTap" class="form-control" />
                    <span asp-validation-for="NoiThucTap" class="text-danger"></span>
                </div>

                <div class="col-md-4">
                    <label asp-for="KinhPhi" class="form-label">Kinh phí</label>
                    <input asp-for="KinhPhi" type="number" class="form-control" />
                    <span asp-validation-for="KinhPhi" class="text-danger"></span>
                </div>

                <div class="col-md-2">
                    <label asp-for="HocKy" class="form-label">Học kỳ</label>
                    <select asp-for="HocKy" class="form-select">
                        <option value="1">HK1</option>
                        <option value="2">HK2</option>
                        <option value="3">HK3</option>
                    </select>
                    <span asp-validation-for="HocKy" class="text-danger"></span>
                </div>

                <div class="col-md-3">
                    <label asp-for="NamHoc" class="form-label">Năm học</label>
                    <select asp-for="NamHoc" class="form-select">
                        @foreach (var y in years)
                        {
                            <option value="@y" selected="@(Model.NamHoc == y)">@y</option>
                        }
                    </select>
                    <span asp-validation-for="NamHoc" class="text-danger"></span>
                </div>

                <div class="col-md-3">
                    <label asp-for="SoLuongToiDa" class="form-label">Số lượng tối đa</label>
                    <input asp-for="SoLuongToiDa" type="number" class="form-control" min="0" />
                    <span asp-validation-for="SoLuongToiDa" class="text-danger"></span>
                </div>
            </div>

            <div class="d-flex gap-2 mt-4">
                <button type="submit" class="btn btn-primary">
                    <i class="bi bi-save me-1"></i> Lưu
                </button>
                <a asp-action="Manage" class="btn btn-outline-secondary">
                    <i class="bi bi-arrow-left me-1"></i> Quay lại
                </a>
            </div>
        </form>
    </div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}
