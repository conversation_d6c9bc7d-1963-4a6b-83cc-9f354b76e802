﻿@model InternshipManagement.Models.ViewModels.GiangVienIndexVm
@{
    ViewData["Title"] = "Danh sách Giảng viên";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<div class="card shadow-sm">
    <div class="card-header bg-white d-flex align-items-center justify-content-between">
        <h5 class="mb-0">Danh sách Giảng viên</h5>
        <div class="small text-muted">Tổng: <strong>@Model.Items.Count</strong> bản ghi</div>
        </div>

    <div class="card shadow-sm mb-3">
        <div class="card-body">
            <form method="get" id="filterForm" class="row g-3">
                @Html.AntiForgeryToken()
                <div class="col-md-4">
                    <label class="form-label">Từ khóa</label>
                    <input class="form-control" asp-for="Filter.Keyword" placeholder="Mã GV hoặc họ tên" />
            </div>
                <div class="col-md-4">
                    <label class="form-label">Khoa</label>
                    <div class="searchable-dropdown" id="khoaDropdown">
                        <input type="hidden" asp-for="Filter.MaKhoa" />
                        <input type="text" class="form-control" placeholder="Tìm kiếm khoa..." autocomplete="off">
                        <i class="bi bi-chevron-down dropdown-arrow"></i>
                        <div class="dropdown-menu">
                            <div class="dropdown-options">
                                <a class="dropdown-item" href="#" data-value="">-- Tất cả --</a>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <label class="form-label">Lương từ</label>
                    <input class="form-control" type="number" asp-for="Filter.LuongMin" min="0" step="1" />
            </div>
                <div class="col-md-2">
                    <label class="form-label">Đến</label>
                    <input class="form-control" type="number" asp-for="Filter.LuongMax" min="0" step="1" />
            </div>

                <div class="col-md-12 d-flex justify-content-end gap-2">
                    <button type="submit" form="filterForm" class="btn btn-primary d-flex align-items-center px-3 py-2">
                        <i class="bi bi-funnel me-1"></i> Lọc
                </button>
                    <a asp-action="Index" class="btn btn-outline-secondary d-flex align-items-center px-3 py-2">
                        <i class="bi-arrow-clockwise me-1"></i> Hủy
                </a>
                    <button type="button" id="btnAddGiangVien" class="btn btn-success d-flex align-items-center px-3 py-2" onclick="showAddGiangVienDialog()">
                    <i class="bi bi-person-fill-add me-1"></i> Thêm GV
                    </button>
                    <button type="button" class="btn btn-primary d-flex align-items-center px-3 py-2" onclick="showImportDialog()">
                        <i class="bi bi-file-earmark-excel me-1"></i> Import Excel
                    </button>
                    <button type="button" class="btn btn-success d-flex align-items-center px-3 py-2" onclick="showExportDialog()">
                        <i class="bi bi-file-earmark-spreadsheet me-1"></i> Export Excel
                    </button>
            </div>
            </form>
        </div>
    </div>

    @* Syncfusion Toast *@
    <ejs-toast id="toast_type" close="onclose" beforeOpen="onBeforeOpen" created="created" timeOut="10000">
        <e-toast-position X="Right" Y="Top"></e-toast-position>
    </ejs-toast>

    <script>
        // Toast functions must be defined BEFORE the tag helper renders
        var toastObj;

        window.created = function() {
            toastObj = this;
            toastObj.target = document.body;
            toastObj.position = { X: 'Right', Y: 'Top' };
            toastObj.timeOut = 10000;
        };

        window.onclose = function(e) {
            // Toast closed
        };

        window.onBeforeOpen = function() {
            // Toast opening
        };
    </script>


    <!-- Responsive Grid Container -->
    <div class="grid-responsive">
        <div class="grid-container">
            <ejs-grid id="GiangVienGrid" dataSource="@Model.Items" allowPaging="true" allowSorting="true" allowMultiSorting="true" allowFiltering="false" allowResizing="true" gridLines="Both" height="600" width="100%" grid-lines="Both" enable-hover="false" enable-alt-row="true">
                <e-grid-pagesettings pageSize="20" pageSizes="new List<int>{10,20,50,100,500}"></e-grid-pagesettings>
                <e-grid-columns>
                    <e-grid-column field="Magv" headerText="Mã GV" width="100" template="#maGvTemplate"></e-grid-column>
                    <e-grid-column field="Hotengv" headerText="Họ tên" width="250"></e-grid-column>
                    <e-grid-column headerText="Khoa" width="150" template="#khoaTemplate"></e-grid-column>
                    <e-grid-column field="Luong" headerText="Lương" width="120" textAlign="Center" format="N0"></e-grid-column>
                    <e-grid-column headerText="Hành động" width="150" textAlign="Center" template="#actionTemplate"></e-grid-column>
                </e-grid-columns>
            </ejs-grid>
        </div>
    </div>

    @* Templates for custom column rendering *@
    <script id="maGvTemplate" type="text/x-template">
        <span class="ma-gv-code">${Magv}</span>
    </script>

    <script id="khoaTemplate" type="text/x-template">
        ${TenKhoa}
    </script>

    <script id="actionTemplate" type="text/x-template">
        <div class="d-flex gap-1 justify-content-center">
            <a class="btn btn-sm btn-primary text-white" href="/GiangVien/Details/${Magv}" title="Xem chi tiết">
                <i class="bi bi-eye"></i>
            </a>
            <button type="button" class="btn btn-sm btn-warning text-white" onclick="editGiangVien(${Magv})" title="Sửa">
                                <i class="bi bi-pencil-square"></i>
            </button>
            <button type="button" class="btn btn-sm btn-danger" data-id="${Magv}" data-name="${Hotengv}" onclick="showDeleteDialogFromButton(this)" title="Xóa">
                <i class="bi bi-trash"></i>
            </button>
    </div>
    </script>
</div>

    <div id="hiddenDialogs" class="d-none">
    @* SyncFusion Dialog for Create/Edit *@
    <ejs-dialog id="giangVienDialog" header="Thêm Giảng viên" isModal="true" showCloseIcon="true" width="500px" height="auto" animationSettings="new Syncfusion.EJ2.Popups.DialogAnimationSettings { Effect = Syncfusion.EJ2.Popups.DialogEffect.Zoom }" visible="false">
        <e-content-template>
            <form id="giangVienForm">
                <input type="hidden" id="MaGv" name="MaGv" />

                <div class="mb-3">
                    <label class="form-label">Khoa <span class="text-danger">*</span></label>
                    <select class="form-select" id="MaKhoa" name="MaKhoa" required>
                        <option value="">-- Chọn khoa --</option>
                        @foreach (var khoa in Model.KhoaOptions)
                        {
                            <option value="@khoa.Value">@khoa.Text</option>
                        }
                    </select>
                    <div class="invalid-feedback" id="maKhoaError"></div>
</div>

                <div class="mb-3">
                    <label class="form-label">Họ tên <span class="text-danger">*</span></label>
                    <input class="form-control" id="HoTenGv" name="HoTenGv" required />
                    <div class="invalid-feedback" id="hoTenGvError"></div>
                    </div>

                <div class="mb-3">
                    <label class="form-label">Lương</label>
                    <input class="form-control" type="number" id="Luong" name="Luong" min="0" step="1" />
                    <div class="invalid-feedback" id="luongError"></div>
                    </div>

                <div class="d-flex justify-content-end gap-2 mt-4">
                    <button type="button" class="btn btn-outline-secondary" onclick="closeGiangVienDialog()">Huỷ</button>
                    <button type="button" id="btnSaveGiangVien" class="btn btn-primary">Lưu</button>
                    </div>
                </form>
        </e-content-template>
    </ejs-dialog>

    @* SyncFusion Dialog for Delete Confirmation *@
    <ejs-dialog id="deleteGvDialog" header="Xác nhận xoá" isModal="true" showCloseIcon="true" width="400px" height="auto" animationSettings="new Syncfusion.EJ2.Popups.DialogAnimationSettings { Effect = Syncfusion.EJ2.Popups.DialogEffect.Zoom }" visible="false">
        <e-content-template>
            <div class="text-center">
                <div class="mb-3">
                    <i class="bi bi-exclamation-triangle-fill text-danger" style="font-size: 3rem;"></i>
            </div>
                <p>Bạn chắc chắn muốn xoá giảng viên <strong id="delete-name"></strong>?</p>
                <div class="small text-muted">Thao tác này không thể hoàn tác.</div>

                <div class="d-flex justify-content-center gap-2 mt-4">
                    <button type="button" class="btn btn-outline-secondary" onclick="closeDeleteDialog()">Huỷ</button>
                    <button type="button" id="btnConfirmDelete" class="btn btn-danger">Xoá</button>
        </div>
    </div>
        </e-content-template>
    </ejs-dialog>

    @* SyncFusion Dialog for Import *@
    <ejs-dialog id="importGvDialog" header="Import Giảng viên từ Excel" isModal="true" showCloseIcon="true" width="600px" height="auto" animationSettings="new Syncfusion.EJ2.Popups.DialogAnimationSettings { Effect = Syncfusion.EJ2.Popups.DialogEffect.Zoom }" visible="false">
        <e-content-template>
            <form method="post" enctype="multipart/form-data" asp-action="Import" id="importForm">
                @Html.AntiForgeryToken()

                <div class="mb-4">
                    <label class="form-label">File Excel mẫu</label>
                    <p class="text-muted small mb-2">
                        File Excel phải có các cột sau:
                    </p>
                    <div class="table-responsive">
                        <table class="table table-sm table-bordered mb-2">
                            <thead class="table-light">
                                <tr>
                                    <th>STT</th>
                                    <th>Mã giảng viên</th>
                                    <th>Họ và tên</th>
                                    <th>Lương</th>
                                    <th>Mã khoa</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>1</td>
                                    <td>1001</td>
                                    <td>Nguyễn Văn A</td>
                                    <td>15</td>
                                    <td>CNTT</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="text-muted small mb-2">
                        <i class="bi bi-info-circle me-1"></i>
                        Lưu ý: Dòng đầu tiên sẽ được coi là tiêu đề và sẽ bị bỏ qua khi import
                    </div>
                    <div class="d-grid mb-4">
                        <a href="@Url.Action("DownloadTemplate")" class="btn btn-outline-primary btn-sm">
                            <i class="bi bi-download me-1"></i> Tải biểu mẫu Excel
                        </a>
                    </div>
                </div>

                <div class="mb-4">
                    <label class="form-label required">Chọn file Excel</label>
                    <input type="file" class="form-control" name="ExcelFile" accept=".xlsx" required />
                    <div class="invalid-feedback">Vui lòng chọn file Excel</div>
                    <div class="form-text">
                        <i class="bi bi-info-circle me-1"></i>
                        Chỉ chấp nhận file Excel (.xlsx) và dung lượng tối đa 10MB
                    </div>
                </div>

                <div class="d-flex justify-content-end gap-2">
                    <button type="button" class="btn btn-outline-secondary" onclick="closeImportDialog()">Huỷ</button>
                    <button type="submit" form="importForm" class="btn btn-primary">
                        <i class="bi bi-file-earmark-excel me-1"></i> Import
                    </button>
                </div>
            </form>
        </e-content-template>
    </ejs-dialog>

    @* SyncFusion Dialog for Export *@
    <ejs-dialog id="exportGvDialog" header="Export danh sách giảng viên" isModal="true" showCloseIcon="true" width="700px" height="auto" animationSettings="new Syncfusion.EJ2.Popups.DialogAnimationSettings { Effect = Syncfusion.EJ2.Popups.DialogEffect.Zoom }" visible="false">
        <e-content-template>
            <form method="post" asp-action="Export" id="exportForm">
                @Html.AntiForgeryToken()

                @* Hidden inputs để truyền filter hiện tại *@
                <input type="hidden" name="Filter.Keyword" value="@Model?.Filter?.Keyword" />
                <input type="hidden" name="Filter.MaKhoa" value="@Model?.Filter?.MaKhoa" />
                <input type="hidden" name="Filter.LuongMin" value="@Model?.Filter?.LuongMin" />
                <input type="hidden" name="Filter.LuongMax" value="@Model?.Filter?.LuongMax" />

                <div class="alert alert-info mb-4">
                    <i class="bi bi-info-circle me-1"></i>
                    <strong>Cấu hình xuất dữ liệu</strong><br/>
                    Tệp Excel sẽ bao gồm các cột bạn chọn và dữ liệu theo bộ lọc hiện tại.
                    <div id="filterInfo">
                        <!-- Filter info sẽ được hiển thị bằng JavaScript -->
                    </div>
                </div>

                <div class="mb-3">
                    <label class="form-label fw-bold">Chọn và sắp xếp thứ tự các cột:</label>
                    <div class="small text-muted mb-2">
                        <i class="bi bi-info-circle me-1"></i>
                        Kéo thả để sắp xếp thứ tự hiển thị. Bỏ chọn để ẩn cột.
                    </div>
                </div>

                <div class="row">
                    <div class="col-12">
                        <div id="sortableColumns" class="sortable-columns">
                            <div class="column-item" data-column="ExportMaGv">
                                <div class="column-handle">
                                    <i class="bi bi-grip-vertical"></i>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="ExportMaGv" value="true" id="exportMaGv" checked>
                                    <input type="hidden" name="ExportMaGv" value="false">
                                    <label class="form-check-label" for="exportMaGv">
                                        <i class="bi bi-person-badge me-1"></i> Mã giảng viên
                                    </label>
                                </div>
                            </div>

                            <div class="column-item" data-column="ExportHoTenGv">
                                <div class="column-handle">
                                    <i class="bi bi-grip-vertical"></i>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="ExportHoTenGv" value="true" id="exportHoTenGv" checked>
                                    <input type="hidden" name="ExportHoTenGv" value="false">
                                    <label class="form-check-label" for="exportHoTenGv">
                                        <i class="bi bi-person me-1"></i> Họ và tên
                                    </label>
                                </div>
                            </div>

                            <div class="column-item" data-column="ExportTenKhoa">
                                <div class="column-handle">
                                    <i class="bi bi-grip-vertical"></i>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="ExportTenKhoa" value="true" id="exportTenKhoa" checked>
                                    <input type="hidden" name="ExportTenKhoa" value="false">
                                    <label class="form-check-label" for="exportTenKhoa">
                                        <i class="bi bi-buildings me-1"></i> Tên khoa
                                    </label>
                                </div>
                            </div>

                            <div class="column-item" data-column="ExportLuong">
                                <div class="column-handle">
                                    <i class="bi bi-grip-vertical"></i>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="ExportLuong" value="true" id="exportLuong" checked>
                                    <input type="hidden" name="ExportLuong" value="false">
                                    <label class="form-check-label" for="exportLuong">
                                        <i class="bi bi-currency-dollar me-1"></i> Lương
                                    </label>
                                </div>
                            </div>

                            <div class="column-item" data-column="ExportMaKhoa">
                                <div class="column-handle">
                                    <i class="bi bi-grip-vertical"></i>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="ExportMaKhoa" value="true" id="exportMaKhoa">
                                    <input type="hidden" name="ExportMaKhoa" value="false">
                                    <label class="form-check-label" for="exportMaKhoa">
                                        <i class="bi bi-building me-1"></i> Mã khoa
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="d-flex justify-content-between align-items-center mt-4">
                    <div>
                        <button type="button" class="btn btn-outline-primary btn-sm me-2" id="selectAllFields">
                            <i class="bi bi-check-all me-1"></i> Chọn tất cả
                        </button>
                        <button type="button" class="btn btn-outline-secondary btn-sm" id="clearAllFields">
                            <i class="bi bi-x-square me-1"></i> Bỏ chọn tất cả
                        </button>
                    </div>

                    <div class="d-flex gap-2">
                        <button type="button" class="btn btn-outline-secondary" onclick="closeExportDialog()">Huỷ</button>
                        <button type="submit" form="exportForm" class="btn btn-success">
                            <i class="bi bi-file-earmark-spreadsheet me-1"></i> Export Excel
                        </button>
                    </div>
                </div>
            </form>
        </e-content-template>
    </ejs-dialog>
    </div>

    @section Scripts{
    <link href="~/css/searchable-dropdown.css" rel="stylesheet" />
    <script src="~/js/searchable-dropdown.js"></script>
    <style>
        /* Toast styles */
        .e-toast-container {
            z-index: 9999 !important;
        }

        .e-toast {
            z-index: 9999 !important;
        }

        /* Fix scroll issue */
        body {
            overflow: auto !important;
            height: auto !important;
        }

        html {
            overflow: auto !important;
        }

        /* Đảm bảo modal hoạt động */
        .modal {
            z-index: 1055 !important;
        }

        .modal-backdrop {
            z-index: 1050 !important;
        }

        /* Toast animations */
        @@keyframes slideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        @@keyframes slideOut {
            from {
                transform: translateX(0);
                opacity: 1;
            }
            to {
                transform: translateX(100%);
                opacity: 0;
            }
        }

        /* Đảm bảo toast không tạo backdrop */
        #toast-container {
            pointer-events: none !important;
        }

        #toast-container > div {
            pointer-events: auto !important;
        }

        /* Toast icons */
        @* @@font-face {
            font-family: 'Toast_icons';
            src: url(data:application/x-font-ttf;charset=utf-8;base64,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) format('truetype');
            font-weight: normal;
            font-style: normal;
        } *@

        .toast-icons {
            font-family: 'Toast_icons' !important;
            speak: none;
            font-size: 55px;
            font-style: normal;
            font-weight: normal;
            font-variant: normal;
            text-transform: none;
            line-height: 1;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        #toast_type .e-toast-icon.e-icons {
            height: auto;
            font-size: 30px;
        }

        .toast-icons.e-success::before {
            content: "\e701";
        }

        .toast-icons.e-error::before {
            content: "\e700";
        }

        .toast-icons.e-info::before {
            content: "\e704";
        }

        .toast-icons.e-warning::before {
            content: "\e703";
        }

        /* Grid responsive styles */
        .grid-responsive {
            overflow-x: auto;
            width: 100%;
        }

        .grid-container {
            min-width: 100%;
        }

        /* Ma GV styling - removed custom styling to use default black color */

        /* Bootstrap icons in toast */
        .e-toast .bi {
            font-size: 1.2em;
            margin-right: 8px;
        }

        .e-toast-icon {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* Sortable columns styles for export modal */
        .sortable-columns { display: flex; flex-direction: column; gap: 8px; }
        .column-item { background: #f8f9fa; border: 1px solid #e9ecef; border-radius: 6px; padding: 8px 12px; display: flex; align-items: center; justify-content: flex-start; gap: 10px; }
        .column-item.dragging { opacity: 0.6; }
        .column-handle { cursor: grab; margin-right: 2px; color: #6c757d; }
        .column-handle i { pointer-events: none; }
        .column-item .form-check { display: flex; align-items: center; gap: 8px; margin: 0; }

        /* Ensure dialogs use Bootstrap body font (match rest of site) */
        #exportGvDialog .e-dlg-header-content,
        #exportGvDialog .e-dlg-content,
        #exportGvDialog .e-footer-content,
        #exportGvDialog label,
        #exportGvDialog input,
        #exportGvDialog .form-check-label,
        #exportGvDialog .btn,
        #exportGvDialog .column-item,
        #importGvDialog .e-dlg-header-content,
        #importGvDialog .e-dlg-content,
        #importGvDialog .e-footer-content,
        #importGvDialog label,
        #importGvDialog input,
        #importGvDialog .form-check-label,
        #importGvDialog .btn {
            font-family: var(--bs-body-font-family, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", "Liberation Sans", sans-serif) !important;
            font-size: var(--bs-body-font-size, 1rem);
            line-height: var(--bs-body-line-height, 1.5);
        }
    </style>

        <script>
        // Initialize searchable dropdown for Khoa filter
        document.addEventListener('DOMContentLoaded', function () {
            try {
                new SearchableDropdown('khoaDropdown', {
                    placeholder: 'Tìm kiếm khoa...',
                    searchUrl: '@Url.Action("Search", "Khoa")',
                    searchParam: 'q',
                    debounceDelay: 300,
                    allowClear: true
                });
            } catch (e) {
                console.error('Failed to init Khoa searchable dropdown:', e);
            }
        });
        var toastObj;
        var giangVienDialog;
        var deleteGvDialog;
        var importGvDialog;
        var exportGvDialog;
        var toasts = [
            { title: 'Warning!', content: 'There was a problem with your network connection.', cssClass: 'e-toast-warning', icon: 'bi bi-exclamation-triangle text-warning' },
            { title: 'Success!', content: 'Your message has been sent successfully.', cssClass: 'e-toast-success', icon: 'bi bi-check-circle text-success' },
            { title: 'Error!', content: 'A problem has been occurred while submitting your data.', cssClass: 'e-toast-danger', icon: 'bi bi-x-circle text-danger' },
            { title: 'Information!', content: 'Please read the comments carefully.', cssClass: 'e-toast-info', icon: 'bi bi-info-circle text-info' }
        ];

        // Function to show toast notification
        function showMessage(content, severity = 'Info') {
            if (!toastObj) return;

            let toastIndex;
            switch(severity) {
                case 'Success':
                    toastIndex = 1;
                    toasts[toastIndex].content = content;
                    break;
                case 'Error':
                    toastIndex = 2;
                    toasts[toastIndex].content = content;
                    break;
                case 'Warning':
                    toastIndex = 0;
                    toasts[toastIndex].content = content;
                    break;
                default:
                    toastIndex = 3;
                    toasts[toastIndex].content = content;
                    break;
            }

            toastObj.show(toasts[toastIndex]);
        }

        // Initialize searchable dropdown for Khoa in filter form
        document.addEventListener('DOMContentLoaded', function () {
            try {
                new SearchableDropdown('khoaDropdown', {
                    placeholder: 'Tìm kiếm khoa...',
                    searchUrl: '@Url.Action("Search", "Khoa")',
                    searchParam: 'q',
                    debounceDelay: 300,
                    allowClear: true
                });
            } catch {}
        });

        // Initialize SyncFusion Dialogs
        function initializeDialogs() {
            // Initialize GiangVien Dialog
            giangVienDialog = document.getElementById('giangVienDialog').ej2_instances[0];

            // Initialize Delete Dialog
            deleteGvDialog = document.getElementById('deleteGvDialog').ej2_instances[0];

            // Initialize Import Dialog
            importGvDialog = document.getElementById('importGvDialog').ej2_instances[0];

            // Initialize Export Dialog
            exportGvDialog = document.getElementById('exportGvDialog').ej2_instances[0];
        }

        // Show Add GiangVien Dialog
        function showAddGiangVienDialog() {
            if (!giangVienDialog) initializeDialogs();

            document.getElementById('giangVienForm').reset();
            document.getElementById('MaGv').value = '';
            document.getElementById('giangVienDialog').ej2_instances[0].header = 'Thêm Giảng viên';
            document.getElementById('btnSaveGiangVien').textContent = 'Lưu';
            clearValidationErrors();

            giangVienDialog.show();
        }

        // Show Edit GiangVien Dialog
        window.editGiangVien = function(maGv) {
            if (!giangVienDialog) initializeDialogs();

            // Load giang vien data
            loadGiangVienData(maGv);
            document.getElementById('giangVienDialog').ej2_instances[0].header = 'Sửa Giảng viên';
            document.getElementById('btnSaveGiangVien').textContent = 'Cập nhật';

            giangVienDialog.show();
        };

        // Show Delete Confirmation Dialog from button click
        function showDeleteDialogFromButton(button) {
            const maGv = button.getAttribute('data-id');
            const name = button.getAttribute('data-name');

            if (!deleteGvDialog) initializeDialogs();

            document.getElementById('delete-name').textContent = name;
            // Store the ID for later use in confirmDelete
            if (deleteGvDialog) {
                deleteGvDialog.dataId = maGv;
            }
            deleteGvDialog.show();
        }

        // Show Delete Confirmation Dialog (legacy function for backward compatibility)
        function showDeleteDialog(maGv, name) {
            if (!deleteGvDialog) initializeDialogs();

            document.getElementById('delete-name').textContent = name;
            // Store the ID for later use in confirmDelete
            if (deleteGvDialog) {
                deleteGvDialog.dataId = maGv;
            }
            deleteGvDialog.show();
        }

        // Close GiangVien Dialog
        function closeGiangVienDialog() {
            if (giangVienDialog) {
                giangVienDialog.hide();
            }
        }

        // Close Delete Dialog
        function closeDeleteDialog() {
            if (deleteGvDialog) {
                deleteGvDialog.hide();
            }
        }

        // Show Import Dialog
        function showImportDialog() {
            if (!importGvDialog) initializeDialogs();
            importGvDialog.show();
        }

        // Close Import Dialog
        function closeImportDialog() {
            if (importGvDialog) {
                importGvDialog.hide();
            }
        }

        // Show Export Dialog
        function showExportDialog() {
            if (!exportGvDialog) initializeDialogs();

            // Update filter info in export dialog
            updateExportFilterInfo();

            exportGvDialog.show();
        }

        // Close Export Dialog
        function closeExportDialog() {
            if (exportGvDialog) {
                exportGvDialog.hide();
            }
        }

        // Update filter information in export dialog
        function updateExportFilterInfo() {
            const filterInfo = document.getElementById('filterInfo');
            if (!filterInfo) return;

            const keyword = document.querySelector('input[name="Filter.Keyword"]')?.value;
            const maKhoa = document.querySelector('select[name="Filter.MaKhoa"]')?.value;
            const luongMin = document.querySelector('input[name="Filter.LuongMin"]')?.value;
            const luongMax = document.querySelector('input[name="Filter.LuongMax"]')?.value;

            const selectedKhoaText = maKhoa ? document.querySelector(`select[name="Filter.MaKhoa"] option[value="${maKhoa}"]`)?.textContent : '';

            let filterText = '';
            if (keyword || maKhoa || luongMin || luongMax) {
                filterText = '<br/><strong>Bộ lọc đang áp dụng</strong>';

                if (keyword) {
                    filterText += '<br/>- Từ khóa: ' + keyword;
                }

                if (maKhoa && selectedKhoaText) {
                    filterText += '<br/>- Khoa: ' + selectedKhoaText;
                }

                if (luongMin || luongMax) {
                    filterText += '<br/>- Lương: ';
                    if (luongMin && luongMax) {
                        filterText += 'từ ' + parseInt(luongMin).toLocaleString() + ' đến ' + parseInt(luongMax).toLocaleString();
                    } else if (luongMin) {
                        filterText += 'từ ' + parseInt(luongMin).toLocaleString() + ' trở lên';
                    } else if (luongMax) {
                        filterText += 'đến ' + parseInt(luongMax).toLocaleString() + ' trở xuống';
                    }
                }
            } else {
                filterText = '<br/><em>Không áp dụng bộ lọc</em>: xuất toàn bộ giảng viên.';
            }

            filterInfo.innerHTML = filterText;
        }

        document.addEventListener('DOMContentLoaded', function () {
            // Initialize dialogs when DOM is loaded
            setTimeout(function() {
                initializeDialogs();

                // Handle Save button click
                document.getElementById('btnSaveGiangVien').addEventListener('click', function() {
                    saveGiangVien();
                });

                // Handle Delete confirmation
                document.getElementById('btnConfirmDelete').addEventListener('click', function() {
                    confirmDelete();
                });
            }, 100);
        });

        // Function to reset form
        function resetForm() {
            document.getElementById('giangVienForm').reset();
            document.getElementById('MaGv').value = '';
            clearValidationErrors();
        }

        // Function to clear validation errors
        function clearValidationErrors() {
            const errorElements = document.querySelectorAll('.invalid-feedback');
            errorElements.forEach(el => el.textContent = '');

            const formControls = document.querySelectorAll('.form-control, .form-select');
            formControls.forEach(el => el.classList.remove('is-invalid'));
        }

        // Function to load giang vien data for editing
        function loadGiangVienData(maGv) {
            fetch(`/GiangVien/GetById/${maGv}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        document.getElementById('MaGv').value = data.data.magv;
                        document.getElementById('MaKhoa').value = data.data.maKhoa || '';
                        document.getElementById('HoTenGv').value = data.data.hotengv || '';
                        document.getElementById('Luong').value = data.data.luong || '';
                    } else {
                        showMessage('Không thể tải dữ liệu giảng viên', 'Error');
                    }
                })
                .catch(error => {
                    console.error('Error loading giang vien data:', error);
                    showMessage('Có lỗi xảy ra khi tải dữ liệu', 'Error');
                });
        }

        // Function to normalize name (capitalize first letter of each word)
        function normalizeName(name) {
            if (!name || typeof name !== 'string') return '';

            return name.trim()
                .split(' ')
                .filter(word => word.length > 0)
                .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
                .join(' ');
        }

        // Function to save giang vien
        function saveGiangVien() {
                clearValidationErrors();

                // Chuẩn hóa dữ liệu đầu vào
                const hoTenInput = document.getElementById('HoTenGv');
                if (hoTenInput.value) {
                    hoTenInput.value = normalizeName(hoTenInput.value);
                }

                const formData = new FormData(document.getElementById('giangVienForm'));
                const maGv = document.getElementById('MaGv').value;
                const isEdit = maGv && maGv !== '';

                // For create mode, remove MaGv from form data since it will be auto-generated
                if (!isEdit) {
                    formData.delete('MaGv');
                }

                const url = isEdit ? `/GiangVien/Edit/${maGv}` : '/GiangVien/Create';
                const method = 'POST';

                // Add anti-forgery token
                const token = document.querySelector('input[name="__RequestVerificationToken"]').value;
                formData.append('__RequestVerificationToken', token);

                fetch(url, {
                    method: method,
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        closeGiangVienDialog();
                        const successMessage = isEdit ? 'Cập nhật giảng viên thành công!' : 'Thêm giảng viên thành công!';
                        showMessage(successMessage, 'Success');

                        // Update DataGrid without page reload
                        setTimeout(() => {
                            updateDataGridAfterSave(isEdit, data.data);
                        }, 500);
                    } else {
                        // Handle validation errors
                        if (data.errors) {
                            Object.keys(data.errors).forEach(field => {
                                const errorElement = document.getElementById(field + 'Error');
                                const inputElement = document.getElementById(field);
                                if (errorElement && inputElement) {
                                    errorElement.textContent = data.errors[field][0];
                                    inputElement.classList.add('is-invalid');
                                }
                            });
                        } else {
                            showMessage(data.message || 'Có lỗi xảy ra', 'Error');
                        }
                    }
                })
                .catch(error => {
                    console.error('Error saving giang vien:', error);
                    showMessage('Có lỗi xảy ra khi lưu dữ liệu', 'Error');
                });
            }

        // Function to update DataGrid after save/edit
        function updateDataGridAfterSave(isEdit, savedData) {
            const grid = document.getElementById('GiangVienGrid').ej2_instances[0];
            if (!grid || !savedData) return;

            if (isEdit) {
                // Update existing row
                const dataSource = grid.dataSource;
                const index = dataSource.findIndex(item => item.Magv == savedData.magv);
                if (index !== -1) {
                    // Update the row data with correct field names
                    dataSource[index] = {
                        ...dataSource[index],
                        Hotengv: savedData.hotengv,
                        MaKhoa: savedData.maKhoa,
                        Luong: savedData.luong
                    };
                    grid.refresh();
                }
            } else {
                // Add new row - cần lấy thông tin khoa từ khoa options
                const khoaSelect = document.getElementById('MaKhoa');
                const selectedKhoaText = khoaSelect.options[khoaSelect.selectedIndex]?.text || '';

                const newRow = {
                    Magv: savedData.magv,
                    Hotengv: savedData.hotengv,
                    MaKhoa: savedData.maKhoa,
                    TenKhoa: selectedKhoaText,
                    Luong: savedData.luong
                };

                // Thêm vào dataSource
                grid.dataSource.push(newRow);
                grid.refresh();

                // Sort lại theo Magv để giữ thứ tự
                grid.sortColumn('Magv', 'Ascending');
            }
        }

        // Function to confirm delete
        function confirmDelete() {
            if (!deleteGvDialog) {
                showMessage('Không thể thực hiện thao tác xóa', 'Error');
                return;
            }

            const id = deleteGvDialog.dataId;

            if (!id) {
                showMessage('Không tìm thấy ID giảng viên', 'Error');
                return;
            }

            fetch(`/GiangVien/Delete/${id}`, {
                method: 'POST',
                body: new FormData(),
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]').value
                }
            })
            .then(response => response.json())
            .then(data => {
                closeDeleteDialog();

                if (data.success) {
                    showMessage(data.message || 'Đã xóa giảng viên thành công!', 'Success');

                    // Xóa row khỏi DataGrid mà không reload trang
                    setTimeout(() => {
                        const grid = document.getElementById('GiangVienGrid').ej2_instances[0];
                        if (grid) {
                            // Tìm và xóa row có ID tương ứng
                            const rowToDelete = grid.dataSource.find(item => item.Magv == id);
                            if (rowToDelete) {
                                // Lưu trang hiện tại
                                const currentPage = grid.pageSettings.currentPage;
                                const pageSize = grid.pageSettings.pageSize;

                                // Xóa row khỏi dataSource
                                const index = grid.dataSource.indexOf(rowToDelete);
                                grid.dataSource.splice(index, 1);

                                // Refresh grid
                                grid.refresh();

                                // Tính toán trang mới
                                const totalPages = Math.ceil(grid.dataSource.length / pageSize);
                                const newPage = Math.min(currentPage, totalPages || 1);

                                // Nếu trang hiện tại trống và không phải trang 1, quay về trang trước
                                if (currentPage > totalPages && totalPages > 0) {
                                    grid.goToPage(totalPages);
                                } else if (newPage > 0) {
                                    grid.goToPage(newPage);
                                }
                            }
                        }
                    }, 1500);
                } else {
                    showMessage(data.message || 'Có lỗi xảy ra khi xóa giảng viên', 'Error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                closeDeleteDialog();
                showMessage('Có lỗi xảy ra khi xóa giảng viên', 'Error');
            });
        }

        // Xử lý form import
        document.getElementById('importForm')?.addEventListener('submit', function(e) {
            e.preventDefault(); // Ngăn submit mặc định

            const fileInput = this.querySelector('input[type="file"]');
            if (!fileInput.files.length) {
                fileInput.classList.add('is-invalid');
                showMessage('Vui lòng chọn file Excel để import!', 'Warning');
                return;
            }

            fileInput.classList.remove('is-invalid');
            const form = this;
            const formData = new FormData(form);

            showMessage('Đang xử lý file Excel...', 'Info');

            fetch(form.action, {
                method: 'POST',
                body: formData,
                headers: {
                    'RequestVerificationToken': document.querySelector('#importForm input[name="__RequestVerificationToken"]').value,
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                const createdCount = data.createdCount || 0;
                const errors = Array.isArray(data.errors) ? data.errors : [];
                const errorsFileUrl = data.errorsFileUrl || null;
                const errorsFileBase64 = data.errorsFileBase64 || null;
                const errorsFileName = data.errorsFileName || 'Import_GV_Errors.xlsx';

                // Hiển thị toast thành công nếu có ít nhất một bản ghi được tạo
                if (createdCount > 0) {
                    showMessage(`Import thành công ${createdCount} giảng viên.`, 'Success');
                }

                // Hiển thị từng lỗi thành toast riêng
                if (errors.length > 0) {
                    errors.forEach(err => showMessage(err, 'Error'));
                }

                // Cho phép tải về danh sách lỗi nếu có
                if (errorsFileBase64) {
                    showMessage('Có danh sách lỗi. Đang tải file các dòng lỗi...', 'Info');
                    try {
                        const linkSource = `data:application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;base64,${errorsFileBase64}`;
                        const a = document.createElement('a');
                        a.href = linkSource;
                        a.download = errorsFileName;
                        document.body.appendChild(a);
                        a.click();
                        document.body.removeChild(a);
                    } catch {}
                } else if (errorsFileUrl) {
                    // Fallback (shouldn't be used now)
                    try {
                        const a = document.createElement('a');
                        a.href = errorsFileUrl;
                        a.download = errorsFileName;
                        document.body.appendChild(a);
                        a.click();
                        document.body.removeChild(a);
                    } catch {}
                }

                // Reset form sau khi xử lý
                form.reset();
                fileInput.classList.remove('is-invalid');

                // Chỉ reload khi có tạo mới
                if (createdCount > 0) {
                    closeImportDialog();
                    setTimeout(() => { window.location.reload(); }, 2000);
                }
            })
            .catch(error => {
                console.error('Import error:', error);
                showMessage('Lỗi khi import file: ' + error.message, 'Error');
            });
        });

        // Xử lý export form
        document.getElementById('selectAllFields')?.addEventListener('click', function() {
            const checkboxes = document.querySelectorAll('#exportForm input[type="checkbox"]');
            checkboxes.forEach(cb => cb.checked = true);
        });

        document.getElementById('clearAllFields')?.addEventListener('click', function() {
            const checkboxes = document.querySelectorAll('#exportForm input[type="checkbox"]');
            checkboxes.forEach(cb => cb.checked = false);
        });

        // Validation cho export form + đính kèm columnOrder
        document.getElementById('exportForm')?.addEventListener('submit', function(e) {
            const checkboxes = document.querySelectorAll('#exportForm input[type="checkbox"]:checked');
            if (checkboxes.length === 0) {
                e.preventDefault();
                showMessage('Vui lòng chọn ít nhất một trường để export!', 'Warning');
                return;
            }

            // Lưu thứ tự cột trước khi submit
            (function updateColumnOrder(){
                const columnItems = document.querySelectorAll('#sortableColumns .column-item');
                const columnOrder = Array.from(columnItems).map(item => item.getAttribute('data-column'));
                let orderInput = document.getElementById('columnOrder');
                if (!orderInput) {
                    orderInput = document.createElement('input');
                    orderInput.type = 'hidden';
                    orderInput.name = 'columnOrder';
                    orderInput.id = 'columnOrder';
                    document.getElementById('exportForm').appendChild(orderInput);
                }
                orderInput.value = columnOrder.join(',');
            })();
        });

        // Simple drag-and-drop sorting for export columns
        (function initSortableColumns() {
            const container = document.getElementById('sortableColumns');
            if (!container) return;

            let draggingEl = null;

            container.querySelectorAll('.column-item').forEach(item => {
                item.setAttribute('draggable', 'true');
                item.addEventListener('dragstart', function (e) {
                    draggingEl = this;
                    this.classList.add('dragging');
                    e.dataTransfer.effectAllowed = 'move';
                    try { e.dataTransfer.setData('text/plain', this.getAttribute('data-column') || ''); } catch {}
                });
                item.addEventListener('dragend', function () {
                    this.classList.remove('dragging');
                    draggingEl = null;
                });
            });

            container.addEventListener('dragover', function (e) {
                if (!draggingEl) return;
                e.preventDefault();
                const afterElement = getDragAfterElement(container, e.clientY);
                if (afterElement == null) {
                    container.appendChild(draggingEl);
                } else if (afterElement !== draggingEl) {
                    container.insertBefore(draggingEl, afterElement);
                }
            });

            function getDragAfterElement(container, y) {
                const draggableElements = [...container.querySelectorAll('.column-item:not(.dragging)')];
                return draggableElements.reduce((closest, child) => {
                    const box = child.getBoundingClientRect();
                    const offset = y - box.top - box.height / 2;
                    if (offset < 0 && offset > closest.offset) {
                        return { offset: offset, element: child };
                    } else {
                        return closest;
                    }
                }, { offset: Number.NEGATIVE_INFINITY }).element || null;
            }
        })();
        </script>
    }
