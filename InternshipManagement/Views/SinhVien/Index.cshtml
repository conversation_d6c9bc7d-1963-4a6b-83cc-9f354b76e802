﻿@model InternshipManagement.Models.ViewModels.SinhVienIndexVm
@{
    ViewData["Title"] = "Danh sách Sinh viên";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<div class="card shadow-sm">
    <div class="card-header bg-white d-flex align-items-center justify-content-between">
        <h5 class="mb-0">Danh sách Sinh viên</h5>
        <div class="small text-muted">Tổng: <strong>@Model.Items.Count</strong> bản ghi</div>
    </div>

    <div class="card shadow-sm mb-3">
        <div class="card-body">
            <form method="get" id="filterForm" class="row g-3">
                @Html.AntiForgeryToken()
                <div class="col-md-4">
                    <label class="form-label">Từ khóa</label>
                    <input class="form-control" asp-for="Filter.Keyword" placeholder="Tê<PERSON>, quê quán hoặc mã SV..." />
                </div>
                <div class="col-md-4">
                    <label class="form-label">Khoa</label>
                    <div class="searchable-dropdown" id="khoaDropdown">
                        <input type="hidden" asp-for="Filter.MaKhoa" />
                        <input type="text" class="form-control" placeholder="Tìm kiếm khoa..." autocomplete="off">
                        <i class="bi bi-chevron-down dropdown-arrow"></i>
                        <div class="dropdown-menu">
                            <div class="dropdown-options">
                                <a class="dropdown-item" href="#" data-value="">-- Tất cả --</a>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <label class="form-label">Năm sinh từ</label>
                    <select class="form-select" asp-for="Filter.NamSinhMin" id="FilterNamSinhMin">
                        <option value="">-- Tất cả --</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">Đến</label>
                    <select class="form-select" asp-for="Filter.NamSinhMax" id="FilterNamSinhMax">
                        <option value="">-- Tất cả --</option>
                    </select>
                </div>

                <div class="col-md-12 d-flex justify-content-end gap-2">
                    <button type="submit" form="filterForm" class="btn btn-primary d-flex align-items-center px-3 py-2">
                        <i class="bi bi-funnel me-1"></i> Lọc
                    </button>
                    <a asp-action="Index" class="btn btn-outline-secondary d-flex align-items-center px-3 py-2">
                        <i class="bi-arrow-clockwise me-1"></i> Hủy
                    </a>
                    <button type="button" id="btnAddSinhVien" class="btn btn-success d-flex align-items-center px-3 py-2">
                        <i class="bi bi-person-fill-add me-1"></i> Thêm SV
                    </button>
                    <button type="button" class="btn btn-primary d-flex align-items-center px-3 py-2" data-bs-toggle="modal" data-bs-target="#importModal">
                        <i class="bi bi-file-earmark-excel me-1"></i> Import Excel
                    </button>
                    <button type="button" class="btn btn-success d-flex align-items-center px-3 py-2" data-bs-toggle="modal" data-bs-target="#exportModal">
                        <i class="bi bi-file-earmark-spreadsheet me-1"></i> Export Excel
                    </button>
                </div>
            </form>
        </div>
    </div>

    @* Syncfusion Toast *@
    <ejs-toast id="toast_type" close="onclose" beforeOpen="onBeforeOpen" created="created" timeOut="10000">
        <e-toast-position X="Right" Y="Top"></e-toast-position>
    </ejs-toast>

    @* Server messages bridge for toasts *@
    <div id="serverMessages"
         data-success="@TempData["Success"]"
         data-warning="@TempData["Warning"]"
         data-error="@TempData["Error"]"
         style="display:none;"></div>

    <script>
        // Toast functions must be defined BEFORE the tag helper renders
        var toastObj;
        
        window.created = function() {
            toastObj = this;
            toastObj.target = document.body;
            toastObj.position = { X: 'Right', Y: 'Top' };
            toastObj.timeOut = 10000;
        };

        window.onclose = function(e) {
            // Toast closed
        };

        window.onBeforeOpen = function() {
            // Toast opening
        };
    </script>


    <!-- Responsive Grid Container -->
    <div class="grid-responsive">
        <div class="grid-container">
            <ejs-grid id="SinhVienGrid" dataSource="@Model.Items" allowPaging="true" allowSorting="true" allowMultiSorting="true" allowFiltering="false" allowResizing="true" gridLines="Both" height="600" width="100%" grid-lines="Both" enable-hover="false" enable-alt-row="true">
                <e-grid-pagesettings pageSize="20" pageSizes="new List<int>{10,20,50,100,500}"></e-grid-pagesettings> 
                <e-grid-columns>
                    <e-grid-column field="Masv" headerText="Mã SV" width="100" template="#maSvTemplate"></e-grid-column>
                    <e-grid-column field="Hotensv" headerText="Họ tên" width="250"></e-grid-column>
                    <e-grid-column headerText="Khoa" width="150" template="#khoaTemplate"></e-grid-column>
                    <e-grid-column field="NamSinh" headerText="Năm sinh" width="120" textAlign="Center"></e-grid-column>
                    <e-grid-column field="QueQuan" headerText="Quê quán" width="200"></e-grid-column>
                    <e-grid-column headerText="Hành động" width="150" textAlign="Center" template="#actionTemplate"></e-grid-column>
                </e-grid-columns>
            </ejs-grid>
        </div>
    </div>

    @* Templates for custom column rendering *@
    <script id="maSvTemplate" type="text/x-template">
        <span class="ma-sv-code">${Masv}</span>
    </script>

    <script id="khoaTemplate" type="text/x-template">
        ${TenKhoa}
    </script>

    <script id="actionTemplate" type="text/x-template">
        <div class="d-flex gap-1 justify-content-center">
            <a class="btn btn-sm btn-primary text-white" href="/SinhVien/Details/${Masv}" title="Xem chi tiết">
                <i class="bi bi-eye"></i>
            </a>
            <button type="button" class="btn btn-sm btn-warning text-white" onclick="editSinhVien(${Masv})" title="Sửa">
                <i class="bi bi-pencil-square"></i>
            </button>
            <button type="button" class="btn btn-sm btn-danger" data-bs-toggle="modal" data-bs-target="#deleteSvModal" data-id="${Masv}" data-name="${Hotensv}" title="Xóa">
                <i class="bi bi-trash"></i>
            </button>
        </div>
    </script>
    @* EJ2 Grid component *@
</div>

@* Alert TempData *@

@* Delete Modal *@
<div class="modal fade" id="deleteSvModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content border-0">
            <div class="modal-header">
                <h5 class="modal-title">Xác nhận xoá</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Đóng"></button>
            </div>
            <div class="modal-body">
                Bạn chắc chắn muốn xoá sinh viên <strong id="delete-name"></strong>?
                <div class="small text-muted mt-1">Thao tác này không thể hoàn tác.</div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Huỷ</button>
                <form method="post" asp-action="Delete" asp-controller="SinhVien" class="m-0">
                    @Html.AntiForgeryToken()
                    <input type="hidden" name="id" id="delete-id" />
                    <button type="submit" class="btn btn-danger">Xoá</button>
                </form>
            </div>
        </div>
    </div>
</div>

@* Import Modal *@
<div class="modal fade" id="importModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content border-0">
            <div class="modal-header">
                <h5 class="modal-title">Import Sinh viên từ Excel</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Đóng"></button>
            </div>
            <div class="modal-body">
                <form method="post" enctype="multipart/form-data" asp-action="Import" id="importForm">
                    @Html.AntiForgeryToken()

                    <div class="mb-4">
                        <label class="form-label">File Excel mẫu</label>
                        <p class="text-muted small mb-2">
                            File Excel phải có các cột sau:
                        </p>
                        <div class="table-responsive">
                            <table class="table table-sm table-bordered mb-2">
                                <thead class="table-light">
                                    <tr>
                                        <th>STT</th>
                                        <th>Mã sinh viên</th>
                                        <th>Họ và tên</th>
                                        <th>Năm sinh</th>
                                        <th>Quê quán</th>
                                        <th>Mã khoa</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>1</td>
                                        <td>20</td>
                                        <td>Nguyễn Văn A</td>
                                        <td>2000</td>
                                        <td>Hà Nội</td>
                                        <td>CNTT</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="text-muted small mb-2">
                            <i class="bi bi-info-circle me-1"></i>
                            Lưu ý: Dòng đầu tiên sẽ được coi là tiêu đề và sẽ bị bỏ qua khi import
                        </div>
                        <div class="d-grid">
                            <a href="@Url.Action("DownloadTemplate")" class="btn btn-outline-primary btn-sm">
                                <i class="bi bi-download me-1"></i> Tải biểu mẫu Excel
                            </a>
                        </div>
                    </div>

                    <div class="mb-4">
                        <label class="form-label required">Chọn file Excel</label>
                        <input type="file" class="form-control" name="ExcelFile" accept=".xlsx" required />
                        <div class="invalid-feedback">Vui lòng chọn file Excel</div>
                        <div class="form-text">
                            <i class="bi bi-info-circle me-1"></i>
                            Chỉ chấp nhận file Excel (.xlsx) và dung lượng tối đa 10MB
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Huỷ</button>
                <button type="submit" form="importForm" class="btn btn-primary">
                    <i class="bi bi-file-earmark-excel me-1"></i> Import
                </button>
            </div>
        </div>
    </div>
</div>

@* Export Modal *@
<div class="modal fade" id="exportModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-lg">
        <div class="modal-content border-0">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-file-earmark-spreadsheet me-2"></i>
                    Export danh sách sinh viên
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Đóng"></button>
            </div>
            <div class="modal-body">
                <form method="post" asp-action="Export" id="exportForm">
                    @Html.AntiForgeryToken()

                    @* Hidden inputs để truyền filter hiện tại *@
                    <input type="hidden" name="Filter.Keyword" value="@Model?.Filter?.Keyword" />
                    <input type="hidden" name="Filter.MaKhoa" value="@Model?.Filter?.MaKhoa" />
                    <input type="hidden" name="Filter.NamSinhMin" value="@Model?.Filter?.NamSinhMin" />
                    <input type="hidden" name="Filter.NamSinhMax" value="@Model?.Filter?.NamSinhMax" />

                    <div class="row">
                        <div class="col-12">
                            <div class="alert alert-info">
                                <i class="bi bi-info-circle me-1"></i>
                                <strong>Cấu hình xuất dữ liệu</strong><br/>
                                Tệp Excel sẽ bao gồm các cột bạn chọn và dữ liệu theo bộ lọc hiện tại.
                                <div id="filterInfo">
                                    <!-- Filter info sẽ được hiển thị bằng JavaScript -->
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-12">
                            <label class="form-label fw-bold">Chọn và sắp xếp thứ tự các cột:</label>
                            <div class="small text-muted mb-2">
                                <i class="bi bi-info-circle me-1"></i>
                                Kéo thả để sắp xếp thứ tự hiển thị. Bỏ chọn để ẩn cột.
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-12">
                            <div id="sortableColumns" class="sortable-columns">
                                <div class="column-item" data-column="ExportMaSv">
                                    <div class="column-handle">
                                        <i class="bi bi-grip-vertical"></i>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="ExportMaSv" value="true" id="exportMaSv" checked>
                                        <input type="hidden" name="ExportMaSv" value="false">
                                        <label class="form-check-label" for="exportMaSv">
                                            <i class="bi bi-person-badge me-1"></i> Mã sinh viên
                                        </label>
                                    </div>
                                </div>

                                <div class="column-item" data-column="ExportHoTenSv">
                                    <div class="column-handle">
                                        <i class="bi bi-grip-vertical"></i>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="ExportHoTenSv" value="true" id="exportHoTenSv" checked>
                                        <input type="hidden" name="ExportHoTenSv" value="false">
                                        <label class="form-check-label" for="exportHoTenSv">
                                            <i class="bi bi-person me-1"></i> Họ và tên
                                        </label>
                                    </div>
                                </div>

                                <div class="column-item" data-column="ExportTenKhoa">
                                    <div class="column-handle">
                                        <i class="bi bi-grip-vertical"></i>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="ExportTenKhoa" value="true" id="exportTenKhoa" checked>
                                        <input type="hidden" name="ExportTenKhoa" value="false">
                                        <label class="form-check-label" for="exportTenKhoa">
                                            <i class="bi bi-buildings me-1"></i> Tên khoa
                                        </label>
                                    </div>
                                </div>

                                <div class="column-item" data-column="ExportNamSinh">
                                    <div class="column-handle">
                                        <i class="bi bi-grip-vertical"></i>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="ExportNamSinh" value="true" id="exportNamSinh" checked>
                                        <input type="hidden" name="ExportNamSinh" value="false">
                                        <label class="form-check-label" for="exportNamSinh">
                                            <i class="bi bi-calendar-event me-1"></i> Năm sinh
                                        </label>
                                    </div>
                                </div>

                                <div class="column-item" data-column="ExportQueQuan">
                                    <div class="column-handle">
                                        <i class="bi bi-grip-vertical"></i>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="ExportQueQuan" value="true" id="exportQueQuan" checked>
                                        <input type="hidden" name="ExportQueQuan" value="false">
                                        <label class="form-check-label" for="exportQueQuan">
                                            <i class="bi bi-geo-alt me-1"></i> Quê quán
                                        </label>
                                    </div>
                                </div>

                                <div class="column-item" data-column="ExportMaKhoa">
                                    <div class="column-handle">
                                        <i class="bi bi-grip-vertical"></i>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="ExportMaKhoa" value="true" id="exportMaKhoa">
                                        <input type="hidden" name="ExportMaKhoa" value="false">
                                        <label class="form-check-label" for="exportMaKhoa">
                                            <i class="bi bi-building me-1"></i> Mã khoa
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-3">
                        <div class="col-12">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <button type="button" class="btn btn-outline-primary btn-sm" id="selectAllFields">
                                        <i class="bi bi-check-all me-1"></i> Chọn tất cả
                                    </button>
                                    <button type="button" class="btn btn-outline-secondary btn-sm ms-2" id="clearAllFields">
                                        <i class="bi bi-x-square me-1"></i> Bỏ chọn tất cả
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Huỷ</button>
                <button type="submit" form="exportForm" class="btn btn-success text-white">
                    <i class="bi bi-file-earmark-spreadsheet me-1"></i> Export Excel
                </button>
            </div>
        </div>
    </div>
</div>

@* Create/Edit Modal using Bootstrap Modal *@
<div class="modal fade" id="sinhVienModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modalTitle">Thêm Sinh viên</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="sinhVienForm">
                    <input type="hidden" id="MaSv" name="MaSv" />
                    
                    <div class="mb-3">
                        <label class="form-label">Khoa <span class="text-danger">*</span></label>
                        <select class="form-select" id="MaKhoa" name="MaKhoa" required>
                            <option value="">-- Chọn khoa --</option>
                            @if (Model?.KhoaOptions != null)
                            {
                                foreach (var khoa in Model.KhoaOptions)
                                {
                                    <option value="@khoa.Value">@khoa.Text</option>
                                }
                            }
                        </select>
                        <div class="invalid-feedback" id="maKhoaError"></div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Họ tên <span class="text-danger">*</span></label>
                        <input class="form-control" id="HoTenSv" name="HoTenSv" required />
                        <div class="invalid-feedback" id="hoTenSvError"></div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Năm sinh <span class="text-danger">*</span></label>
                        <select class="form-select" id="NamSinh" name="NamSinh" required>
                            <option value="">-- Chọn năm sinh --</option>
                        </select>
                        <div class="invalid-feedback" id="namSinhError"></div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Quê quán</label>
                        <input class="form-control" id="QueQuan" name="QueQuan" />
                        <div class="invalid-feedback" id="queQuanError"></div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Huỷ</button>
                <button type="button" id="btnSaveSinhVien" class="btn btn-primary">Lưu</button>
            </div>
        </div>
    </div>
</div>

@section Scripts{
    <link href="~/css/searchable-dropdown.css" rel="stylesheet" />
    <script src="~/js/searchable-dropdown.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>
    <style>
        /* Toast styles */
        .e-toast-container {
            z-index: 9999 !important;
        }
        
        .e-toast {
            z-index: 9999 !important;
        }
        
        /* Fix scroll issue */
        body {
            overflow: auto !important;
            height: auto !important;
        }
        
        html {
            overflow: auto !important;
        }
        
        /* Đảm bảo modal hoạt động */
        .modal {
            z-index: 1055 !important;
        }
        
        .modal-backdrop {
            z-index: 1050 !important;
        }
        
        /* Toast animations */
        @@keyframes slideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }
        
        @@keyframes slideOut {
            from {
                transform: translateX(0);
                opacity: 1;
            }
            to {
                transform: translateX(100%);
                opacity: 0;
            }
        }
        
        /* Đảm bảo toast không tạo backdrop */
        #toast-container {
            pointer-events: none !important;
        }
        
        #toast-container > div {
            pointer-events: auto !important;
        }
        
        /* Toast icons */
        @@font-face {
            font-family: 'Toast_icons';
            src: url(data:application/x-font-ttf;charset=utf-8;base64,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) format('truetype');
            font-weight: normal;
            font-style: normal;
        }

        .toast-icons {
            font-family: 'Toast_icons' !important;
            speak: none;
            font-size: 55px;
            font-style: normal;
            font-weight: normal;
            font-variant: normal;
            text-transform: none;
            line-height: 1;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        #toast_type .e-toast-icon.e-icons {
            height: auto;
            font-size: 30px;
        }

        .toast-icons.e-success::before {
            content: "\e701";
        }

        .toast-icons.e-error::before {
            content: "\e700";
        }

        .toast-icons.e-info::before {
            content: "\e704";
        }

        .toast-icons.e-warning::before {
            content: "\e703";
        }

        /* Sortable columns styles */
        .sortable-columns {
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            padding: 0.5rem;
            background-color: #f8f9fa;
        }
        
        .column-item {
            display: flex;
            align-items: center;
            padding: 0.5rem;
            margin-bottom: 0.25rem;
            background-color: white;
            border: 1px solid #e9ecef;
            border-radius: 0.25rem;
            cursor: move;
            transition: all 0.2s ease;
        }
        
        .column-item:hover {
            background-color: #f8f9fa;
            border-color: #007bff;
        }
        
        .column-item.sortable-ghost {
            opacity: 0.4;
            background-color: #e3f2fd;
        }
        
        .column-item.sortable-chosen {
            background-color: #e3f2fd;
            border-color: #007bff;
            box-shadow: 0 2px 4px rgba(0,123,255,0.2);
        }
        
        .column-handle {
            margin-right: 0.75rem;
            color: #6c757d;
            cursor: grab;
        }
        
        .column-handle:active {
            cursor: grabbing;
        }
        
        .column-item .form-check {
            flex: 1;
            margin: 0;
        }
        
        .column-item .form-check-label {
            cursor: pointer;
            margin: 0;
        }
    </style>

    <script>
        var toastObj;
        var toasts = [
            { title: 'Warning!', content: 'There was a problem with your network connection.', cssClass: 'e-toast-warning', icon: 'e-warning toast-icons' },
            { title: 'Success!', content: 'Your message has been sent successfully.', cssClass: 'e-toast-success', icon: 'e-success toast-icons' },
            { title: 'Error!', content: 'A problem has been occurred while submitting your data.', cssClass: 'e-toast-danger', icon: 'e-error toast-icons' },
            { title: 'Information!', content: 'Please read the comments carefully.', cssClass: 'e-toast-info', icon: 'e-info toast-icons' }
        ];

        // Function to show toast notification
        function showMessage(content, severity = 'Info') {
            if (!toastObj) return;
            
            let toastIndex;
            switch(severity) {
                case 'Success':
                    toastIndex = 1;
                    toasts[toastIndex].content = content;
                    break;
                case 'Error':
                    toastIndex = 2;
                    toasts[toastIndex].content = content;
                    break;
                case 'Warning':
                    toastIndex = 0;
                    toasts[toastIndex].content = content;
                    break;
                default:
                    toastIndex = 3;
                    toasts[toastIndex].content = content;
                    break;
            }
            
            toastObj.show(toasts[toastIndex]);
        }

        // Function to populate year combobox
        function populateYearComboBox() {
            const yearSelect = document.getElementById('NamSinh');
            if (!yearSelect) return;
            
            const currentYear = new Date().getFullYear();
            const startYear = currentYear - 100; // 100 years ago
            const endYear = currentYear - 17;    // 17 years ago (minimum age)
            
            // Clear existing options except the first one
            yearSelect.innerHTML = '<option value="">-- Chọn năm sinh --</option>';
            
            // Add year options from endYear to startYear (newest to oldest)
            for (let year = endYear; year >= startYear; year--) {
                const option = document.createElement('option');
                option.value = year;
                option.textContent = year;
                yearSelect.appendChild(option);
            }
        }

        // Function to populate filter year comboboxes
        function populateFilterYearComboBoxes() {
            const minYearSelect = document.getElementById('FilterNamSinhMin');
            const maxYearSelect = document.getElementById('FilterNamSinhMax');
            
            if (!minYearSelect || !maxYearSelect) return;
            
            // Lưu giá trị hiện tại
            const currentMinValue = minYearSelect.value;
            const currentMaxValue = maxYearSelect.value;
            
            const currentYear = new Date().getFullYear();
            const startYear = currentYear - 100; // 100 years ago
            const endYear = currentYear - 17;    // 17 years ago (minimum age)
            
            // Clear existing options except the first one
            minYearSelect.innerHTML = '<option value="">-- Tất cả --</option>';
            maxYearSelect.innerHTML = '<option value="">-- Tất cả --</option>';
            
            // Add year options from endYear to startYear (newest to oldest)
            for (let year = endYear; year >= startYear; year--) {
                const minOption = document.createElement('option');
                minOption.value = year;
                minOption.textContent = year;
                minYearSelect.appendChild(minOption);
                
                const maxOption = document.createElement('option');
                maxOption.value = year;
                maxOption.textContent = year;
                maxYearSelect.appendChild(maxOption);
            }
            
            // Khôi phục giá trị đã chọn
            if (currentMinValue) {
                minYearSelect.value = currentMinValue;
            }
            if (currentMaxValue) {
                maxYearSelect.value = currentMaxValue;
            }
            
            // Add event listener for "Từ" combobox to update "Đến" options
            minYearSelect.addEventListener('change', function() {
                updateMaxYearOptions(this.value);
            });
        }

        // Function to update max year options based on min year selection
        function updateMaxYearOptions(minYear) {
            const maxYearSelect = document.getElementById('FilterNamSinhMax');
            if (!maxYearSelect) return;
            
            // Lưu giá trị hiện tại
            const currentMaxValue = maxYearSelect.value;
            
            const currentYear = new Date().getFullYear();
            const startYear = currentYear - 100;
            const endYear = currentYear - 17;
            
            // Clear existing options except the first one
            maxYearSelect.innerHTML = '<option value="">-- Tất cả --</option>';
            
            if (minYear) {
                // If min year is selected, only show years >= min year
                for (let year = endYear; year >= Math.max(parseInt(minYear), startYear); year--) {
                    const option = document.createElement('option');
                    option.value = year;
                    option.textContent = year;
                    maxYearSelect.appendChild(option);
                }
            } else {
                // If no min year selected, show all years
                for (let year = endYear; year >= startYear; year--) {
                    const option = document.createElement('option');
                    option.value = year;
                    option.textContent = year;
                    maxYearSelect.appendChild(option);
                }
            }
            
            // Khôi phục giá trị đã chọn nếu còn hợp lệ
            if (currentMaxValue) {
                // Kiểm tra xem giá trị có còn trong options không
                const optionExists = Array.from(maxYearSelect.options).some(option => option.value === currentMaxValue);
                if (optionExists) {
                    maxYearSelect.value = currentMaxValue;
                }
            }
        }

        document.addEventListener('DOMContentLoaded', function () {
            // Populate year comboboxes
            populateYearComboBox();
            populateFilterYearComboBoxes();

            // Initialize SearchableDropdown for Khoa
            const khoaDropdown = new SearchableDropdown('khoaDropdown', {
                placeholder: 'Tìm kiếm khoa...',
                searchUrl: '@Url.Action("Search", "Khoa")',
                searchParam: 'q',
                debounceDelay: 300,
                allowClear: true
            });

            // Initialize sortable columns
            const sortableColumns = document.getElementById('sortableColumns');
            if (sortableColumns) {
                const sortable = new Sortable(sortableColumns, {
                    handle: '.column-handle',
                    animation: 150,
                    ghostClass: 'sortable-ghost',
                    chosenClass: 'sortable-chosen',
                    onEnd: function(evt) {
                        // Update column order when drag ends
                        updateColumnOrder();
                    }
                });
            }
            
            // Handle filter form submission
            const filterForm = document.getElementById('filterForm');
            filterForm.addEventListener('submit', function(e) {
                e.preventDefault();

                // Build query string with filter parameters (no paging)
                var formData = new FormData(this);
                var queryParams = new URLSearchParams();

                for (var pair of formData.entries()) {
                    queryParams.append(pair[0], pair[1]);
                }

                window.location.href = '@Url.Action("Index", "SinhVien")?' + queryParams.toString();
            });

        // Gán dữ liệu cho modal xóa khi mở
        document.getElementById('deleteSvModal')
            ?.addEventListener('show.bs.modal', function (e) {
                console.log('Modal đang mở...');
                const btn = e.relatedTarget;
                const id = btn?.getAttribute('data-id');
                const name = btn?.getAttribute('data-name');
                console.log('ID:', id, 'Name:', name);
                document.getElementById('delete-id').value = id || '';
                document.getElementById('delete-name').textContent = name || '';
            });

        // Xử lý form xóa bằng AJAX
        document.querySelector('#deleteSvModal form')?.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const id = formData.get('id');
            
            fetch(`/SinhVien/Delete/${id}`, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                // Đóng modal trước
                const modalElement = document.getElementById('deleteSvModal');
                const modal = bootstrap.Modal.getInstance(modalElement);
                if (modal) {
                    modal.hide();
                }
                
                if (data.success) {
                    // Hiển thị toast thành công
                    showMessage(data.message || 'Đã xóa sinh viên thành công!', 'Success');
                    
                    // Xóa backdrop sau khi hiển thị toast
                    setTimeout(() => {
                        const backdrops = document.querySelectorAll('.modal-backdrop');
                        backdrops.forEach(backdrop => {
                            if (backdrop && backdrop.parentNode) {
                                backdrop.remove();
                            }
                        });
                    }, 500);
                    
                    // Xóa row khỏi DataGrid mà không reload trang
                    setTimeout(() => {
                        const grid = document.getElementById('SinhVienGrid').ej2_instances[0];
                        if (grid) {
                            // Tìm và xóa row có ID tương ứng
                            const rowToDelete = grid.dataSource.find(item => item.Masv == id);
                            if (rowToDelete) {
                                // Lưu trang hiện tại
                                const currentPage = grid.pageSettings.currentPage;
                                const pageSize = grid.pageSettings.pageSize;
                                
                                // Xóa row khỏi dataSource
                                const index = grid.dataSource.indexOf(rowToDelete);
                                grid.dataSource.splice(index, 1);
                                
                                // Refresh grid
                                grid.refresh();
                                
                                // Tính toán trang mới
                                const totalPages = Math.ceil(grid.dataSource.length / pageSize);
                                const newPage = Math.min(currentPage, totalPages || 1);
                                
                                // Nếu trang hiện tại trống và không phải trang 1, quay về trang trước
                                if (currentPage > totalPages && totalPages > 0) {
                                    grid.goToPage(totalPages);
                                } else if (newPage > 0) {
                                    grid.goToPage(newPage);
                                }
                            }
                        }
                    }, 1500);
                } else {
                    // Hiển thị lỗi từ server
                    showMessage(data.message || 'Có lỗi xảy ra khi xóa sinh viên', 'Error');
                    
                    // Xóa backdrop ngay lập tức khi có lỗi
                    setTimeout(() => {
                        const backdrops = document.querySelectorAll('.modal-backdrop');
                        backdrops.forEach(backdrop => {
                            if (backdrop && backdrop.parentNode) {
                                backdrop.remove();
                            }
                        });
                    }, 100);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showMessage('Có lỗi xảy ra khi xóa sinh viên', 'Error');
            });
        });

        // Xử lý form import (AJAX + per-error toasts + download error workbook)
        document.getElementById('importForm')?.addEventListener('submit', function(e) {
            e.preventDefault();

            const fileInput = this.querySelector('input[type="file"]');
            if (!fileInput.files.length) {
                fileInput.classList.add('is-invalid');
                showMessage('Vui lòng chọn file Excel để import!', 'Warning');
                return;
            }

            fileInput.classList.remove('is-invalid');
            const form = this;
            const formData = new FormData(form);

            showMessage('Đang xử lý file Excel...', 'Info');

            fetch(form.action, {
                method: 'POST',
                body: formData,
                headers: {
                    'RequestVerificationToken': document.querySelector('#importForm input[name="__RequestVerificationToken"]').value,
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                const createdCount = data.createdCount || 0;
                const errors = Array.isArray(data.errors) ? data.errors : [];
                const errorsFileUrl = data.errorsFileUrl || null;
                const errorsFileBase64 = data.errorsFileBase64 || null;
                const errorsFileName = data.errorsFileName || 'Import_SV_Errors.xlsx';

                if (createdCount > 0) {
                    showMessage(`Import thành công ${createdCount} sinh viên.`, 'Success');
                }

                if (errors.length > 0) {
                    errors.forEach(err => showMessage(err, 'Error'));
                }

                if (errorsFileBase64) {
                    showMessage('Có danh sách lỗi. Đang tải file các dòng lỗi...', 'Info');
                    try {
                        const linkSource = `data:application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;base64,${errorsFileBase64}`;
                        const a = document.createElement('a');
                        a.href = linkSource;
                        a.download = errorsFileName;
                        document.body.appendChild(a);
                        a.click();
                        document.body.removeChild(a);
                    } catch {}
                } else if (errorsFileUrl) {
                    try {
                        const a = document.createElement('a');
                        a.href = errorsFileUrl;
                        a.download = errorsFileName;
                        document.body.appendChild(a);
                        a.click();
                        document.body.removeChild(a);
                    } catch {}
                }

                // Reset form sau khi xử lý
                form.reset();
                fileInput.classList.remove('is-invalid');

                // Chỉ reload khi có tạo mới
                if (createdCount > 0) {
                    setTimeout(() => { window.location.reload(); }, 2000);
                }
            })
            .catch(error => {
                console.error('Import error:', error);
                showMessage('Lỗi khi import file: ' + error.message, 'Error');
            });
        });

        // Xử lý export modal
        document.getElementById('selectAllFields')?.addEventListener('click', function() {
            const checkboxes = document.querySelectorAll('#exportForm input[type="checkbox"]');
            checkboxes.forEach(cb => cb.checked = true);
        });

        document.getElementById('clearAllFields')?.addEventListener('click', function() {
            const checkboxes = document.querySelectorAll('#exportForm input[type="checkbox"]');
            checkboxes.forEach(cb => cb.checked = false);
        });

        // Function to update column order
        function updateColumnOrder() {
            const columnItems = document.querySelectorAll('#sortableColumns .column-item');
            const columnOrder = Array.from(columnItems).map(item => item.getAttribute('data-column'));
            
            // Store column order in hidden input
            let orderInput = document.getElementById('columnOrder');
            if (!orderInput) {
                orderInput = document.createElement('input');
                orderInput.type = 'hidden';
                orderInput.name = 'columnOrder';
                orderInput.id = 'columnOrder';
                document.getElementById('exportForm').appendChild(orderInput);
            }
            orderInput.value = columnOrder.join(',');
        }

        // Validation cho export form
        document.getElementById('exportForm')?.addEventListener('submit', function(e) {
            // Update column order before export
            updateColumnOrder();
            
            const checkboxes = document.querySelectorAll('#exportForm input[type="checkbox"]:checked');
            if (checkboxes.length === 0) {
                e.preventDefault();
                showMessage('Vui lòng chọn ít nhất một trường để export!', 'Warning');
            }
        });

        // Hiển thị thông tin filter trong export modal
        document.getElementById('exportModal')?.addEventListener('show.bs.modal', function() {
            const filterInfo = document.getElementById('filterInfo');
            if (!filterInfo) return;

                const keyword = document.querySelector('input[name="Filter.Keyword"]')?.value;
                const maKhoa = document.querySelector('select[name="Filter.MaKhoa"]')?.value;
                const namSinhMin = document.querySelector('select[name="Filter.NamSinhMin"]')?.value;
                const namSinhMax = document.querySelector('select[name="Filter.NamSinhMax"]')?.value;
                
                const selectedKhoaText = maKhoa ? document.querySelector(`select[name="Filter.MaKhoa"] option[value="${maKhoa}"]`)?.textContent : '';

            let filterText = '';
            if (keyword || maKhoa || namSinhMin || namSinhMax) {
                filterText = '<br/><strong>Bộ lọc đang áp dụng</strong>';
                
                if (keyword) {
                    filterText += '<br/>- Từ khóa: ' + keyword;
                }
                
                if (maKhoa && selectedKhoaText) {
                    filterText += '<br/>- Khoa: ' + selectedKhoaText;
                }
                
                if (namSinhMin || namSinhMax) {
                    filterText += '<br/>- Năm sinh: ';
                    if (namSinhMin && namSinhMax) {
                        filterText += 'từ ' + namSinhMin + ' đến ' + namSinhMax;
                    } else if (namSinhMin) {
                        filterText += 'từ ' + namSinhMin + ' trở lên';
                    } else if (namSinhMax) {
                        filterText += 'đến ' + namSinhMax + ' trở xuống';
                    }
                }
            } else {
                filterText = '<br/><em>Không áp dụng bộ lọc</em>: xuất toàn bộ sinh viên.';
            }
            
            filterInfo.innerHTML = filterText;
        });

            // Initialize Bootstrap Modal
            var modal = new bootstrap.Modal(document.getElementById('sinhVienModal'));

            // Handle Add button click
            document.getElementById('btnAddSinhVien').addEventListener('click', function() {
                document.getElementById('modalTitle').textContent = 'Thêm Sinh viên';
                document.getElementById('btnSaveSinhVien').textContent = 'Lưu';
                document.getElementById('MaSv').value = '';
                resetForm();
                modal.show();
            });

            // Handle Save button click
            document.getElementById('btnSaveSinhVien').addEventListener('click', function() {
                saveSinhVien();
            });

            // Make modal globally accessible
            window.modal = modal;

            // Show server messages (from TempData) as toasts
            try {
                const bridge = document.getElementById('serverMessages');
                if (bridge) {
                    const success = bridge.dataset.success;
                    const warning = bridge.dataset.warning;
                    const error = bridge.dataset.error;
                    if (success) showMessage(success, 'Success');
                    if (warning) showMessage(warning, 'Warning');
                    if (error) showMessage(error, 'Error');
                }
            } catch {}
        });

        // Global function for edit button
        window.editSinhVien = function(maSv) {
            document.getElementById('modalTitle').textContent = 'Sửa Sinh viên';
            document.getElementById('btnSaveSinhVien').textContent = 'Cập nhật';
            
            // Load sinh viên data
            loadSinhVienData(maSv);
            window.modal.show();
        };

        // Function to reset form
        function resetForm() {
            document.getElementById('sinhVienForm').reset();
            // Reset year combobox to default
            document.getElementById('NamSinh').value = '';
            clearValidationErrors();
        }

        // Function to clear validation errors
        function clearValidationErrors() {
            const errorElements = document.querySelectorAll('.invalid-feedback');
            errorElements.forEach(el => el.textContent = '');
            
            const formControls = document.querySelectorAll('.form-control, .form-select');
            formControls.forEach(el => el.classList.remove('is-invalid'));
        }


        // Function to load sinh viên data for editing
        function loadSinhVienData(maSv) {
            fetch(`/SinhVien/GetById/${maSv}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        document.getElementById('MaSv').value = data.data.masv;
                        document.getElementById('MaKhoa').value = data.data.maKhoa || '';
                        document.getElementById('HoTenSv').value = data.data.hotensv || '';
                        document.getElementById('NamSinh').value = data.data.namSinh || '';
                        document.getElementById('QueQuan').value = data.data.queQuan || '';
                    } else {
                        showMessage('Không thể tải dữ liệu sinh viên', 'Error');
                    }
                })
                .catch(error => {
                    console.error('Error loading sinh viên data:', error);
                    showMessage('Có lỗi xảy ra khi tải dữ liệu', 'Error');
                });
        }

        // Function to update DataGrid after save/edit
        function updateDataGridAfterSave(isEdit, savedData) {
            const grid = document.getElementById('SinhVienGrid').ej2_instances[0];
            if (!grid || !savedData) return;
            
            if (isEdit) {
                // Update existing row
                const dataSource = grid.dataSource;
                const index = dataSource.findIndex(item => item.Masv == savedData.masv);
                if (index !== -1) {
                    // Update the row data with correct field names
                    dataSource[index] = {
                        ...dataSource[index],
                        Hotensv: savedData.hotensv,
                        MaKhoa: savedData.maKhoa,
                        NamSinh: savedData.namSinh,
                        QueQuan: savedData.queQuan
                    };
                    grid.refresh();
                }
            } else {
                // Add new row - cần lấy thông tin khoa từ khoa options
                const khoaSelect = document.getElementById('MaKhoa');
                const selectedKhoaText = khoaSelect.options[khoaSelect.selectedIndex]?.text || '';
                
                const newRow = {
                    Masv: savedData.masv,
                    Hotensv: savedData.hotensv,
                    MaKhoa: savedData.maKhoa,
                    TenKhoa: selectedKhoaText,
                    NamSinh: savedData.namSinh,
                    QueQuan: savedData.queQuan
                };
                
                // Thêm vào dataSource
                grid.dataSource.push(newRow);
                grid.refresh();
                
                // Sort lại theo Masv để giữ thứ tự
                grid.sortColumn('Masv', 'Ascending');
            }
        }

        // Function to normalize name (capitalize first letter of each word)
        function normalizeName(name) {
            if (!name || typeof name !== 'string') return '';
            
            return name.trim()
                .split(' ')
                .filter(word => word.length > 0)
                .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
                .join(' ');
        }

        // Function to validate year of birth
        function validateYearOfBirth(year) {
            if (!year || year === '') return 'Vui lòng chọn năm sinh';
            
            const currentYear = new Date().getFullYear();
            const age = currentYear - parseInt(year);
            
            // Validation đã được thực hiện ở combobox (chỉ hiển thị năm hợp lệ)
            // Nhưng vẫn kiểm tra để đảm bảo an toàn
            if (age < 17) return 'Tuổi sinh viên phải từ 17 tuổi trở lên';
            if (age > 100) return 'Tuổi sinh viên không được quá 100 tuổi';
            if (parseInt(year) > currentYear) return 'Năm sinh không được lớn hơn năm hiện tại';
            
            return null;
        }

        // Function to save sinh viên
        function saveSinhVien() {
                clearValidationErrors();
                
                // Chuẩn hóa dữ liệu đầu vào
                const hoTenInput = document.getElementById('HoTenSv');
                const queQuanInput = document.getElementById('QueQuan');
                const namSinhInput = document.getElementById('NamSinh');
                
                if (hoTenInput.value) {
                    hoTenInput.value = normalizeName(hoTenInput.value);
                }
                
                if (queQuanInput.value) {
                    queQuanInput.value = queQuanInput.value.trim();
                }
                
                // Validate year of birth
                const yearError = validateYearOfBirth(namSinhInput.value);
                if (yearError) {
                    namSinhInput.classList.add('is-invalid');
                    const errorElement = document.getElementById('NamSinhError');
                    if (errorElement) {
                        errorElement.textContent = yearError;
                    }
                    showMessage(yearError, 'Error');
                    return;
                }
                
                const formData = new FormData(document.getElementById('sinhVienForm'));
                const maSv = document.getElementById('MaSv').value;
                const isEdit = maSv && maSv !== '';
                
                // For create mode, remove MaSv from form data since it will be auto-generated
                if (!isEdit) {
                    formData.delete('MaSv');
                }
                
                const url = isEdit ? `/SinhVien/Edit/${maSv}` : '/SinhVien/Create';
                const method = 'POST';
                
                // Add anti-forgery token
                const token = document.querySelector('input[name="__RequestVerificationToken"]').value;
                formData.append('__RequestVerificationToken', token);
                
                fetch(url, {
                    method: method,
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        window.modal.hide();
                        const successMessage = isEdit ? 'Cập nhật sinh viên thành công!' : 'Thêm sinh viên thành công!';
                        showMessage(successMessage, 'Success');
                        
                        // Update DataGrid without page reload
                        setTimeout(() => {
                            updateDataGridAfterSave(isEdit, data.data);
                        }, 500);
                    } else {
                        // Handle validation errors
                        if (data.errors) {
                            Object.keys(data.errors).forEach(field => {
                                const errorElement = document.getElementById(field + 'Error');
                                const inputElement = document.getElementById(field);
                                if (errorElement && inputElement) {
                                    errorElement.textContent = data.errors[field][0];
                                    inputElement.classList.add('is-invalid');
                                }
                            });
                        } else {
                            showMessage(data.message || 'Có lỗi xảy ra', 'Error');
                        }
                    }
                })
                .catch(error => {
                    console.error('Error saving sinh viên:', error);
                    showMessage('Có lỗi xảy ra khi lưu dữ liệu', 'Error');
                });
            }
    </script>
}