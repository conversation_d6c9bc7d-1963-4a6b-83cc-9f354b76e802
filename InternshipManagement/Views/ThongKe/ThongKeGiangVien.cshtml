﻿@model InternshipManagement.Models.ViewModels.ThongKeGiangVienVm
@using System.Text.Json

@{
    ViewData["Title"] = "Thống kê Giảng viên";
}

<div class="container py-3">
    <div class="d-flex align-items-center justify-content-between mb-4">
        <h4 class="mb-0 text-primary">
            <i class="fas fa-chart-line me-2"></i>Thống kê cá nhân - @ViewBag.LecturerName
        </h4>
        <div class="text-muted small">
            Khoa: @ViewBag.LecturerDepartment | Kỳ hiện tại: HK @ViewBag.CurrentTerm (@ViewBag.AcademicYear)
        </div>
    </div>

    <!-- Filter Section -->
    <div class="card shadow-sm mb-4">
        <div class="card-header bg-light">
            <h6 class="mb-0 d-flex align-items-center">
                <i class="fas fa-filter me-2 text-primary"></i>
                <PERSON><PERSON> lọc thống kê
            </h6>
        </div>
        <div class="card-body">
            <form method="get" class="row g-3 align-items-end">
                <div class="col-md-6">
                    <label class="form-label small fw-semibold">Học kỳ/Năm học</label>
                    <div class="searchable-dropdown" id="termDropdown">
                        <input type="hidden" name="hocKy" value="@Context.Request.Query["hocKy"]" />
                        <input type="hidden" name="namHocStart" value="@Context.Request.Query["namHocStart"]" />
                        <input type="hidden" name="namHocEnd" value="@Context.Request.Query["namHocEnd"]" />
                        <input type="text" class="form-control form-control-sm" placeholder="HK1 (2025-2026)" autocomplete="off">
                        <i class="bi bi-chevron-down dropdown-arrow"></i>
                        <div class="dropdown-menu">
                            <div class="dropdown-options">
                                <a class="dropdown-item" href="#" data-value="">-- Tất cả --</a>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <label class="form-label small fw-semibold">Đề tài</label>
                    <div class="searchable-dropdown" id="topicDropdown">
                        <input type="hidden" name="maDt" value="@Context.Request.Query["maDt"]" />
                        <input type="text" class="form-control form-control-sm" placeholder="Tìm kiếm đề tài..." autocomplete="off">
                        <i class="bi bi-chevron-down dropdown-arrow"></i>
                        <div class="dropdown-menu">
                            <div class="dropdown-options">
                                <a class="dropdown-item" href="#" data-value="">-- Tất cả --</a>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-12">
                    <div class="d-flex justify-content-end gap-2"> 
                        <button class="btn btn-primary btn-sm px-3 py-2" type="submit">
                            <i class="bi bi-funnel me-1"></i>Lọc
                        </button>
                        <a href="@Url.Action("Index", "ThongKe")" class="btn btn-outline-secondary btn-sm px-3 py-2">
                            <i class="bi bi-arrow-clockwise me-1"></i>Xóa lọc
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Error Message -->
    @if (!string.IsNullOrEmpty(ViewBag.ErrorMessage))
    {
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-triangle me-2"></i>@ViewBag.ErrorMessage
        </div>
    }

    <!-- Summary Cards -->
    <div class="row g-4 mb-4">
        <div class="col-lg-3 col-md-6">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="d-flex align-items-center justify-content-between">
                        <div>
                            <h6 class="card-title text-muted mb-2">Tổng đề tài</h6>
                            <h3 class="text-primary mb-0">@Model.TermSummary.TotalTopics</h3>
                        </div>
                        <div class="bg-primary bg-opacity-10 rounded-circle p-3">
                            <i class="fas fa-tasks text-primary fa-lg"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="d-flex align-items-center justify-content-between">
                        <div>
                            <h6 class="card-title text-muted mb-2">Sinh viên đăng ký</h6>
                            <h3 class="text-info mb-0">@Model.TermSummary.TotalStudents</h3>
                        </div>
                        <div class="bg-info bg-opacity-10 rounded-circle p-3">
                            <i class="fas fa-users text-info fa-lg"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="d-flex align-items-center justify-content-between">
                        <div>
                            <h6 class="card-title text-muted mb-2">Đã hoàn thành</h6>
                            <h3 class="text-success mb-0">@Model.TermSummary.CompletedStudents</h3>
                        </div>
                        <div class="bg-success bg-opacity-10 rounded-circle p-3">
                            <i class="fas fa-check-circle text-success fa-lg"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="d-flex align-items-center justify-content-between">
                        <div>
                            <h6 class="card-title text-muted mb-2">Điểm TB</h6>
                            <h3 class="text-warning mb-0">
                                @if (Model.TermSummary.AverageScore.HasValue)
                                {
                                    @String.Format("{0:F1}", Model.TermSummary.AverageScore.Value)
                                }
                                else
                                {
                                    <span class="text-muted">--</span>
                                }
                            </h3>
                        </div>
                        <div class="bg-warning bg-opacity-10 rounded-circle p-3">
                            <i class="fas fa-star text-warning fa-lg"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Registration Status Chart & Slot Usage -->
    <div class="row g-4 mb-4">
        <div class="col-lg-8">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-light">
                    <h6 class="mb-0">
                        <i class="fas fa-chart-pie me-2 text-primary"></i>
                        Thống kê tình hình đăng ký hướng dẫn
                    </h6>
                </div>
                <div class="card-body">
                    @if (Model.RegistrationStats.Pending + Model.RegistrationStats.Accepted + Model.RegistrationStats.InProgress + Model.RegistrationStats.Completed + Model.RegistrationStats.Rejected + Model.RegistrationStats.Withdrawn > 0)
                    {
                        <div id="registrationStatusChart" style="width: 100%; height: 350px;"></div>
                        
                        <script>
                            document.addEventListener('DOMContentLoaded', function() {
                                const registrationData = @Html.Raw(Json.Serialize(new[] {
                                    new { name = "Đang chờ duyệt", value = Model.RegistrationStats.Pending },
                                    new { name = "Đã chấp nhận", value = Model.RegistrationStats.Accepted },
                                    new { name = "Đang thực hiện", value = Model.RegistrationStats.InProgress },
                                    new { name = "Đã hoàn thành", value = Model.RegistrationStats.Completed },
                                    new { name = "Đã từ chối", value = Model.RegistrationStats.Rejected },
                                    new { name = "Đã rút đăng ký", value = Model.RegistrationStats.Withdrawn }
                                }.Where(x => x.value > 0)));

                                const chartDom = document.getElementById('registrationStatusChart');
                                const myChart = echarts.init(chartDom);
                                
                                const colors = [
                                    '#ffc107',  // Đang chờ duyệt - vàng
                                    '#17a2b8',  // Đã chấp nhận - cyan
                                    '#007bff',  // Đang thực hiện - xanh dương
                                    '#28a745',  // Đã hoàn thành - xanh lá
                                    '#dc3545',  // Đã từ chối - đỏ
                                    '#6c757d'   // Đã rút đăng ký - xám
                                ];

                                const option = {
                                    title: {
                                        text: 'Tình hình đăng ký',
                                        textStyle: {
                                            fontSize: 16,
                                            fontWeight: 'bold'
                                        },
                                        top: 10,
                                        left: 'center'
                                    },
                                    tooltip: {
                                        trigger: 'item',
                                        formatter: function(params) {
                                            return `<strong>${params.name}</strong><br/>
                                                    Số lượng: ${params.value}<br/>
                                                    Tỷ lệ: ${params.percent}%`;
                                        }
                                    },
                                    legend: {
                                        show: false
                                    },
                                    series: [{
                                        name: 'Trạng thái',
                                        type: 'pie',
                                        radius: ['25%', '60%'],
                                        center: ['50%', '50%'],
                                        data: registrationData,
                                        emphasis: {
                                            itemStyle: {
                                                shadowBlur: 10,
                                                shadowOffsetX: 0,
                                                shadowColor: 'rgba(0, 0, 0, 0.5)'
                                            }
                                        },
                                        itemStyle: {
                                            borderRadius: 8,
                                            borderColor: '#fff',
                                            borderWidth: 2
                                        },
                                        label: {
                                            show: true,
                                            formatter: function(params) {
                                                return `${params.name}\n${params.percent}%`;
                                            },
                                            fontSize: 11,
                                            fontWeight: 'bold',
                                            lineHeight: 16
                                        },
                                        labelLine: {
                                            show: true,
                                            length: 15,
                                            length2: 10
                                        }
                                    }],
                                    color: colors
                                };

                                myChart.setOption(option);
                                
                                // Responsive
                                window.addEventListener('resize', function () {
                                    myChart.resize();
                                });
                            });
                        </script>
                    }
                    else
                    {
                        <div class="text-center py-5">
                            <i class="fas fa-chart-pie text-muted" style="font-size: 3rem; opacity: 0.3;"></i>
                            <p class="text-muted mt-3">Chưa có dữ liệu đăng ký</p>
                        </div>
                    }
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-light">
                    <h6 class="mb-0">
                        <i class="fas fa-chart-line me-2 text-primary"></i>
                        Số lượt đề tài đã sử dụng
                    </h6>
                </div>
                <div class="card-body">
                    <div id="slotUsageChart" style="width: 100%; height: 300px;"></div>
                    
                    <script>
                        document.addEventListener('DOMContentLoaded', function() {
                            const slotData = @Html.Raw(Json.Serialize(new[] {
                                new { name = "Đã sử dụng", value = Model.SlotUsage.UsedSlots },
                                new { name = "Còn lại", value = Model.SlotUsage.RemainingSlots }
                            }));

                            const chartDom = document.getElementById('slotUsageChart');
                            const myChart = echarts.init(chartDom);
                            
                            const option = {
                                title: {
                                    text: `${@Model.SlotUsage.UsagePercentage}%`,
                                    subtext: 'Đã sử dụng',
                                    left: 'center',
                                    top: 'center',
                                    textStyle: {
                                        fontSize: 24,
                                        fontWeight: 'bold',
                                        color: '#007bff'
                                    },
                                    subtextStyle: {
                                        fontSize: 12,
                                        color: '#666'
                                    }
                                },
                                tooltip: {
                                    trigger: 'item',
                                    formatter: function(params) {
                                        return `<strong>${params.name}</strong><br/>Số slot: ${params.value}`;
                                    }
                                },
                                legend: {
                                    orient: 'horizontal',
                                    bottom: 10,
                                    textStyle: {
                                        fontSize: 11
                                    }
                                },
                                series: [{
                                    name: 'Slot usage',
                                    type: 'pie',
                                    radius: ['50%', '80%'],
                                    center: ['50%', '50%'],
                                    data: slotData,
                                    itemStyle: {
                                        borderRadius: 10,
                                        borderColor: '#fff',
                                        borderWidth: 3
                                    },
                                    label: {
                                        show: false
                                    },
                                    emphasis: {
                                        scale: false,
                                        itemStyle: {
                                            shadowBlur: 10,
                                            shadowOffsetX: 0,
                                            shadowColor: 'rgba(0, 0, 0, 0.5)'
                                        }
                                    }
                                }],
                                color: ['#007bff', '#e9ecef']
                            };

                            myChart.setOption(option);
                            
                            // Responsive
                            window.addEventListener('resize', function () {
                                myChart.resize();
                            });
                        });
                    </script>
                    
                    <div class="text-center mt-3">
                        <small class="text-muted">
                            @Model.SlotUsage.UsedSlots / @Model.SlotUsage.TotalSlots slots
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Topic Scores Chart -->
    @if (Model.TopicScores.Any())
    {
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-light">
                <h6 class="mb-0">
                    <i class="fas fa-star me-2 text-primary"></i>
                    Thống kê điểm số các đề tài
                </h6>
            </div>
            <div class="card-body">
                <div id="topicScoresChart" style="width: 100%; height: 400px;"></div>
                
                <script>
                    document.addEventListener('DOMContentLoaded', function() {
                        const topicData = @Html.Raw(Json.Serialize(Model.TopicScores.Select(t => new {
                            maDt = t.MaDt,
                            tenDt = t.TenDt.Length > 30 ? t.TenDt.Substring(0, 30) + "..." : t.TenDt,
                            diemTB = t.DiemTrungBinh ?? 0,
                            soSV = t.SoSinhVienHoanThanh,
                            slotMax = t.SlotToiDa,
                            slotConLai = t.SlotConLai
                        })));

                        const chartDom = document.getElementById('topicScoresChart');
                        const myChart = echarts.init(chartDom);
                        
                        const categories = topicData.map(x => x.maDt);
                        const scores = topicData.map(x => x.diemTB);

                        const option = {
                            title: {
                                text: 'Điểm trung bình các đề tài',
                                textStyle: {
                                    fontSize: 16,
                                    fontWeight: 'bold'
                                },
                                top: 10,
                                left: 'center'
                            },
                            tooltip: {
                                trigger: 'axis',
                                axisPointer: {
                                    type: 'shadow'
                                },
                                formatter: function(params) {
                                    const dataIndex = params[0].dataIndex;
                                    const item = topicData[dataIndex];
                                    return `
                                        <strong>Mã ĐT:</strong> ${item.maDt}<br/>
                                        <strong>Tên:</strong> ${item.tenDt}<br/>
                                        <strong>Điểm TB:</strong> ${item.diemTB.toFixed(2)}<br/>
                                        <strong>SV hoàn thành:</strong> ${item.soSV}<br/>
                                        <strong>Slot còn lại:</strong> ${item.slotConLai}/${item.slotMax}
                                    `;
                                }
                            },
                            grid: {
                                left: '3%',
                                right: '4%',
                                bottom: '20%',
                                top: '15%',
                                containLabel: true
                            },
                            xAxis: {
                                type: 'category',
                                data: categories,
                                name: 'Đề tài',
                                nameLocation: 'middle',
                                nameGap: 30,
                                axisLabel: {
                                    interval: 0,
                                    rotate: 45,
                                    fontSize: 10
                                }
                            },
                            yAxis: {
                                type: 'value',
                                name: 'Điểm trung bình',
                                nameLocation: 'middle',
                                nameGap: 40,
                                min: 0,
                                max: 10,
                                interval: 2
                            },
                            series: [{
                                name: 'Điểm TB',
                                type: 'bar',
                                data: scores,
                                itemStyle: {
                                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                        { offset: 0, color: '#4CAF50' },
                                        { offset: 1, color: '#2E7D32' }
                                    ]),
                                    borderRadius: [4, 4, 0, 0]
                                },
                                emphasis: {
                                    itemStyle: {
                                        shadowBlur: 10,
                                        shadowColor: 'rgba(76, 175, 80, 0.5)'
                                    }
                                },
                                label: {
                                    show: true,
                                    position: 'top',
                                    formatter: function(params) {
                                        return params.value > 0 ? params.value.toFixed(1) : '';
                                    },
                                    fontSize: 11,
                                    fontWeight: 'bold'
                                }
                            }]
                        };

                        myChart.setOption(option);
                        
                        // Responsive
                        window.addEventListener('resize', function () {
                            myChart.resize();
                        });
                    });
                </script>
            </div>
        </div>
    }
</div>

@section Scripts {
    <link href="~/css/searchable-dropdown.css" rel="stylesheet" />
    <script src="~/js/searchable-dropdown.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            // Initialize Term dropdown
            const termDropdown = new SearchableDropdown('termDropdown', {
                placeholder: 'Học kỳ',
                searchUrl: '@Url.Action("SuggestTerms", "ThongKe")',
                searchParam: 'q',
                debounceDelay: 300,
                allowClear: true,
                labelFormatter: function (item) {
                    const t = item.term ?? item.Term ?? item.termNumber;
                    const ys = item.yearStart ?? item.YearStart ?? item.namHocStart;
                    const ye = item.yearEnd ?? item.YearEnd ?? item.namHocEnd;
                    const disp = item.display ?? item.Display ?? `HK${t} (${ys}-${ye})`;
                    if (t && ys && ye) return { value: `${t}|${ys}|${ye}`, text: disp };
                    return null;
                }
            });

            // Initialize Topic dropdown
            const topicDropdown = new SearchableDropdown('topicDropdown', {
                placeholder: 'Tìm kiếm đề tài...',
                searchUrl: '@Url.Action("GetLecturerTopics", "ThongKe")',
                searchParam: 'q',
                filterParam: 'termFilter',
                debounceDelay: 300,
                allowClear: true,
                labelFormatter: function (item) {
                    const maDt = item.MaDt ?? item.maDt;
                    const display = item.Display ?? item.display ?? `${maDt} - ${item.TenDt ?? item.tenDt}`;
                    if (maDt) return { value: maDt, text: display };
                    return null;
                }
            });

            // Map term selection to hidden inputs
            document.getElementById('termDropdown').addEventListener('selectionChanged', function (e) {
                const hiddenHocKy = document.querySelector('#termDropdown input[name="hocKy"]');
                const hiddenStart = document.querySelector('#termDropdown input[name="namHocStart"]');
                const hiddenEnd = document.querySelector('#termDropdown input[name="namHocEnd"]');
                const raw = e.detail?.value || '';

                if (!raw) {
                    hiddenHocKy.value = '';
                    hiddenStart.value = '';
                    hiddenEnd.value = '';
                    topicDropdown.setFilterValue('');
                    return;
                }

                const parts = raw.split('|');
                if (parts.length === 3) {
                    hiddenHocKy.value = parts[0];
                    hiddenStart.value = parts[1];
                    hiddenEnd.value = parts[2];
                    
                    // Update topic dropdown filter
                    topicDropdown.setFilterValue(`${parts[0]}|${parts[1]}|${parts[2]}`);
                }
            });

            // Initialize dropdown with current values
            const currentHocKy = '@Context.Request.Query["hocKy"]';
            const currentStart = '@Context.Request.Query["namHocStart"]';
            const currentEnd = '@Context.Request.Query["namHocEnd"]';
            
            if (currentHocKy && currentStart && currentEnd) {
                setTimeout(function() {
                    const termValue = `${currentHocKy}|${currentStart}|${currentEnd}`;
                    const termText = `HK${currentHocKy} (${currentStart}-${currentEnd})`;
                    
                    termDropdown.setValue(termValue, termText);
                    
                    // Set hidden inputs
                    const hiddenHocKy = document.querySelector('#termDropdown input[name="hocKy"]');
                    const hiddenStart = document.querySelector('#termDropdown input[name="namHocStart"]');
                    const hiddenEnd = document.querySelector('#termDropdown input[name="namHocEnd"]');
                    
                    if (hiddenHocKy) hiddenHocKy.value = currentHocKy;
                    if (hiddenStart) hiddenStart.value = currentStart;
                    if (hiddenEnd) hiddenEnd.value = currentEnd;
                    
                    // Set topic filter
                    topicDropdown.setFilterValue(termValue);
                }, 100);
            }
        });
    </script>
}
