﻿using InternshipManagement.Models;
using InternshipManagement.Models.ViewModels;

namespace InternshipManagement.Repositories.Interfaces
{
    public interface ISinhVienRepository
    {
        Task<List<SinhVienListItemVm>> SearchAsync(SinhVienFilterVm filter);
        Task<SinhVienListItemVm?> GetByIdAsync(int maSv);

        Task<SinhVien?> GetEntityAsync(int id);  
        Task CreateAsync(SinhVien entity);     
        Task UpdateAsync(SinhVien entity);
        Task<StudentCurrentTopicVm?> GetCurrentTopicByStudentAsync(int maSv);
        Task DeleteAsync(int id);
        Task<List<SinhVienExportRowVm>> GetForExportAsync(SinhVienFilterVm filter);           
    }
}
