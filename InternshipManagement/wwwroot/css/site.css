﻿/* ============ TOKENS ============ */
:root {
    --primary: #3338A0; /* màu chủ đạo */
    --bs-primary-rgb: 51, 56, 160;
    --primary-contrast: #fff;
    --success: #168F5A;
    --border: #E5E7EB;
    --bg: #F5F7FB;
    --bg-soft: #F8FAFC;
    --r-sm: 8px;
    --r-md: 12px;
    --sh-sm: 0 2px 8px rgba(0,0,0,.06);
    --sh-md: 0 8px 20px rgba(0,0,0,.08);
}

/* ============ GLOBAL ============ */
body {
    background: var(--bg);
    color: #101827; /* text mặc định */
}

/* link */
a {
    color: var(--primary);
    text-decoration: none;
}

    a:hover {
        text-decoration: underline;
    }

/* ============ NAVBAR ============ */
.navbar {
    background: var(--primary) !important;
}

    .navbar .nav-link {
        color: var(--primary-contrast) !important;
        opacity: .9;
    }

        .navbar .nav-link:hover {
            opacity: 1;
        }

/* ============ BUTTONS ============ */
.btn {
    border-radius: var(--r-sm);
    font-weight: 600;
}

.btn-primary {
    background: var(--primary);
    border-color: var(--primary);
    color: var(--primary-contrast);
}

    .btn-primary:hover {
        filter: brightness(.97);
    }

.btn-success {
    background: var(--success);
    border-color: var(--success);
}

/* ============ FILTER CARD ============ */
.filter-card {
    background: #fff;
    border-radius: var(--r-md);
    box-shadow: var(--sh-sm);
    padding: 16px;
}

.filter-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 12px 16px;
    align-items: flex-end;
}

@media (min-width: 992px) {
    .filter-grid {
        flex-wrap: nowrap;
    }
}

/* Đảm bảo filter actions không bị overflow */
@media (max-width: 991px) {
    .filter-grid {
        flex-direction: column;
        align-items: stretch;
    }
    
    .filter-actions {
        margin-top: 12px;
        justify-content: center;
    }
}

.fcol {
    min-width: 180px;
}

.fcol--xs {
    min-width: 120px;
}

.fcol--grow {
    flex: 1 1 240px;
}

.form-label {
    font-size: .85rem;
    font-weight: 600;
    color: #6B7280;
}

.form-control, .form-select {
    height: 44px;
    border: 1px solid var(--border);
    border-radius: var(--r-sm);
    box-shadow: none;
}

    .form-control:focus, .form-select:focus {
        border-color: var(--primary);
        box-shadow: 0 0 0 .15rem rgba(51,56,160,.25);
    }

::placeholder {
    color: #9CA3AF;
}

.filter-actions {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    align-items: center;
    justify-content: flex-start;
}

    .filter-actions .btn {
        height: 44px;
        padding: 0 16px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;
        min-width: fit-content;
    }

/* Responsive cho filter actions */
@media (max-width: 768px) {
    .filter-actions {
        flex-direction: column;
        align-items: stretch;
        width: 100%;
    }
    
    .filter-actions .btn {
        width: 100%;
        justify-content: center;
    }
}

@media (min-width: 769px) and (max-width: 991px) {
    .filter-actions {
        flex-wrap: wrap;
        justify-content: flex-start;
    }
}

/* ============ TABLES ============ */
.table-wrap {
    background: #fff;
    border-radius: var(--r-md);
    box-shadow: var(--sh-sm);
    overflow: hidden;
}

.table thead th {
    background: rgba(51,56,160,.06);
    font-weight: 700;
}

.table tbody tr:hover {
    background: #FAFBFF;
}

/* ============ UTILITY CLASSES ============ */
.btn-group-responsive {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    align-items: center;
}

.btn-group-responsive .btn {
    flex-shrink: 0;
    white-space: nowrap;
}

@media (max-width: 576px) {
    .btn-group-responsive {
        flex-direction: column;
        width: 100%;
    }
    
    .btn-group-responsive .btn {
        width: 100%;
    }
}

/* Overflow handling */
.overflow-wrap {
    overflow-wrap: break-word;
    word-wrap: break-word;
    hyphens: auto;
}

/* ============ HERO (tùy chọn) ============ */
.hero {
    background: linear-gradient(135deg,#3338A0 0%,#4b50c8 100%);
    color: #fff;
    border-radius: var(--r-md);
    box-shadow: var(--sh-md);
}
